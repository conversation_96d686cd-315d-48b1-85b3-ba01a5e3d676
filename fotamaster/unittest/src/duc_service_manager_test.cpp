#include <chrono>
#include <condition_variable>
#include <cstdlib>
#include <functional>
#include <iostream>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/eventloop_manager.h"
#include "logger/logger.h"
#include "progress_fitting/download_progress_fitting.h"
#include "progress_fitting/upgrade_progress_fitting.h"
#include <atomic>
#include <chrono>
#include <iostream>
#include <mutex>
#include <string>
#include <sys/ioctl.h>
#include <termios.h>
#include <thread>
#include <unistd.h>

using namespace std;
using namespace seres::fotamaster;
using namespace seres::ota_duc_service;

class BottomProgressBar : public base::Noncopyable
{
private:
    struct winsize terminalSize;
    struct termios originalTermios;
    int reservedLines = 1; // 底部保留行数，增加一行作为缓冲

    std::atomic<uint8_t> currentProgress{0};
    std::atomic<bool> finished{false};
    std::atomic<bool> shouldStop{false};
    std::string statusText;
    std::mutex statusMutex;
    std::thread refreshThread;

    // 刷新间隔（毫秒）
    static constexpr int REFRESH_INTERVAL_MS = 100;

public:
    BottomProgressBar()
    {
        // 获取终端大小
        ioctl(STDOUT_FILENO, TIOCGWINSZ, &terminalSize);

        // 保存原始终端设置
        tcgetattr(STDIN_FILENO, &originalTermios);

        // 隐藏光标，避免闪烁
        std::cout << "\033[?25l";

        // 设置滚动区域，为进度条预留底部空间
        // 限制滚动区域为第1行到倒数第3行
        std::cout << "\033[1;" << (terminalSize.ws_row - reservedLines) << "r";

        // 初始化底部区域
        clearBottomArea();

        // 移动光标回到滚动区域的最后一行
        std::cout << "\033[" << (terminalSize.ws_row - reservedLines) << ";1H";
        std::cout.flush();

        // 启动刷新线程
        refreshThread = std::thread(&BottomProgressBar::refreshLoop, this);
    }

    ~BottomProgressBar()
    {
        cleanup();
    }

    void cleanup()
    {
        // 停止刷新线程
        shouldStop = true;
        if (refreshThread.joinable())
        {
            refreshThread.join();
        }

        // 显示光标
        std::cout << "\033[?25h";

        // 恢复滚动区域到全屏
        std::cout << "\033[1;" << terminalSize.ws_row << "r";

        // 清除底部进度条区域
        clearBottomArea();

        // 移动光标到正常位置
        std::cout << "\033[" << terminalSize.ws_row << ";1H";
        std::cout.flush();
    }

    void updateProgress(uint8_t progress, const std::string &status = "")
    {
        currentProgress = progress;

        {
            std::lock_guard<std::mutex> lock(statusMutex);
            if (!status.empty())
            {
                statusText = status;
            }
        }

        if (progress >= 100)
        {
            // 等待一小段时间让用户看到完成状态
            std::this_thread::sleep_for(std::chrono::milliseconds(1500));
            finished = true;
        }
    }

    void setStatus(const std::string &status)
    {
        std::lock_guard<std::mutex> lock(statusMutex);
        statusText = status;
    }

private:
    void clearBottomArea()
    {
        // 清除底部的保留行
        for (int i = 0; i < reservedLines; ++i)
        {
            std::cout << "\033["
                      << (terminalSize.ws_row - reservedLines + 1 + i) << ";1H";
            std::cout << "\033[2K"; // 清除整行
        }
    }

    void refreshLoop()
    {
        while (!shouldStop)
        {
            drawProgressBar();
            std::this_thread::sleep_for(
                std::chrono::milliseconds(REFRESH_INTERVAL_MS));

            if (finished)
            {
                // 显示完成状态一段时间后退出
                std::this_thread::sleep_for(std::chrono::milliseconds(1000));
                break;
            }
        }
    }

    void drawProgressBar()
    {
        // 保存当前光标位置
        std::cout << "\033[s";

        // 移动到底部进度条位置
        int progressRow = terminalSize.ws_row - 1;
        std::cout << "\033[" << progressRow << ";1H";

        // 清除当前行
        std::cout << "\033[2K";

        uint8_t progress = currentProgress;

        // 获取状态文本
        std::string currentStatus;
        {
            std::lock_guard<std::mutex> lock(statusMutex);
            currentStatus = statusText;
        }

        // 计算进度条长度
        int statusLength = currentStatus.length();
        int percentLength = 5;              // " 100%"
        int bracketLength = 2;              // "[]"
        int checkLength = finished ? 4 : 0; // " ✓ 完成" 或者空

        int availableWidth = terminalSize.ws_col - statusLength -
                             percentLength - bracketLength - checkLength -
                             2; // 留2个空格
        if (availableWidth < 10)
            availableWidth = 10; // 最小宽度

        int filledWidth = static_cast<int>(
            (static_cast<double>(progress) / 100.0) * availableWidth);

        // 创建进度条
        std::string bar = "[";
        for (int i = 0; i < availableWidth; ++i)
        {
            if (i < filledWidth)
            {
                bar += "█"; // 实心块
            }
            else if (i == filledWidth && progress < 100)
            {
                // 根据精确进度显示不同的部分填充字符
                double exactProgress =
                    (static_cast<double>(progress) / 100.0) * availableWidth;
                double fraction = exactProgress - filledWidth;
                if (fraction > 0.75)
                    bar += "▉";
                else if (fraction > 0.5)
                    bar += "▊";
                else if (fraction > 0.25)
                    bar += "▋";
                else
                    bar += "▌";
            }
            else
            {
                bar += "░"; // 空心块
            }
        }
        bar += "] " + std::to_string(progress) + "%";

        // 添加状态文本
        if (!currentStatus.empty())
        {
            bar = currentStatus + " " + bar;
        }

        // 添加完成标志
        if (finished)
        {
            bar += " ✓ 完成";
        }

        // 输出进度条
        std::cout << bar;

        // 恢复光标位置
        std::cout << "\033[u";
        std::cout.flush();
    }
};

void waitForEnterAndClearScreen()
{
    cout << "\nPress Enter to continue...";
    cin.ignore(numeric_limits<streamsize>::max(), '\n'); // Wait for Enter
    // Clear the screen (works on most terminals)
    cout << "\033[2J\033[1;1H"; // ANSI escape code for clear screen
}

void clearScreen()
{
    cout << "\033[2J\033[1;1H"; // ANSI escape code for clear screen
}

void displayTestMenu()
{
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    clearScreen();
    cout << "\n=== DUC Service Manager Test Menu ===" << endl;
    cout << "Please select a test to run:" << endl;
    cout << "1. Inventory Collection Test" << endl;
    cout << "2. Download Test" << endl;
    cout << "3. Unzip Test" << endl;
    cout << "4. Package Verification Test" << endl;
    cout << "5. Update Condition Check Test" << endl;
    cout << "6. Update Test (Enhanced Multi-Domain)" << endl;
    cout << "7. Rollback Test" << endl;
    cout << "8. Activation Test" << endl;
    cout << "9. Log Upload Test" << endl;
    cout << "10. Run All Tests" << endl;
    cout << "0. Exit" << endl;
    cout << "Enter your choice (0-10): ";
}

int getUserChoice()
{
    int choice;
    while (!(cin >> choice) || choice < 0 || choice > 10)
    {
        cout << "Invalid input. Please enter a number between 0 and 10: ";
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
    }
    cin.ignore(numeric_limits<streamsize>::max(),
               '\n'); // Clear the input buffer
    return choice;
}

// Topic callback functions
void onInventoryResult(const InventoryResult &result)
{
    LOG_INFO("Callback::::::::::[Received inventory result:");
    for (const auto &info : result.InventoryLists())
    {
        LOG_INFO("  ECU: %s, Version: %s",
                 info.ecuName().c_str(),
                 info.softwareVersion().c_str());
    }
}

void onDownloadProgress(const DownloadProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received download progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Package name: %s, Progress: %d%%, Status: %d",
                 info.packageName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status().status()));
    }
}

void onUzipResult(const CommonStatus &result)
{
    LOG_INFO("Callback::::::::::[Received unzip result: Success=%d, Error=%d",
             static_cast<int>(result.status()),
             static_cast<int>(result.reason()));
}

void onVerifyResult(const CommonStatus &result)
{
    LOG_INFO(
        "Callback::::::::::[Received verification result: Success=%d, Error=%d",
        static_cast<int>(result.status()),
        static_cast<int>(result.reason()));
}

void onCheckConditionResult(const CommonStatus &result)
{
    LOG_INFO("Callback::::::::::[Received check condition result: Passed=%d, "
             "Error code=%d",
             static_cast<int>(result.status()),
             static_cast<int>(result.reason()));
}

void onUpdateProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received update progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status().status()));
    }
}

void onRollbackProgress(const UpdateProgress &progress)
{
    LOG_INFO("Callback::::::::::[Received rollback progress:");
    for (const auto &info : progress.progressLists())
    {
        LOG_INFO("  Device: %s, Progress: %d%%, Status: %d",
                 info.deviceName().c_str(),
                 info.progressPercent(),
                 static_cast<int>(info.status().status()));
    }
}

// Service status callback
void onServiceStatus(DUCType type, bool connected)
{
    LOG_INFO("Callback::::::::::[Service status changed: Type=%s, Connected=%d",
             ducTypeToString(type).c_str(),
             connected);
}

// Test function declarations
bool testInventoryCollection(DUCServiceManager &manager);
bool testDownload(DUCServiceManager &manager);
bool testUnzip(DUCServiceManager &manager);
bool testPackageVerification(DUCServiceManager &manager);
bool testUpdateConditionCheck(DUCServiceManager &manager);
bool testUpdate(DUCServiceManager &manager);
bool testRollback(DUCServiceManager &manager);
bool testActivation(DUCServiceManager &manager);
bool testLogUpload(DUCServiceManager &manager);
bool runAllTests(DUCServiceManager &manager);
bool runSelectedTest(int choice, DUCServiceManager &manager);

// Helper function to create devices for different domains
std::vector<string> createDevicesForDomain(DUCType ducType)
{
    std::vector<string> devices;
    std::string domainName = ducTypeToString(ducType);

    // Different number of devices for each domain
    int deviceCount = 0;
    std::string devicePrefix = "";

    switch (ducType)
    {
    case DUCType::CDC:
        deviceCount = 20;
        devicePrefix = "CDC_ECU";
        break;
    case DUCType::MDC:
        deviceCount = 20;
        devicePrefix = "MDC_ECU";
        break;
    case DUCType::ZCU:
        deviceCount = 10;
        devicePrefix = "ZCU_ECU";
        break;
    default:
        deviceCount = 1;
        devicePrefix = "UNKNOWN_ECU";
        break;
    }

    for (int i = 0; i < deviceCount; i++)
    {
        std::string device =
            (domainName + "_Test_Device_" + std::to_string(i + 1));
        devices.push_back(device);
    }

    return devices;
}

// Test function implementations
bool testInventoryCollection(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Inventory Collection Test ===");

    SelectedInventoryList inventoryList;
    inventoryList.inventoryLists().push_back("123123");

    if (!manager.subscribeInventoryResult(DUCType::CDC, onInventoryResult))
    {
        LOG_ERROR("Failed to subscribe to inventory result topic");
        return false;
    }

    ReturnCode ret = manager.inventoryCollection(DUCType::CDC, inventoryList);
    LOG_INFO("inventoryCollection returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(4));

    return ret == ReturnCode::OK;
}

bool testDownload(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Download Test ===");
    // Test each domain with different devices
    std::vector<DUCType> domains = {DUCType::CDC, DUCType::MDC, DUCType::ZCU};

    for (DUCType domain : domains)
    {
        DownloadConditionLists downloadConditions;
        CommonStatus cond_result;
        auto ret = manager.checkDownloadCondition(domain,
                                                  downloadConditions,
                                                  cond_result);
        LOG_INFO("checkDownloadCondition returned: %s",
                 returnCodeToString(ret).c_str());
        if (ret != ReturnCode::OK)
        {
            LOG_ERROR("checkDownloadCondition failed");
            return false;
        }
    }
    auto &d_progres_fit = base::Singleton<DownloadProgressManager>::Instance();
    BottomProgressBar &bottomProgressBar =
        base::Singleton<BottomProgressBar>::Instance();
    d_progres_fit.Initialize(
        [&](uint8_t progress,
            uint64_t allPakageSize,
            float speed,
            uint32_t remainingTime) {
            LOG_INFO(
                "Download progress: %d%%, Speed: %f KB/s, Remaining Time: %d "
                "seconds",
                progress,
                speed,
                remainingTime);
            bottomProgressBar.updateProgress(progress, "downloading");
        },
        1,
        1);
    for (DUCType domain : domains)
    {
        DownloadTaskLists tasks;
        DownloadTaskInfo task;
        task.taskId(ducTypeToString(domain) + "_TASK001");
        task.packageVersion("1.0.0");
        task.packageName(ducTypeToString(domain) + "_test_package");
        task.packageUrl("http://test.com/package_" + ducTypeToString(domain));
        task.packageSize("102400000");
        task.packageMd5("2374891273123648");
        tasks.taskLists().push_back(task);
        d_progres_fit.AddDownloadPackage(domain, tasks.taskLists());
        // if (!manager.subscribeDownloadProgress(domain, onDownloadProgress))
        // {
        //     LOG_ERROR("Failed to subscribe to download progress topic");
        //     return false;
        // }
        auto ret = manager.startDownload(domain, tasks);
        LOG_INFO("startDownload returned: %s", returnCodeToString(ret).c_str());
    }

    base::Singleton<EventLoopManager>::Instance()
        .GetDefaultLoop()
        ->RunInLoopThread([&] { d_progres_fit.Start(); });

    std::this_thread::sleep_for(std::chrono::seconds(12));
    return true;
}

bool testUnzip(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Unzip Test ===");

    if (!manager.subscribeUzipPackagesResult(DUCType::CDC, onUzipResult))
    {
        LOG_ERROR("Failed to subscribe to unzip result topic");
        return false;
    }

    ReturnCode ret = manager.uzipPackages(DUCType::CDC);
    LOG_INFO("uzipPackages returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    CommonStatus uzipResult;
    ret = manager.getuzipPackagesResult(DUCType::CDC, uzipResult);
    LOG_INFO("getuzipPackagesResult returned: %s",
             returnCodeToString(ret).c_str());

    return ret == ReturnCode::OK;
}

bool testPackageVerification(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Package Verification Test ===");

    if (!manager.subscribePackagesVerifyResult(DUCType::CDC, onVerifyResult))
    {
        LOG_ERROR("Failed to subscribe to verification result topic");
        return false;
    }

    ReturnCode ret = manager.startPackagesVerify(DUCType::CDC);
    LOG_INFO("startPackagesVerify returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    CommonStatus verifyResult;
    ret = manager.getPackagesVerifyResult(DUCType::CDC, verifyResult);
    LOG_INFO("getPackagesVerifyResult successed? : %d",
             static_cast<int>(verifyResult.status()));

    return ret == ReturnCode::OK;
}

bool testUpdateConditionCheck(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Update Condition Check Test ===");

    if (!manager.subscribeCheckUpdateConditionResult(DUCType::CDC,
                                                     onCheckConditionResult))
    {
        LOG_ERROR("Failed to subscribe to check condition result topic");
        return false;
    }

    ReturnCode ret = manager.checkUpdateCondition(DUCType::CDC);
    LOG_INFO("checkUpdateCondition returned: %s",
             returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(5));

    CommonStatus checkResult;
    ret = manager.getCheckUpdateConditionResult(DUCType::CDC, checkResult);
    LOG_INFO("getCheckUpdateConditionResult returned: %s",
             returnCodeToString(ret).c_str());

    return ret == ReturnCode::OK;
}

bool testUpdate(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Enhanced Update Test ===");

    // Initialize ProgressFitting
    UpgradeProgressManager &progressFit =
        base::Singleton<UpgradeProgressManager>::Instance();
    BottomProgressBar &bottomProgressBar =
        base::Singleton<BottomProgressBar>::Instance();
    progressFit.Initialize(
        [&](uint8_t progress,
            bool allFinished,
            std::vector<EachUpdateProgressInfo> eachProgress) {
            //输出进度条
            bottomProgressBar.updateProgress(progress,
                                             allFinished ? "finished"
                                                         : "updating");
            for (auto &info : eachProgress)
            {
                LOG_DEBUG("Device %s progress: %d%%",
                          info.DeviceId.c_str(),
                          info.currentProgress);
            }
            if (allFinished)
            {
                progressFit.Reset();
                progressFit.Stop();
            }
        },
        0.5,
        true);
    bool allDomainsSuccess = true;

    // Test each domain with different devices
    std::vector<DUCType> domains = {DUCType::CDC, DUCType::MDC, DUCType::ZCU};

    for (DUCType domain : domains)
    {
        LOG_INFO("\n--- Testing %s Domain ---",
                 ducTypeToString(domain).c_str());

        // Create devices for this domain
        std::vector<string> devices = createDevicesForDomain(domain);

        LOG_INFO("Created %zu devices for %s domain:",
                 devices.size(),
                 ducTypeToString(domain).c_str());
        for (const auto &device : devices)
        {
            LOG_INFO("  Device: %s", device.c_str());
        }

        // Add devices to progress fitting
        progressFit.AddUpdateDevice(domain, devices);

        // Create update device list
        UpdateDeviceList updateList;
        for (const auto &device : devices)
        {
            updateList.updateDeviceLists().push_back(device);
        }

        // Start update for this domain
        ReturnCode ret =
            manager.startUpdate(domain, UpdateMode::FormalMode, updateList);
        LOG_INFO("startUpdate for %s returned: %s",
                 ducTypeToString(domain).c_str(),
                 returnCodeToString(ret).c_str());

        if (ret != ReturnCode::OK)
        {
            allDomainsSuccess = false;
        }
    }

    base::Singleton<EventLoopManager>::Instance()
        .GetDefaultLoop()
        ->RunInLoopThread([&] { progressFit.Start(); });

    return allDomainsSuccess;
}

bool testRollback(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Rollback Test ===");

    RollbackComponentList rollbackList;
    // Note: rollbackList.rollbackLists().push_back(); - add components as needed

    ReturnCode ret = manager.rollback(DUCType::CDC, rollbackList);
    LOG_INFO("rollback returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(11));

    return ret == ReturnCode::OK;
}

bool testActivation(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Activation Test ===");

    ReturnCode ret = manager.activate(DUCType::CDC);
    LOG_INFO("activate returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(2));

    return ret == ReturnCode::OK;
}

bool testLogUpload(DUCServiceManager &manager)
{
    LOG_INFO("\n=== Starting Log Upload Test ===");

    ReturnCode ret = manager.uploadLog(DUCType::CDC);
    LOG_INFO("uploadLog returned: %s", returnCodeToString(ret).c_str());

    std::this_thread::sleep_for(std::chrono::seconds(2));

    return ret == ReturnCode::OK;
}

bool runAllTests(DUCServiceManager &manager)
{
    clearScreen();
    LOG_INFO("\n=== Running All Tests ===");

    bool allTestsPassed = true;

    LOG_INFO("Starting test sequence...");

    allTestsPassed &= testInventoryCollection(manager);
    allTestsPassed &= testDownload(manager);
    allTestsPassed &= testUnzip(manager);
    allTestsPassed &= testPackageVerification(manager);
    allTestsPassed &= testUpdateConditionCheck(manager);
    allTestsPassed &= testUpdate(manager);
    allTestsPassed &= testRollback(manager);
    allTestsPassed &= testActivation(manager);
    allTestsPassed &= testLogUpload(manager);

    if (allTestsPassed)
    {
        LOG_INFO("=== All tests PASSED ===");
    }
    else
    {
        LOG_ERROR("=== Some tests FAILED ===");
    }

    return allTestsPassed;
}

bool runSelectedTest(int choice, DUCServiceManager &manager)
{
    switch (choice)
    {
    case 1:
        return testInventoryCollection(manager);
    case 2:
        return testDownload(manager);
    case 3:
        return testUnzip(manager);
    case 4:
        return testPackageVerification(manager);
    case 5:
        return testUpdateConditionCheck(manager);
    case 6:
        return testUpdate(manager);
    case 7:
        return testRollback(manager);
    case 8:
        return testActivation(manager);
    case 9:
        return testLogUpload(manager);
    case 10:
        return runAllTests(manager);
    default:
        LOG_ERROR("Invalid test choice: %d", choice);
        return false;
    }
}


int main()
{
    LogConfig config;
    config.log_file_path = "logs/ota_master.log";
    config.max_file_size = 5 * 1024 * 1024;
    config.max_files = 3;
    config.console_output = true;
    config.log_level = LogLevel::kLogLevelDebug;

    base::Singleton<Logger>::Instance().Init(config);
    try
    {
        LOG_INFO("=== [DUC Service Manager Test] Starting...");

        auto &manager = base::Singleton<DUCServiceManager>::Instance();
        if (!manager.initialize(1))
        {
            LOG_ERROR("Failed to initialize service manager");
            return EXIT_FAILURE;
        }
        LOG_INFO("Service manager initialized successfully");

        if (!manager.createClient(DUCType::CDC))
        {
            LOG_ERROR("Failed to create CDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("CDC domain client created successfully");
        if (!manager.createClient(DUCType::MDC))
        {
            LOG_ERROR("Failed to create MDC domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("MDC domain client created successfully");
        if (!manager.createClient(DUCType::ZCU))
        {
            LOG_ERROR("Failed to create ZCU domain client");
            return EXIT_FAILURE;
        }
        LOG_INFO("ZCU domain client created successfully");

        LOG_INFO("Waiting for service connection...");

        std::thread([&] { // Interactive test menu
            int choice;
            bool testResult = true;
            do
            {
                displayTestMenu();
                choice = getUserChoice();

                if (choice == 0)
                {
                    LOG_INFO("Exiting test program...");
                    break;
                }

                testResult = runSelectedTest(choice, manager);

                if (testResult)
                {
                    LOG_INFO("Test completed successfully!");
                }
                else
                {
                    LOG_ERROR("Test failed!");
                }

                if (choice != 10) // Don't wait after "Run All Tests"
                {
                    cout << "\nPress Enter to return to menu...";
                    cin.get();
                }

            } while (choice != 0);

            // Terminate event loop
            auto &eventLoopManager =
                base::Singleton<EventLoopManager>::Instance();
            auto eventLoop = eventLoopManager.GetDefaultLoop();
            eventLoop->TerminateLoop();

            manager.shutdown();
            LOG_INFO("=== [DUC Service Manager Test] Program terminated ===");
            return EXIT_SUCCESS;

        })
            .detach();


        base::Singleton<EventLoopManager>::Instance()
            .GetDefaultLoop()
            ->LoopForever();
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Test exception: %s", e.what());
        return EXIT_FAILURE;
    }
}