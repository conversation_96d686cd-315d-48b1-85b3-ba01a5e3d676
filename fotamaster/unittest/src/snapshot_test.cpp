#define CATCH_CONFIG_MAIN // 该宏用于生成Catch2的main函数
#include "catch2/catch.hpp"
#include "state_machine/ota_state_snapshot.h"
#include "logger/logger.h"

using namespace seres::fotamaster;

TEST_CASE("OtaStateSnapshot", "[snapshot]")
{
    LogConfig config;
    config.log_file_path = "./snapshot_test.log";     // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;         // 5MB
    config.max_files = 3;                           // 最多保留3个文件
    config.console_output = true;                   // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug;    // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);

    OtaStateSnapshot snap("./test_history.json");
    SECTION("take")
    {
        // 构造测试数据
        OtaStateSnapshotInfo snapshot_info{
            "2025-06-10T12:34:56Z",
            {OtaInState::kOtaInDownloadState,
             OtaTaskInfo{"firmware_v1.2.3", "package_123456"}}};

        auto retval = snap.TakeSnapshot(snapshot_info);
        REQUIRE(retval);
    }

    SECTION("get")
    {
        OtaStateSnapshotInfo snapshot_info;
        auto retval = snap.GetSnapshot();
        REQUIRE(retval.has_value());
        snapshot_info = *retval;

        LOG_INFO("时间: %s, 状态: %d", snapshot_info.time.c_str(), static_cast<int>(snapshot_info.state_info.in_state));
    }
}