// #include "dds_service_manager/dds_wapper.hpp"
// #include "HPCC_OTA_HMI_Status.hpp"
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>
#include <optional>
#include "logger/logger.h"
#include "base/singleton.h"
#include "report_ota_hmi_status.h"

using namespace seres::fotamaster;
using namespace Seres::HPCC_OTA_HMI;

using HPCC_OTA_HMI_StatusPublisher =
    dds_wrapper::Publisher<HPCC_OTA_HMI_Status>;

int main()
{
#if 0
    auto pub_result = HPCC_OTA_HMI_StatusPublisher::Create(42, "HPCC_OTA_HMI_Status");
    if (!pub_result)
    {
        std::cerr << "创建订阅者失败: " << pub_result.error_msg << std::endl;
        return -1;
    }
    auto publisher = pub_result.GetValue();

    std::cout << "等待连接建立..." << std::endl;
    while (!publisher->IsConnected().GetValue())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << publisher->GetMatchedSubscribersCount()
                     .GetValue()
              << std::endl;

    HPCC_OTA_HMI_Status msg;
    std::optional<NewVersionStatus> version("3.1415");
    msg.OTAM_notifyNewVersionStatus(version);
    DownloadProgressStatus dl_progress{25., 400, 10, 80};
    std::optional<DownloadProgressStatus> download_progress(dl_progress);
    msg.OTAM_notifyDownloadProgressStatus(download_progress);
    std::optional<InstallProgressStatus> install_progress(99);
    msg.OTAM_notifyInstallProgressStatus(install_progress);
    std::optional<UpgradeFailStatus> fail_reason(611661);
    msg.OTAM_notifyUpgradeFailStatus(fail_reason);
    auto result = publisher->Publish(msg);
    if (!result)
    {
        std::cerr << "发布消息失败: " << std::endl;
        return -1;
    }

    std::cout << "已发布消息: version="
              << *msg.OTAM_notifyNewVersionStatus()
              << std::endl;
#else
    LogConfig config;
    config.log_file_path = "logs/HPCC_OTA_HMI_Status_oub_test.log";     // 日志文件路径
    config.max_file_size = 5 * 1024 * 1024;         // 5MB
    config.max_files = 3;                           // 最多保留3个文件
    config.console_output = true;                   // 输出到控制台
    config.log_level = LogLevel::kLogLevelDebug;    // 日志级别

    // 初始化日志系统
    base::Singleton<Logger>::Instance().Init(config);

    auto report_ota_hmi_status = std::make_unique<ReportOtaHmiStatus>();
    assert(report_ota_hmi_status);
    LOG_INFO("Create report_ota_hmi_status class succeed");
    LOG_INFO("等待连接建立...");
    while (!report_ota_hmi_status->PublisherIsConnected())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // std::this_thread::sleep_for(std::chrono::seconds(1));
    LOG_INFO("ReportNewVersion...");
    if (!report_ota_hmi_status->ReportNewVersion("3.1415"))
    {
        return -1;
    }
    // std::this_thread::sleep_for(std::chrono::seconds(1));

    LOG_INFO("ReportDownloadProgress...");
    if (!report_ota_hmi_status->ReportDownloadProgress(500, 30, 30, 40))
    {
        return -1;
    }

    // std::this_thread::sleep_for(std::chrono::seconds(1));
    LOG_INFO("ReportInstallPreconditionStatus...");
    if (!report_ota_hmi_status->ReportInstallPreconditionStatus(0x1234))
    {
        return -1;
    }

    // std::this_thread::sleep_for(std::chrono::seconds(1));
    LOG_INFO("ReportInstallProgress...");
    if (!report_ota_hmi_status->ReportInstallProgress(99))
    {
        return -1;
    }

    // std::this_thread::sleep_for(std::chrono::seconds(1));
    LOG_INFO("ReportUpgradeFailReason...");
    if (!report_ota_hmi_status->ReportUpgradeFailReason(122222))
    {
        return -1;
    }

    // std::this_thread::sleep_for(std::chrono::seconds(1));
    LOG_INFO("TriggerUpgradeCountdown...");
    if (!report_ota_hmi_status->TriggerUpgradeCountdown())
    {
        return -1;
    }
#endif
    // 等待所有消息被接收
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "pub 测试完成" << std::endl;
    return 0;
}