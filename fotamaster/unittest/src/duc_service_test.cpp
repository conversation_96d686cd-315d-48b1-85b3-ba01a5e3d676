#include "rpc/OTA_DucInterface_gen_server.hpp"
#include <chrono>
#include <cstdlib>
#include <iostream>
#include <memory>
#include <string>
#include <thread>
#include <vector>

using namespace std;
using namespace seres::ota_duc_service;

class DucService_impl : public Interface_DucServiceInterface_BASE
{
private:
    DUCType m_type;
    dds::domain::DomainParticipant m_participant;
    dds::pub::Publisher publisher;

    // 主题
    shared_ptr<dds::topic::Topic<OTA_DucDataUnion>> m_topic;

    // 发布者
    shared_ptr<dds::pub::DataWriter<OTA_DucDataUnion>> m_writer;

public:
    DucService_impl(DUCType type, dds::domain::DomainParticipant participant)
        : m_type(type), m_participant(participant), publisher(m_participant)
    {
        string topicname;
        switch (type)
        {
        case seres::ota_duc_service::DUCType::CDC:
            topicname = CDC_TOPIC_NAME;
            break;
        case seres::ota_duc_service::DUCType::MDC:
            topicname = MDC_TOPIC_NAME;
            break;
        case seres::ota_duc_service::DUCType::VDC:
            topicname = VDC_TOPIC_NAME;
            break;
        default:
            break;
        }
        m_topic =
            std::make_shared<dds::topic::Topic<OTA_DucDataUnion>>(m_participant,
                                                                  topicname);
        m_writer =
            std::make_shared<dds::pub::DataWriter<OTA_DucDataUnion>>(publisher,
                                                                     *m_topic);
        std::cout << "=== [DUC Server] 初始化完成" << std::endl;
    }

    // 实现RPC接口
    virtual ReturnCode inventoryCollection(
        const SelectedInventoryList &inventory_list) override
    {
        std::cout << m_type << "--[DUC Server] -- inventoryCollection called"
                  << std::endl;
        // 创建示例数据
        std::thread([this] {
            InventoryResult inventory_Result_list;
            InventoryInfo info;
            info.partNumber("TEST001");
            info.softwareVersion("1.0.0");
            info.supplierCode("SUP001");
            info.ecuName("TestECU");
            info.serialNumber("SN001");
            info.hardwareVersion("HW001");
            info.ecuBatchNumber("BATCH001");
            info.bootloaderVersion("BL001");
            info.backupVersion("BK001");
            info.SeamlessModeSupport(true);

            inventory_Result_list.InventoryLists().push_back(info);
            OTA_DucDataUnion data;
            data.inventoryResult(inventory_Result_list);
            sleep(3);
            // 发布结果
            m_writer->write(data);
            std::cout << "pub inventory_Result_list success\n";
        }).detach();

        return ReturnCode::OK;
    }

    virtual ReturnCode stopInventoryCollection() override
    {
        std::cout << m_type
                  << "--[DUC Server] -- stopInventoryCollection called"
                  << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getInventoryResult(
        InventoryResult &inventory_list) override
    {
        std::cout << m_type << "--[DUC Server] -- getInventoryResult called"
                  << std::endl;
        // 创建示例数据
        InventoryInfo info;
        info.partNumber("TEST001");
        info.softwareVersion("1.0.0");
        info.supplierCode("SUP001");
        info.ecuName("TestECU");
        info.serialNumber("SN001");
        info.hardwareVersion("HW001");
        info.ecuBatchNumber("BATCH001");
        info.bootloaderVersion("BL001");
        info.backupVersion("BK001");
        info.SeamlessModeSupport(true);

        inventory_list.InventoryLists().push_back(info);
        OTA_DucDataUnion data;
        data.inventoryResult(inventory_list);
        // 发布结果

        m_writer->write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode checkDownloadCondition(
        const DownloadConditionLists &conditions,
        CommonStatus &condition_result) override
    {
        std::cout << m_type << "--[DUC Server] -- checkDownloadCondition called"
                  << std::endl;
        condition_result.status() = Status::kStatusSuccess;
        return ReturnCode::OK;
    }

    virtual ReturnCode startDownload(
        const DownloadTaskLists &task_list) override
    {
        std::cout << m_type << "--[DUC Server] -- startDownload called"
                  << std::endl;
        std::thread([=] {
            int p = 0;
            do
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                p += 5;
                DownloadProgress download_progress;
                for (auto &task_info : task_list.taskLists())
                {
                    uint64_t download_count = p * task_info.packageSize() / 100;
                    DownloadProgressInfo progress_info;
                    progress_info.progressPercent(p);
                    progress_info.packageName(task_info.packageName());
                    progress_info.downloadedSize(download_count);
                    progress_info.totalSize(task_info.packageSize());
                    if (p == 100)
                        progress_info.status().status(Status::kStatusSuccess);
                    else
                        progress_info.status().status(
                            Status::kStatusInProgress);

                    download_progress.allFinished(p == 100);
                    download_progress.progressLists().push_back(progress_info);
                }

                OTA_DucDataUnion data;
                data.downloadProgress(download_progress);
                // 发布进度
                m_writer->write(data);
                std::cout << m_type
                          << "--[DUC Server] -- publish download progress: "
                          << p << "%" << std::endl;
            } while (p < 100);
        }).detach();

        return ReturnCode::OK;
    }

    virtual ReturnCode downloadCtrl(
        const DownloadCtrl &download_command) override
    {
        std::cout << m_type << "--[DUC Server] -- downloadCtrl called"
                  << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getDownloadProgress(
        DownloadProgress &download_progress) override
    {
        std::cout << m_type << "--[DUC Server] -- getDownloadProgress called"
                  << std::endl;
        // 创建示例进度数据
        DownloadProgressInfo progress_info;
        progress_info.progressPercent(50);
        progress_info.packageName("test_package");
        progress_info.downloadedSize(500);
        progress_info.totalSize(1000);
        progress_info.status().status(Status::kStatusInProgress);

        download_progress.allFinished(false);
        download_progress.progressLists().push_back(progress_info);
        return ReturnCode::OK;
    }

    virtual ReturnCode uzipPackages() override
    {
        std::cout << m_type << "--[DUC Server] -- uzipPackages called"
                  << std::endl;
        std::thread([this] {
            CommonStatus uzip_Result;
            uzip_Result.status(Status::kStatusSuccess);
            std::this_thread::sleep_for(std::chrono::seconds(5));
            OTA_DucDataUnion data;
            data.uzipPackagesResult(uzip_Result);
            // 发布结果
            m_writer->write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getuzipPackagesResult(CommonStatus &uzip_Result) override
    {
        std::cout << m_type << "--[DUC Server] -- getuzipPackagesResult called"
                  << std::endl;
        uzip_Result.status(Status::kStatusSuccess);
        return ReturnCode::OK;
    }

    virtual ReturnCode startPackagesVerify() override
    {
        std::cout << m_type << "--[DUC Server] -- startPackagesVerify called"
                  << std::endl;
        std::thread([this] {
            CommonStatus verify_Result;
            verify_Result.status(Status::kStatusSuccess);
            // 发布结果
            OTA_DucDataUnion data;
            data.packagesVerifyResult(verify_Result);
            m_writer->write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getPackagesVerifyResult(
        CommonStatus &verify_Result) override
    {
        std::cout << m_type
                  << "--[DUC Server] -- getPackagesVerifyResult called"
                  << std::endl;
        verify_Result.status(Status::kStatusSuccess);

        return ReturnCode::OK;
    }

    virtual ReturnCode checkUpdateCondition() override
    {
        std::cout << m_type << "--[DUC Server] -- checkUpdateCondition called"
                  << std::endl;
        std::thread([this] {
            CommonStatus checkcondition_Result;
            checkcondition_Result.status(Status::kStatusSuccess);
            std::this_thread::sleep_for(std::chrono::seconds(5));
            // 发布结果
            OTA_DucDataUnion data;
            data.checkUpdateConditionResult(checkcondition_Result);
            m_writer->write(data);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getCheckUpdateConditionResult(
        CommonStatus &checkcondition_Result) override
    {
        std::cout << m_type
                  << "--[DUC Server] -- getCheckUpdateConditionResult called"
                  << std::endl;
        checkcondition_Result.status(Status::kStatusSuccess);
        return ReturnCode::OK;
    }

    virtual ReturnCode startUpdate(const UpdateMode &mode,
                                   const UpdateDeviceList &update_list) override
    {
        std::cout << m_type << "--[DUC Server] -- startUpdate called"
                  << std::endl;

        // 获取需要更新的设备列表
        std::thread([update_list, this] {
            switch (m_type)
            {
            case DUCType::CDC:
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                break;
            case DUCType::MDC:
                std::this_thread::sleep_for(std::chrono::milliseconds(2500));
                break;
            case DUCType::VDC:
                std::this_thread::sleep_for(std::chrono::milliseconds(5000));
                break;
            default:
                break;
            }

            int p = 0;
            do
            {
                switch (m_type)
                {
                case DUCType::CDC:
                    std::this_thread::sleep_for(std::chrono::milliseconds(300));
                    break;
                case DUCType::MDC:
                    std::this_thread::sleep_for(std::chrono::milliseconds(400));
                    break;
                case DUCType::VDC:
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                    break;
                default:
                    break;
                }

                UpdateProgress update_progress;
                p += 5;
                int i = 0;
                for (const auto &device : update_list.updateDeviceLists())
                {
                    int gress = p + update_list.updateDeviceLists().size() - i;
                    i++;
                    std::cout << "Device: " << device << std::endl;
                    DeviceUpdateProgress progress;
                    progress.progressPercent(min(100, gress));
                    progress.deviceName(device);
                    progress.deviceId(device);
                    progress.status().status(Status::kStatusInProgress);
                    update_progress.allFinished(p == 100);
                    update_progress.progressLists().push_back(progress);
                    std::cout << m_type << "--[DUC Server] -- "
                              << progress.deviceName()
                              << " publish update progress: " << min(100, gress)
                              << "%" << std::endl;
                }
                // 发布进度
                OTA_DucDataUnion data;
                data.updateProgress(update_progress);
                m_writer->write(data);
            } while (p < 100);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode resumeUpdate() override
    {
        std::cout << m_type << "--[DUC Server] -- resumeUpdate called"
                  << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode pauseUpdate() override
    {
        std::cout << m_type << "--[DUC Server] -- pauseUpdate called"
                  << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode getUpdateProgress(
        UpdateProgress &update_progress) override
    {
        std::cout << m_type << "--[DUC Server] -- getUpdateProgress called"
                  << std::endl;
        // 创建示例进度数据
        DeviceUpdateProgress progress;
        progress.progressPercent(75);
        progress.deviceName("TestDevice");
        progress.deviceId("DEV001");
        progress.status().status(Status::kStatusInProgress);

        update_progress.allFinished(false);
        update_progress.progressLists().push_back(progress);

        // 发布进度
        OTA_DucDataUnion data;
        data.updateProgress(update_progress);
        m_writer->write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode activate() override
    {
        std::cout << m_type << "--[DUC Server] -- activate called" << std::endl;
        return ReturnCode::OK;
    }

    virtual ReturnCode rollback(
        const RollbackComponentList &component_list) override
    {
        std::cout << m_type << "--[DUC Server] -- rollback called" << std::endl;
        std::thread([this] {
            int p = 0;
            do
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                p += 10;
                UpdateProgress update_progress;
                DeviceUpdateProgress progress;
                progress.progressPercent(p);
                progress.deviceName("RollbackDevice");
                progress.deviceId("DEV001");
                progress.status().status(Status::kStatusInProgress);
                if (p == 100)
                    update_progress.allFinished(true);
                else
                    update_progress.allFinished(false);
                update_progress.progressLists().push_back(progress);
                // 发布进度
                OTA_DucDataUnion data;
                data.updateProgress(update_progress);
                std::cout << m_type
                          << "--[DUC Server] -- publish rollback progress: "
                          << p << "%" << std::endl;
                m_writer->write(data);
            } while (p < 100);
        }).detach();
        return ReturnCode::OK;
    }

    virtual ReturnCode getRollbackProgress(
        UpdateProgress &update_progress) override
    {
        std::cout << m_type << "--[DUC Server] -- getRollbackProgress called"
                  << std::endl;
        // 创建示例进度数据
        DeviceUpdateProgress progress;
        progress.progressPercent(25);
        progress.deviceName("TestDevice");
        progress.deviceId("DEV001");
        progress.status().status(Status::kStatusInProgress);

        update_progress.allFinished(false);
        update_progress.progressLists().push_back(progress);

        // 发布进度
        OTA_DucDataUnion data;
        data.updateProgress(update_progress);
        m_writer->write(data);
        return ReturnCode::OK;
    }

    virtual ReturnCode uploadLog() override
    {
        std::cout << m_type << "--[DUC Server] -- uploadLog called"
                  << std::endl;
        return ReturnCode::OK;
    }
};

int main(int argc, char *argv[])
{
    try
    {
        DUCType duc_type;
        std::cout << "=== [DUC Server] Starting..." << std::endl;
        if (argc < 2)
        { // 添加参数检查
            std::cout << "=== [DUC Server] Missing DUC type argument"
                      << std::endl;
            return EXIT_FAILURE;
        }
        std::string serviceName;
        if (std::string(argv[1]) == "cdc") // 将argv[1]转换为std::string再比较
        {
            duc_type = DUCType::CDC;
            serviceName = CDC_SERVICE_NAME;
        }
        else if (std::string(argv[1]) == "mdc") // 直接比较std::string
        {
            duc_type = DUCType::MDC;
            serviceName = MDC_SERVICE_NAME;
        }
        else if (std::string(argv[1]) == "VDC") // 直接比较std::string
        {
            duc_type = DUCType::VDC;
            serviceName = VDC_SERVICE_NAME;
        }
        else
        {
            std::cout << "=== [DUC Server] Invalid DUC type" << std::endl;
            return EXIT_FAILURE;
        }
        // 创建RPC服务器
        dds::rpc::Server server(dds::rpc::ServerParam(4));

        // 配置服务参数
        dds::domain::DomainParticipant participant(1);
        dds::rpc::ServiceParams param(participant);
        param.serviceName(serviceName);

        // 创建服务实现
        std::shared_ptr<DucService_impl> impl =
            std::make_shared<DucService_impl>(duc_type, participant);

        // 创建服务
        DucServiceInterfaceService service(impl, server, param);

        std::cout << "=== [DUC Server] Running..." << std::endl;

        // 运行服务器
        server.run();

        return 0;
    }
    catch (const dds::core::Exception &e)
    {
        std::cerr << "=== [DUC Server] Exception: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
}