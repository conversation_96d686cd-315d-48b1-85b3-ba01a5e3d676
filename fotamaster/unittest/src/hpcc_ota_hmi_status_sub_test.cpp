#include "dds_service_manager/dds_wapper.hpp"
#include "HPCC_OTA_HMI_Status.hpp"
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

using namespace seres::fotamaster;
using namespace Seres::HPCC_OTA_HMI;

using HPCC_OTA_HMI_StatusSubscriber =
    dds_wrapper::Subscriber<HPCC_OTA_HMI_Status>;

int main()
{
    // 创建订阅者
    auto sub_result = HPCC_OTA_HMI_StatusSubscriber::Create(
        42,
        "HPCC_OTA_HMI_Status",
        [](const auto &msg) {
            std::cout << "Recv HPCC_OTA_HMI_Status msg\n";
            auto new_version = msg.OTAM_notifyNewVersionStatus();
            if (new_version.has_value())
            {
                std::cout << "new version: " << *new_version << "\n";
            }

            auto download_progress = msg.OTAM_notifyDownloadProgressStatus();
            if (download_progress.has_value())
            {
                std::cout << "download rate: " << (*download_progress).rate()
                          << "\n";
                std::cout << "download packagesize: "
                          << (*download_progress).packagesize() << "\n";
                std::cout << "download remaintime: "
                          << (*download_progress).remaintime() << "\n";
                std::cout << "download progress: "
                          << static_cast<uint32_t>(
                                 (*download_progress).progress())
                          << "\n";
            }

            auto upgrade_contdown = msg.OTAM_TriggerUpgradeCountdown();
            if (upgrade_contdown.has_value())
            {
                std::cout << "upgrade_contdown: "
                          << static_cast<uint32_t>(*upgrade_contdown) << "\n";
            }

            auto precondition = msg.OTAM_notifyInstallPreconditionStatus();
            if (precondition.has_value())
            {
                std::cout << "precondition: "
                          << static_cast<uint32_t>(*precondition) << "\n";
            }

            auto install_progress = msg.OTAM_notifyInstallProgressStatus();
            if (install_progress.has_value())
            {
                std::cout << "install_progress: "
                          << static_cast<uint32_t>(
                                 (*install_progress).progress())
                          << "\n";
            }

            auto fail_status = msg.OTAM_notifyUpgradeFailStatus();
            if (fail_status.has_value())
            {
                std::cout << "fail_status: " << *fail_status << "\n";
            }
        });
    if (!sub_result)
    {
        std::cerr << "创建订阅者失败: " << sub_result.error_msg << std::endl;
        return -1;
    }
    auto subscriber = sub_result.GetValue();

    // 等待所有消息被接收
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "sub 测试完成" << std::endl;
    return 0;
}