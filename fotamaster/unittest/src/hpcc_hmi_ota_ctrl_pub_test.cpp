#include "HPCC_HMI_OTA_Control.hpp"
#include "dds_service_manager/hmi_ota_adapter.hpp"
#include <chrono>
#include <iostream>
#include <memory>
#include <signal.h>
#include <termios.h>
#include <thread>

// 设置终端为非规范模式（直接读取按键）
static void set_terminal_mode(bool enable_raw_mode)
{
    static struct termios original_tio;
    static bool is_original_saved = false;

    if (enable_raw_mode)
    {
        // 保存原始终端设置
        if (!is_original_saved)
        {
            tcgetattr(STDIN_FILENO, &original_tio);
            is_original_saved = true;
        }

        // 设置非规范模式：关闭回显、即时读取
        struct termios raw_tio = original_tio;
        raw_tio.c_lflag &= ~(ICANON | ECHO);
        tcsetattr(STDIN_FILENO, TCSANOW, &raw_tio);
    }
    else
    {
        // 恢复原始终端设置
        if (is_original_saved)
        {
            tcsetattr(STDIN_FILENO, TCSANOW, &original_tio);
        }
    }
}

static void press_space()
{
    set_terminal_mode(true);
    // 循环检测输入
    char c = 0;
    while (1)
    {
        if (read(STDIN_FILENO, &c, 1) == 1)
        {
            if (c == ' ')
            { // 空格键的 ASCII 码
                break;
            }
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    set_terminal_mode(false);
}

// 信号处理函数
static void signal_handler(int sig)
{
    set_terminal_mode(false); // 恢复终端
    exit(sig);
}

using namespace seres::fotamaster;
using namespace Seres::HPCC_HMI_OTA;

std::shared_ptr<dds_wrapper::HPCC_HMI_OTA_ControlPublisher> g_publisher{nullptr};

static int create_publisher()
{
    auto pub_result =
        dds_wrapper::HPCC_HMI_OTA_ControlPublisher::Create(42, "HPCC_HMI_OTA_Control");
    if (!pub_result)
    {
        std::cerr << "创建订阅者失败: " << pub_result.error_msg << std::endl;
        return -1;
    }
    g_publisher = pub_result.GetValue();
    return 0;
}

static void set_seamless_upgrade_mode()
{
    std::cout << "add HMI_SeamlessUpgradeSet_OTA_In topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (!g_publisher->IsConnected().GetValue())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    HMI_SeamlessUpgradeSet_OTA_In msg;
    msg.seamlessupgradecmd(true);
    auto result = g_publisher->Publish(msg);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }

    std::cout << "已发布消息: seamlessupgradecmd="
              << static_cast<uint32_t>(msg.seamlessupgradecmd())
              << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

static void set_auto_update()
{
    std::cout << "add HMI_AutoUpdateSet_OTA_In topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (!g_publisher->IsConnected().GetValue())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    HMI_AutoUpdateSet_OTA_In msg;
    msg.autoupdatecmd(false);
    auto result = g_publisher->Publish(msg);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }

    std::cout << "已发布消息: autoupdatecmd="
              << msg.autoupdatecmd()
              << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

static void upgrade_mode_ctrl()
{
    std::cout << "add HMI_UpdateModeCtrl_OTA_In topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (!g_publisher->IsConnected().GetValue())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    AppointmentTime time(DayFlag::TOMORROW, 2, 30);
    UpdateModeCtrl update_mode_ctrl(UpdateMode::UPDATE_APPOINTMENT, time);
    HMI_UpdateModeCtrl_OTA_In msg(update_mode_ctrl);
    auto result = g_publisher->Publish(msg);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }
    std::cout << "已发布消息: upgrade_mode="
              << static_cast<uint32_t>(msg.updatemodectrl().updatemode()) << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

static void upgrade_ctrl()
{
    std::cout << "add HMI_UpdateCtrl_OTA_In topic\n";
    // 等待连接建立
    std::cout << "等待连接建立..." << std::endl;
    while (!g_publisher->IsConnected().GetValue())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    std::cout << "连接已建立, count: "
              << g_publisher->GetMatchedSubscribersCount().GetValue()
              << std::endl;

    HMI_UpdateCtrl_OTA_In msg;
    msg.updatectrl(true);
    auto result = g_publisher->Publish(msg);
    if (!result)
    {
        std::cerr << "发布消息失败: err: " << result.error_msg << std::endl;
        return;
    }

    std::cout << "已发布消息: autoupdatecmd="
              << msg.updatectrl()
              << std::endl;
    std::this_thread::sleep_for(std::chrono::seconds(1));
}

int main()
{
    // 注册信号处理
    signal(SIGINT, signal_handler);  // Ctrl+C
    signal(SIGTERM, signal_handler); // kill

    if (create_publisher() == -1)
    {
        return -1;
    }

    // 1
    press_space();
    set_seamless_upgrade_mode();

    // 2
    press_space();
    set_auto_update();

    // 3
    press_space();
    upgrade_mode_ctrl();

    // 4
    press_space();
    upgrade_ctrl();

    // 等待所有消息被接收
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "pub 测试完成" << std::endl;
    return 0;
}