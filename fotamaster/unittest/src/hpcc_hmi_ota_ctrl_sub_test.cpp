#include "HPCC_HMI_OTA_Control.hpp"
#include "dds_service_manager/hmi_ota_adapter.hpp"
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

using namespace seres::fotamaster;
using namespace Seres::HPCC_HMI_OTA;

std::shared_ptr<dds_wrapper::HPCC_HMI_OTA_ControlSubscriber> g_subscriber{nullptr};

static int create_subscriber()
{
    // 创建订阅者
    auto sub_result =
        dds_wrapper::HPCC_HMI_OTA_ControlSubscriber::Create(42, "HPCC_HMI_OTA_Control");
    if (!sub_result)
    {
        std::cerr << "创建订阅者失败: " << sub_result.error_msg << std::endl;
        return -1;
    }
    g_subscriber = sub_result.GetValue();

    g_subscriber->Subscribe<HMI_SeamlessUpgradeSet_OTA_In>(
        [](const HMI_SeamlessUpgradeSet_OTA_In &msg) {
            std::cout << "收到消息: is seamless upgrade = " << msg.seamlessupgradecmd() << std::endl;
        });
    g_subscriber->Subscribe<HMI_AutoUpdateSet_OTA_In>(
        [](const HMI_AutoUpdateSet_OTA_In &msg) {
            std::cout << "收到消息: is auto update = " << msg.autoupdatecmd() << std::endl;
        });
    g_subscriber->Subscribe<HMI_UpdateModeCtrl_OTA_In>(
        [](const HMI_UpdateModeCtrl_OTA_In &msg) {
            auto update_mode_ctrl = msg.updatemodectrl();

            std::cout << "收到消息: updatemode = "
                      << static_cast<int32_t>(update_mode_ctrl.updatemode())
                      << std::endl;
            // auto time = update_mode_ctrl.appointmenttime();
            // std::cout << "收到消息: appointmenttime dayflag = "
            //           << static_cast<int32_t>(time.dayflag())
            //           << ", hour = " << static_cast<int32_t>(time.hour())
            //           << ", minute = " << static_cast<int32_t>(time.minute())
            //           << std::endl;
        });

    g_subscriber->Subscribe<HMI_UpdateCtrl_OTA_In>(
        [](const HMI_UpdateCtrl_OTA_In &msg) {
            std::cout << "收到消息: is updating = " << msg.updatectrl() << std::endl;
        });
    return 0;
}

int main()
{
    if (create_subscriber() == -1)
    {
        return -1;
    }

    // 等待所有消息被接收
    while (1)
    {
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    std::cout << "sub 测试完成" << std::endl;
    return 0;
}