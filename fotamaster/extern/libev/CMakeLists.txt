cmake_minimum_required(VERSION 3.1 FATAL_ERROR)

project(libev VERSION 4.25 LANGUAGES C)

# check include files
include(CheckIncludeFiles)
check_include_files(dlfcn.h HAVE_DLFCN_H)
check_include_files(inttypes.h HAVE_INTTYPES_H)
check_include_files(memory.h HAVE_MEMORY_H)
check_include_files(poll.h HAVE_POLL_H)
check_include_files(port.h HAVE_PORT_H)
check_include_files(stdint.h HAVE_STDINT_H)
check_include_files(stdlib.h HAVE_STDLIB_H)
check_include_files(strings.h HAVE_STRINGS_H)
check_include_files(string.h HAVE_STRING_H)
check_include_files(sys/epoll.h HAVE_SYS_EPOLL_H)
check_include_files(sys/eventfd.h HAVE_SYS_EVENTFD_H)
check_include_files(sys/event.h HAVE_SYS_EVENT_H)
check_include_files(sys/inotify.h HAVE_SYS_INOTIFY_H)
check_include_files(sys/select.h HAVE_SYS_SELECT_H)
check_include_files(sys/signalfd.h HAVE_SYS_SIGNALFD_H)
check_include_files(sys/stat.h HAVE_SYS_STAT_H)
check_include_files(sys/types.h HAVE_SYS_TYPES_H)
check_include_files(unistd.h HAVE_UNISTD_H)
check_include_files("fcntl.h;assert.h;errno.h;limits.h;stdio.h;time.h;signal.h;math.h;float.h" STDC_HEADERS)

# check functions
include(CheckFunctionExists)
check_function_exists(clock_gettime HAVE_CLOCK_GETTIME)
check_function_exists(epoll_ctl HAVE_EPOLL_CTL)
check_function_exists(eventfd HAVE_EVENTFD)
check_function_exists(inotify_init HAVE_INOTIFY_INIT)
check_function_exists(kqueue HAVE_KQUEUE)
check_function_exists(nanosleep HAVE_NANOSLEEP)
check_function_exists(poll HAVE_POLL)
check_function_exists(port_create HAVE_PORT_CREATE)
check_function_exists(select HAVE_SELECT)
check_function_exists(signalfd HAVE_SIGNALFD)

# check libraries
include(CheckLibraryExists)
check_library_exists(rt timer_create "" HAVE_LIBRT)
check_library_exists(m floor "" HAVE_FLOOR)
check_library_exists(pthread pthread_create "" HAVE_LIBPTHREAD)

set(LT_OBJDIR ".libs")

configure_file(config.h.cmake.in ${CMAKE_CURRENT_BINARY_DIR}/config.h)

# add libraries
if (HAVE_LIBRT)
list(APPEND SYSTEM_LIBS "rt")
endif()

if (HAVE_FLOOR)
list(APPEND SYSTEM_LIBS "m")
endif()

if (HAVE_LIBPTHREAD)
list(APPEND SYSTEM_LIBS "pthread")
endif()

set(LIBEV_INTERFACE_HEADER ev.h ev++.h event.h)

add_library(libev SHARED ev.c event.c)
add_library(libev::libev ALIAS libev)

set_target_properties(libev
  PROPERTIES
    SOVERSION ${PROJECT_VERSION_MAJOR}
    VERSION ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}.0
    PUBLIC_HEADER "${LIBEV_INTERFACE_HEADER}"
    PREFIX ""
)

include(GNUInstallDirs)

target_include_directories(libev
  PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
    $<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}>
  PRIVATE
    ${CMAKE_CURRENT_BINARY_DIR}
)

target_compile_options(libev
  PRIVATE
    $<$<CONFIG:Debug>:-O0 -g>
    -Wall -Wextra
    -Wno-unused-value -Wno-comment -Wno-sign-compare -Wno-parentheses

  PUBLIC
    -Wno-unused-parameter -Wno-strict-aliasing -Wno-shadow
)

target_link_libraries(libev
  PRIVATE
    ${SYSTEM_LIBS}
)
# install
set(libev_cmake_dir ${CMAKE_INSTALL_LIBDIR}/cmake/${PROJECT_NAME})

install(TARGETS libev
  EXPORT libev-targets
  LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
  PUBLIC_HEADER DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
)

install(EXPORT libev-targets
  FILE libev-targets.cmake
  NAMESPACE libev::
  DESTINATION ${libev_cmake_dir}
)

include(CMakePackageConfigHelpers)

configure_package_config_file(
  ${CMAKE_CURRENT_SOURCE_DIR}/libev-config.cmake.in
  ${CMAKE_CURRENT_BINARY_DIR}/libev-config.cmake
  INSTALL_DESTINATION
    ${libev_cmake_dir}
)

write_basic_package_version_file(
  ${CMAKE_CURRENT_BINARY_DIR}/libev-config-version.cmake
  VERSION
    ${PROJECT_VERSION}
  COMPATIBILITY
    SameMajorVersion
)

install(FILES
  ${CMAKE_CURRENT_BINARY_DIR}/libev-config.cmake
  ${CMAKE_CURRENT_BINARY_DIR}/libev-config-version.cmake
  DESTINATION
    ${libev_cmake_dir}
)