TODO: ev_loop_wakeup
TODO: EV_STANDALONE == NO_HASSLE (do not use clock_gettime in ev_standalone)
TODO: faq, process a thing in each iteration
TODO: dbeugging tips, ev_verify, ev_init twice
TODO: ev_break for immediate exit (EVBREAK_NOW?)
TODO: ev_feed_child_event
TODO: document the special problem of signals around fork.
TODO: store pid for each signal
TODO: document file descriptor usage per loop
TODO: store loop pid_t and compare isndie signal handler,store 1 for same, 2 for differign pid, clean up in loop_fork
TODO: embed watchers need updating when fd changes
TODO: document portability requirements for atomic pointer access
TODO: document requirements for function pointers and calling conventions.

