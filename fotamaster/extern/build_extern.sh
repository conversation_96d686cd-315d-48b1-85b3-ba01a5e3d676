#!/bin/bash

BUILD_PLATFORM=$1
BUILD_TYPE=$2
EXTERN_INSTALL_PATH=$3
echo "EXTERN_INSTALL_PATH:${EXTERN_INSTALL_PATH}"

LIBEV_PATH=${PWD}/libev
if [ -e ${LIBEV_PATH} ] && [ -d ${LIBEV_PATH} ];then
    echo "building libev..."
    cd ${LIBEV_PATH}
    ./build.sh ${BUILD_PLATFORM} ${BUILD_TYPE} ${EXTERN_INSTALL_PATH}
    cd ..
fi

SPDLOG_PATH=${PWD}/spdlog-1.15.2
if [ -e ${SPDLOG_PATH} ] &&  [ -d ${SPDLOG_PATH} ];then
    echo "building spdlog..."
    cd ${SPDLOG_PATH}
    ./build.sh ${BUILD_PLATFORM} ${BUILD_TYPE} ${EXTERN_INSTALL_PATH}
    cd ..
fi

CATCH2_PATH=${PWD}/catch2
if [ -e ${CATCH2_PATH} ] && [ -d ${CATCH2_PATH} ];then
    echo "copying catch2 header file..."
    cp ${CATCH2_PATH}/ ${EXTERN_INSTALL_PATH}/include/ -arf
fi

JSON_PATH=${PWD}/nlohmann
if [ -e ${JSON_PATH} ] && [ -d ${JSON_PATH} ];then
    echo "copying nlohmann header file..."
    cp ${JSON_PATH}/ ${EXTERN_INSTALL_PATH}/include/ -arf
fi




