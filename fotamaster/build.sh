#!/bin/bash

set -e

# 显示帮助信息
show_help() {
    echo "Usage: $0 -p <value> -t <value> -o <value>"
    echo "Options:"
    echo "  -p <value>  指定参数 p 的值: platform: x86 or mt8678"
    echo "  -t <value>  指定参数 t 的值: build type: Release or Debug"
    echo "  -o <value>  指定参数 o 的值: install prefix"
    echo "  -h          显示帮助信息"
}

# 无参数时显示帮助
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 初始化变量
BUILD_TYPE=""
PLATFORM=""
INSTALL_PREFIX=""

# 解析命令行参数
while getopts ":p:t:o:h" opt; do
    case $opt in
        p)
            PLATFORM="$OPTARG"
            ;;
        t)
            BUILD_TYPE="$OPTARG"
            ;;
        o)
            INSTALL_PREFIX="$OPTARG"
            ;;
        h)
            show_help
            exit 0
            ;;
        \?)
            echo "错误：无效的选项 -$OPTARG" >&2
            show_help
            exit 1
            ;;
        :)
            echo "错误：选项 -$OPTARG 需要参数" >&2
            show_help
            exit 1
            ;;
    esac
done

# 检查必须参数是否存在
if [ -z "$PLATFORM" ] || [ -z "$INSTALL_PREFIX" ]; then
    echo "错误：必须提供 -p 和 -o 参数" >&2
    show_help
    exit 1
fi

if ! { [ "$PLATFORM" = "x86" ] || [ "$PLATFORM" = "mt8678" ]; };then
    echo "错误：必须提供正确的 -p 参数" >&2
    show_help
    exit 1
fi

if [ -z "$BUILD_TYPE" ]; then
    BUILD_TYPE=Release
else
    if ! { [ "$BUILD_TYPE" = "Release" ] || [ "$BUILD_TYPE" = "Debug" ]; };then
        echo "错误：必须提供正确的 -o 参数" >&2
        show_help
        exit 1
    fi
fi

echo "参数 p 的值为：$PLATFORM"
echo "参数 t 的值为：$BUILD_TYPE"
echo "参数 o 的值为：$INSTALL_PREFIX"

# build extern lib
EXTERN_PATH=${PWD}/extern
if [ -e ${EXTERN_PATH} ] &&  [ -d ${EXTERN_PATH} ];then
    echo "building extern..."
    cd ${EXTERN_PATH}
    ./build_extern.sh ${PLATFORM} ${BUILD_TYPE} ${INSTALL_PREFIX}
    cd ..
fi

# generate rpc idl
cd gen/
./gen_rpc_idl.sh OTA_DucInterface.idl
if [ $? -ne 0 ];then
    echo "generate rpc hpp/cpp failed"
    cd -
    exit 1
fi
cd ..

# build fotamaster
mkdir -p build
cd build/
rm -rf *

if [ "${PLATFORM}" = "x86" ];then
    cmake -DCMAKE_INSTALL_PREFIX=${INSTALL_PREFIX} -DCMAKE_BUILD_TYPE=${BUILD_TYPE} -DENABLE_TEST=ON ..
else
    echo "build mt8678 platform.."
    cmake -DCMAKE_INSTALL_PREFIX=${INSTALL_PREFIX} -DCMAKE_BUILD_TYPE=${BUILD_TYPE} -DCMAKE_TOOLCHAIN_FILE=./cmake/yocto-toolchain.cmake -DENABLE_TEST=ON ..
fi

make -j32

make install

# exp ./build.sh -p x86 -o /home/<USER>/workspace/cdds_install/x64