#pragma once
#include "HPCC_OTA_HMI_Status.hpp"
#include "base/noncopyable.h"
#include "dds_service_manager/dds_wapper.hpp"
#include <memory>
#include <string>

namespace seres
{
namespace fotamaster
{

class ReportOtaHmiStatus : base::Noncopyable
{
public:
    using HPCC_OTA_HMI_Status = Seres::HPCC_OTA_HMI::HPCC_OTA_HMI_Status;
    ReportOtaHmiStatus();

    bool PublisherIsConnected();
    bool ReportNewVersion(const std::string &version);
    bool ReportDownloadProgress(const uint32_t total_size,
                                const uint16_t remain_time,
                                const float rate,
                                const uint8_t progress);
    bool ReportInstallPreconditionStatus(const uint16_t precondition_status);
    bool ReportInstallProgress(const uint8_t progress);
    bool TriggerUpgradeCountdown();
    bool ReportUpgradeFailReason(const uint32_t reason);

private:
    bool InitPublisher();

private:
    using HPCC_OTA_HMI_StatusPublisher =
        dds_wrapper::Publisher<HPCC_OTA_HMI_Status>;
    std::shared_ptr<HPCC_OTA_HMI_StatusPublisher> ota_hmi_status_pub_{nullptr};
};

} // namespace fotamaster
} // namespace seres