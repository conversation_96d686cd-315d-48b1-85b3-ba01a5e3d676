#pragma once

#include "base/noncopyable.h"
#include "base/singleton.h"
#include <cassert>
#include <cstdarg>
#include <filesystem>
#include <memory>
#include <mutex>
#include <spdlog/spdlog.h>
#include <string>
#include <vector>

namespace seres
{
namespace fotamaster
{

// 日志级别枚举
enum class LogLevel
{
    kLogLevelTrace = spdlog::level::trace,
    kLogLevelDebug = spdlog::level::debug,
    kLogLevelInfo = spdlog::level::info,
    kLogLevelWarn = spdlog::level::warn,
    kLogLevelError = spdlog::level::err,
    kLogLevelCritical = spdlog::level::critical,
    kLogLevelOff = spdlog::level::off
};

// 日志配置结构体
struct LogConfig
{
    std::string log_file_path{}; // 日志文件路径，为空则不输出到文件
    size_t max_file_size{0ul};  // 单个日志文件最大大小（字节）
    size_t max_files{0ul};      // 最大保留的日志文件数量
    bool console_output{false}; // 是否输出到控制台
    LogLevel log_level{LogLevel::kLogLevelInfo}; // 日志级别
};

#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-function"
static std::string __attribute__((format(printf, 1, 2)))
__LogFormat(const char *fmt, ...)
{
    constexpr int _max_log_line_size_{768};
    std::vector<char> buf(_max_log_line_size_ + 1);

    va_list args;
    va_start(args, fmt);
    int size = vsnprintf(buf.data(), buf.size(), fmt, args);
    va_end(args);

    assert(size >= 0);
    return std::string(buf.data(), static_cast<size_t>(size));
}

// 获取文件名的辅助函数
static std::string GetFileName(const char *file_path)
{
    return std::filesystem::path(file_path).filename().string();
}
#pragma GCC diagnostic pop

class Logger : public base::Noncopyable
{
public:
    Logger() = default;

    // 初始化日志系统
    void Init(std::string &&config_path);
    void Init(const LogConfig &config);

    // 获取日志器
    std::shared_ptr<spdlog::logger> GetLogger() const;

private:
    // 设置日志级别
    void SetLogLevel(LogLevel level);

private:
    std::shared_ptr<spdlog::logger> logger_{nullptr};
    // mutable std::mutex mutex_{};
};

#define LOG_TRACE(fmt, ...)                                                    \
    base::Singleton<Logger>::Instance().GetLogger()->trace(                    \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))
#define LOG_DEBUG(fmt, ...)                                                    \
    base::Singleton<Logger>::Instance().GetLogger()->debug(                    \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))
#define LOG_INFO(fmt, ...)                                                     \
    base::Singleton<Logger>::Instance().GetLogger()->info(                     \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))
#define LOG_WARN(fmt, ...)                                                     \
    base::Singleton<Logger>::Instance().GetLogger()->warn(                     \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))
#define LOG_ERROR(fmt, ...)                                                    \
    base::Singleton<Logger>::Instance().GetLogger()->error(                    \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))
#define LOG_CRITICAL(fmt, ...)                                                 \
    base::Singleton<Logger>::Instance().GetLogger()->critical(                 \
        "[{}:{}] {}",                                                          \
        GetFileName(__FILE__),                                                 \
        __LINE__,                                                              \
        __LogFormat(fmt, ##__VA_ARGS__))

} // namespace fotamaster
} // namespace seres
