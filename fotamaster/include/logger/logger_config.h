#pragma once

#include "nlohmann/json.hpp"
#include <cstdint>
#include <string>

namespace seres
{
namespace fotamaster
{

namespace ns
{

using Json = nlohmann::json;
// using Json = nlohmann::ordered_json;  // ordered_json 不自动排序

struct LoggerConfig
{
    std::string log_file_path{""};
    size_t max_file_size{0ul};
    size_t max_file_count{0ul};
    uint8_t log_level{0x0};
    bool enable_console{false};
};

static const std::string kLoggerConfigLabel{"logger_config"};

inline void from_json(const Json &j, LoggerConfig &v)
{
    j.at("log_file_path").get_to(v.log_file_path);
    j.at("log_max_file_size").get_to(v.max_file_size);
    j.at("log_max_file_count").get_to(v.max_file_count);
    j.at("log_level").get_to(v.log_level);
    j.at("enable_console").get_to(v.enable_console);
}

} // namespace ns

class LogRecoderConfig
{
public:
    explicit LogRecoderConfig(std::string&& config_path);
    bool GetLogConfig(ns::LoggerConfig& log_config);

private:
    std::string config_path_{};
};

} // namespace fotamaster
} // namespace seres