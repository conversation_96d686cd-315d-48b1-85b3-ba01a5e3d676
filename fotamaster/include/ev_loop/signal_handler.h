#pragma once

#include "base/noncopyable.h"
#include "ev_loop/error_code.h"
#include <memory>
#include <optional>
#include <unordered_map>

struct ev_signal; // from libev
struct ev_loop;   // from libev

namespace seres
{
namespace fotamaster
{

class EventLoop;

class SignalHandler : base::Noncopyable
{
public:
    /// @brief  Construct a new SignalHandler object
    ///
    /// @param  loop
    ///
    /// @note Only the DefaultEventLoop is used to handle signal
    ///
    explicit SignalHandler(EventLoop *loop);

    virtual ~SignalHandler();

    /// @brief
    ///
    /// @param  signal - see signal.h
    /// @return std::optional<ErrCode> 失败时有错误码值，成功没有值
    ///
    /// @note must be called in EventLoop's thread
    ///
    std::optional<ErrCode> RegisterSignal(int signal) noexcept;

    ///
    /// @brief
    /// @note must be called in EventLoop's thread
    ///
    void UnregisterSignal(int signal) noexcept;

    /// @brief
    ///
    /// @param	singal
    ///
    virtual void HandleSignal(int signal) noexcept = 0;

    EventLoop *loop() const noexcept
    {
        return loop_;
    }

private:
    /// @brief
    ///
    /// @param	backend
    /// @param	watcher
    ///
    static void SignalCallback(struct ev_loop *backend,
                               struct ev_signal *watcher,
                               int);

    EventLoop *loop_{nullptr};
    std::unordered_map<int, std::unique_ptr<ev_signal>> signal_watchers_;
};

} // namespace fotamaster
} // namespace seres
