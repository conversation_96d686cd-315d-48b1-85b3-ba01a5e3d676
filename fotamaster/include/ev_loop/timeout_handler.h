#pragma once

#include "base/noncopyable.h"
#include "ev_loop/error_code.h"
#include <memory>
#include <optional>

struct ev_loop;  // from libev
struct ev_timer; // from libev

namespace seres
{
namespace fotamaster
{

class EventLoop;

class TimeoutHandler : base::Noncopyable
{
public:
    explicit TimeoutHandler(EventLoop *loop);
    virtual ~TimeoutHandler();

    std::optional<ErrCode> RegisterTimeout(double value, bool is_one_shot = true) noexcept;

    ///
    /// @brief
    /// @note must be called in EventLoop's thread
    ///
    void UnregisterTimeout() noexcept;

    bool IsRegistered() const noexcept;

    EventLoop *loop() const noexcept
    {
        return loop_;
    }

    virtual void OnTimeout() = 0;

private:
    /// @brief
    ///
    /// @param	backend
    /// @param	watcher
    ///
    static void TimeoutCallback(struct ev_loop *backend,
                                struct ev_timer *watcher,
                                int);

    EventLoop *loop_{nullptr};
    std::unique_ptr<struct ev_timer> timer_watcher_{nullptr};
};

} // namespace fotamaster
} // namespace seres
