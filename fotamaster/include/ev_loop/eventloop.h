#pragma once

#include <atomic>
#include <cstddef>
#include <list>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>

#include "base/noncopyable.h"
#include <functional>
#include <string>

struct ev_loop;
struct ev_async;

namespace seres
{
namespace fotamaster
{

class EventLoopManager;

class EventLoop : base::Noncopyable
{
public:
    using Func = std::function<void()>;

    explicit EventLoop(const std::string &name, bool default_loop = false);
    ~EventLoop();

    /// @brief  Get the backend of *this loop
    ///
    /// @return struct ev_loop*
    ///
    struct ev_loop *GetEventLoopBackend() const noexcept
    {
        return event_backend_;
    }

    const std::string &name() const noexcept
    {
        return name_;
    }

    /// @brief  Get the thread id of *this loop.
    ///
    /// @return std::thread::id
    ///
    std::thread::id loop_thread() const noexcept
    {
        return loop_thread_.load(std::memory_order_relaxed);
    }

    std::size_t LoopOnce() noexcept;

    /// @brief  Run *this loop in std::this_thread.
    ///
    /// @note This function will only return after TerminateLoop() has been called.
    ///
    void LoopForever();

    /// @brief  Wait unitl *this loop has been running.
    ///
    ///
    void WaitUntilLoopRunning();

    /// @brief  Terminate *this loop
    ///
    ///
    void TerminateLoop();

    /// @brief  Check if *this loop is default loop.
    ///
    /// @return true
    /// @return false
    ///
    bool IsDefaultLoop() const noexcept;

    /// @brief  Check if *this loop is running
    ///
    /// @return true
    /// @return false
    ///
    bool IsRunning() const noexcept
    {
        return loop_thread() != std::thread::id();
    }

    bool IsInLoopThread() const noexcept
    {
        auto id = loop_thread();
        return id == std::thread::id() || id == std::this_thread::get_id();
    }

    /// @brief  Check if we are in the thread running *this loop
    ///
    /// @return true
    /// @return false
    ///
    bool IsInRunningLoopThread() const noexcept
    {
        return loop_thread() == std::this_thread::get_id();
    }

    /// @brief  Run a job immediately if *this loop is not running or we are in
    ///         its thread, otherwise the job will be enqueued.
    ///
    /// @param  job
    ///
    /// @note The caller ensures that *this loop's state (running or not) is NOT changed before
    ///       this function return. If not ensured, use RunInRunningLoopThread() instead.
    ///
    void RunInLoopThread(Func job) noexcept;

    /// @brief  Run a job in *this loop.
    ///
    /// If we are in *this loop's thread and the always_enqueue is false,
    /// the job will run immediately.
    ///
    /// @param  job
    /// @param  always_enqueue  - the job is always enqueued, even if we are in *this loop's thread.
    ///
    void RunInRunningLoopThread(Func job, bool always_enqueue = false) noexcept;

    /// @brief  Gets the number of queued jobs waiting to be handled by *this loop.
    ///
    /// @return std::size_t
    ///
    std::size_t GetJobsQueueSize() const noexcept;

    /// @brief  Gets the number of handled jobs that has been handled by *this loop.
    ///
    /// @return std::size_t
    ///
    std::size_t GetHandledJobSize() const noexcept;

    /// @brief  Set the max number of queued jobs that can be handled in one round.
    ///
    /// If this value is set to ZERO, all queued jobs will be handled in one round.
    ///
    /// @param  max_jobs
    ///
    /// @note   The default value is 16.
    void SetMaxJobsAtOnce(std::size_t max_jobs);

private:
    friend class EventLoopManager;

    class JobsQueue;

    std::string name_;
    struct ev_loop *event_backend_;
    std::atomic<std::thread::id> loop_thread_;
    std::unique_ptr<JobsQueue> jobs_queue_;
};

} // namespace fotamaster
} // namespace seres
