// GenericDdsWrapper.hpp
#pragma once
#include "base/noncopyable.h"
#include "dds/dds.hpp"
#include <functional>
#include <memory>
#include <mutex>
#include <optional>
#include <stdexcept>
#include <type_traits>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{
namespace dds_wrapper
{

// TODO 增加QoS config
struct DdsQosConfig
{
    bool use_qos{false};
    dds::core::Duration deadline{dds::core::Duration::infinite()};
    dds::core::Duration liveliness_lease_duration{
        dds::core::Duration::infinite()};
    dds::core::policy::ReliabilityKind reliability{
        dds::core::policy::ReliabilityKind::RELIABLE};
    dds::core::policy::DurabilityKind durability{
        dds::core::policy::DurabilityKind::VOLATILE};
    dds::core::policy::HistoryKind history{
        dds::core::policy::HistoryKind::KEEP_LAST};
    int32_t history_depth{1};
};

static void ApplyDataReaderQosConfig(dds::sub::qos::DataReaderQos &qos,
                            const DdsQosConfig &config)
{
    // QoS配置实现...
}

static void ApplyDataWriterQosConfig(dds::pub::qos::DataWriterQos &qos,
                            const DdsQosConfig &config)
{
    // QoS配置实现...
}

// ==================== 类型特征模板 ====================
template <typename DataUnion, typename TopicEnum>
struct DdsTypeTraits
{
    // 必须实现的静态方法
    template <typename T>
    static constexpr TopicEnum type_to_enum();

    template <typename T>
    static const auto &get_value(const DataUnion &data);

    template <typename T>
    static void set_value(DataUnion &data, const T &value);
};

// ==================== 通用结果类型 ====================
template <typename T>
struct DdsResult
{
    static_assert(!std::is_same_v<T, void>,
                  "DdsResult<T> cannot use T = void. Use DdsResult<void> "
                  "specialization.");

    std::optional<T> value;
    std::string error_msg;

    static DdsResult Success(T val)
    {
        DdsResult res;
        res.value = std::move(val);
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return value.has_value();
    }
    T &GetValue()
    {
        return *value;
    }
    const T &GetValue() const
    {
        return *value;
    }
};

template <>
struct DdsResult<void>
{
    bool has_value{false};
    std::string error_msg;

    static DdsResult Success()
    {
        DdsResult res;
        res.has_value = true;
        return res;
    }

    static DdsResult Failure(std::string &&msg)
    {
        DdsResult res;
        res.has_value = false;
        res.error_msg = std::move(msg);
        return res;
    }

    explicit operator bool() const
    {
        return has_value;
    }
};

// ==================== 通用Subscriber ====================
template <typename DataUnion,
          typename TopicEnum,
          typename Traits = DdsTypeTraits<DataUnion, TopicEnum>>
class UnionSubscriber : base::Noncopyable
{
public:
    template <typename T>
    using DataCallback = std::function<void(const T &)>;
    using CallbackMap =
        std::unordered_map<TopicEnum, std::function<void(const DataUnion &)>>;
    static DdsResult<std::shared_ptr<UnionSubscriber>> Create(
        uint32_t domain_id,
        const std::string &topic_name,
        const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::sub::Subscriber subscriber(participant);
            dds::topic::Topic<DataUnion> topic(participant, topic_name);
            // 配置QoS
            auto qos = subscriber.default_datareader_qos();
            ApplyDataReaderQosConfig(qos, qos_config);

            auto *instance = new UnionSubscriber(std::move(participant),
                                                 std::move(subscriber),
                                                 std::move(topic),
                                                 std::move(qos));
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Success(
                std::shared_ptr<UnionSubscriber>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Failure(
                "DDS error: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<UnionSubscriber>>::Failure(
                "System error: " + std::string(e.what()));
        }
    }

    template <typename T>
    DdsResult<bool> Subscribe(DataCallback<T> callback)
    {
        static_assert(IsSupportedType<T>(), "Unsupported message type");

        const auto topic_enum = Traits::template type_to_enum<T>();
        std::lock_guard<std::mutex> lock(mutex_);

        if (callbacks_.count(topic_enum))
        {
            return DdsResult<bool>::Failure("Type already subscribed");
        }

        callbacks_.emplace(topic_enum,
                           [cb = std::move(callback)](const DataUnion &data) {
                               cb(Traits::template get_value<T>(data));
                           });

        return DdsResult<bool>::Success(true);
    }

    DdsResult<bool> IsConnected()
    {
        try
        {
            auto status = reader_.subscription_matched_status();
            return DdsResult<bool>::Success(status.current_count() > 0);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<int> GetMatchedPublishersCount()
    {
        try
        {
            auto status = reader_.subscription_matched_status();
            return DdsResult<int>::Success(status.current_count());
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<int>::Failure("DDS error: " +
                                           std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<int>::Failure("System error: " +
                                           std::string(e.what()));
        }
    }

    ~UnionSubscriber()
    {
        reader_.listener(nullptr, dds::core::status::StatusMask::none());
        reader_.close();
    }

private:
    UnionSubscriber(dds::domain::DomainParticipant &&participant,
                    dds::sub::Subscriber &&subscriber,
                    dds::topic::Topic<DataUnion> &&topic,
                    dds::sub::qos::DataReaderQos &&qos)
        : participant_(std::move(participant)),
          subscriber_(std::move(subscriber)), topic_(std::move(topic)),
          reader_(subscriber_, topic_, qos),
          listener_(std::make_unique<Listener>(callbacks_, mutex_))
    {
        reader_.listener(listener_.get(),
                         dds::core::status::StatusMask::data_available());
    }

    template <typename T>
    static constexpr bool IsSupportedType()
    {
        // return std::is_same_v<T,
        //                       decltype(Traits::template get_value<T>(
        //                           std::declval<DataUnion>()))>;
        using ReturnType =
            decltype(Traits::template get_value<T>(std::declval<DataUnion>()));
        // 移除引用和cv限定符进行比较
        return std::is_same_v<std::decay_t<ReturnType>, std::decay_t<T>>;
    }

    class Listener : public dds::sub::NoOpDataReaderListener<DataUnion>
    {
    public:
        Listener(CallbackMap &callbacks, std::mutex &mutex)
            : callbacks_(callbacks), mutex_(mutex)
        {
        }

        void on_data_available(dds::sub::DataReader<DataUnion> &reader) override
        {
            dds::sub::LoanedSamples<DataUnion> samples = reader.take();
            for (const auto &sample : samples)
            {
                if (sample.info().valid())
                {
                    const auto &data = sample.data();
                    std::lock_guard<std::mutex> lock(mutex_);
                    if (auto it = callbacks_.find(data._d());
                        it != callbacks_.end())
                    {
                        it->second(data);
                    }
                }
            }
        }

    private:
        CallbackMap &callbacks_;
        std::mutex &mutex_;
    };

    dds::domain::DomainParticipant participant_;
    dds::sub::Subscriber subscriber_;
    dds::topic::Topic<DataUnion> topic_;
    dds::sub::DataReader<DataUnion> reader_;
    std::unique_ptr<Listener> listener_;

    std::mutex mutex_;
    CallbackMap callbacks_;
};

// ==================== 通用Publisher ====================
template <typename DataUnion,
          typename TopicEnum,
          typename Traits = DdsTypeTraits<DataUnion, TopicEnum>>
class UnionPublisher : base::Noncopyable
{
public:
    static DdsResult<std::shared_ptr<UnionPublisher>> Create(
        uint32_t domain_id,
        const std::string &topic_name,
        const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::pub::Publisher publisher(participant);
            dds::topic::Topic<DataUnion> topic(participant, topic_name);

            // 配置QoS
            auto qos = publisher.default_datawriter_qos();
            ApplyDataWriterQosConfig(qos, qos_config);
            dds::pub::DataWriter<DataUnion> writer(publisher, topic, qos);

            auto *instance = new UnionPublisher(std::move(participant),
                                                std::move(publisher),
                                                std::move(topic),
                                                std::move(writer));
            return DdsResult<std::shared_ptr<UnionPublisher>>::Success(
                std::shared_ptr<UnionPublisher>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<UnionPublisher>>::Failure(
                "DDS error: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<UnionPublisher>>::Failure(
                "System error: " + std::string(e.what()));
        }
    }

    template <typename T>
    DdsResult<bool> Publish(const T &msg)
    {
        static_assert(IsSupportedType<T>(), "Unsupported message type");

        DataUnion data;
        try
        {
            Traits::template set_value<T>(data, msg);
            writer_.write(data);
            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<bool> IsConnected()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<bool>::Success(status.current_count() > 0);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS error: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("System error: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<int> GetMatchedSubscribersCount()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<int>::Success(status.current_count());
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<int>::Failure("DDS error: " +
                                           std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<int>::Failure("System error: " +
                                           std::string(e.what()));
        }
    }

private:
    template <typename T>
    static constexpr bool IsSupportedType()
    {
        // return std::is_same_v<T,
        //                       decltype(Traits::template get_value<T>(
        //                           std::declval<DataUnion>()))>;
        using ReturnType =
            decltype(Traits::template get_value<T>(std::declval<DataUnion>()));
        // 移除引用和cv限定符进行比较
        return std::is_same_v<std::decay_t<ReturnType>, std::decay_t<T>>;
    }

    UnionPublisher(dds::domain::DomainParticipant &&participant,
                   dds::pub::Publisher &&publisher,
                   dds::topic::Topic<DataUnion> &&topic,
                   dds::pub::DataWriter<DataUnion> &&writer)
        : participant_(std::move(participant)),
          publisher_(std::move(publisher)), topic_(std::move(topic)),
          writer_(std::move(writer))
    {
    }

    dds::domain::DomainParticipant participant_;
    dds::pub::Publisher publisher_;
    dds::topic::Topic<DataUnion> topic_;
    dds::pub::DataWriter<DataUnion> writer_;
};

template <typename MsgT>
class Subscriber : base::Noncopyable
{
public:
    using DataAvailableCallback = std::function<void(const MsgT &)>;
    using IncompatibleQosCallback = std::function<void(
        const dds::core::status::RequestedIncompatibleQosStatus &)>;

    static DdsResult<std::shared_ptr<Subscriber>> Create(
        uint32_t domain_id,
        const std::string &topic_name,
        DataAvailableCallback data_callback,
        IncompatibleQosCallback qos_callback = nullptr,
        const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::sub::Subscriber subscriber(participant);

            // 配置QoS
            auto qos = subscriber.default_datareader_qos();
            ApplyDataReaderQosConfig(qos, qos_config);

            // 创建监听器
            auto listener =
                std::make_unique<DataReaderListener>(std::move(data_callback),
                                                     std::move(qos_callback));

            // 创建主题和DataReader
            dds::topic::Topic<MsgT> topic(participant, topic_name);
            dds::sub::DataReader<MsgT> reader(subscriber, topic, qos);

            // 设置监听器
            dds::core::status::StatusMask mask =
                dds::core::status::StatusMask::data_available();
            if (listener->HasQosCallback())
            {
                mask |=
                    dds::core::status::StatusMask::requested_incompatible_qos();
            }
            reader.listener(listener.get(), mask);

            // 创建实例
            auto *instance = new Subscriber(std::move(participant),
                                            std::move(subscriber),
                                            std::move(topic),
                                            std::move(reader),
                                            std::move(listener));

            return DdsResult<std::shared_ptr<Subscriber>>::Success(
                std::shared_ptr<Subscriber>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<Subscriber>>::Failure(
                "DDS exception: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<Subscriber>>::Failure(
                "Standard exception: " + std::string(e.what()));
        }
    }

    ~Subscriber()
    {
        reader_.listener(nullptr, dds::core::status::StatusMask::none());
        reader_.close();
    }

private:
    struct DataReaderListener : public dds::sub::NoOpDataReaderListener<MsgT>
    {
        DataAvailableCallback data_cb_;
        IncompatibleQosCallback qos_cb_;

        DataReaderListener(DataAvailableCallback data_cb,
                           IncompatibleQosCallback qos_cb)
            : data_cb_(std::move(data_cb)), qos_cb_(std::move(qos_cb))
        {
        }

        bool HasQosCallback() const
        {
            return static_cast<bool>(qos_cb_);
        }

        void on_data_available(dds::sub::DataReader<MsgT> &reader) override
        {
            dds::sub::LoanedSamples<MsgT> samples = reader.take();
            for (const auto &sample : samples)
            {
                if (sample.info().valid() && data_cb_)
                {
                    data_cb_(sample.data());
                }
            }
        }

        void on_requested_incompatible_qos(
            dds::sub::DataReader<MsgT> &reader,
            const dds::core::status::RequestedIncompatibleQosStatus &status)
            override
        {
            if (qos_cb_)
            {
                qos_cb_(status);
            }
        }
    };

    Subscriber(dds::domain::DomainParticipant &&participant,
               dds::sub::Subscriber &&subscriber,
               dds::topic::Topic<MsgT> &&topic,
               dds::sub::DataReader<MsgT> &&reader,
               std::unique_ptr<DataReaderListener> &&listener)
        : participant_(std::move(participant)),
          subscriber_(std::move(subscriber)), topic_(std::move(topic)),
          reader_(std::move(reader)), listener_(std::move(listener))
    {
    }

    dds::domain::DomainParticipant participant_;
    dds::sub::Subscriber subscriber_;
    dds::topic::Topic<MsgT> topic_;
    dds::sub::DataReader<MsgT> reader_;
    std::unique_ptr<DataReaderListener> listener_;
};

template <typename MsgT>
class Publisher : base::Noncopyable
{
public:
    static DdsResult<std::shared_ptr<Publisher>> Create(
        uint32_t domain_id,
        const std::string &topic_name,
        const DdsQosConfig &qos_config = DdsQosConfig{})
    {
        try
        {
            dds::domain::DomainParticipant participant(domain_id);
            dds::pub::Publisher publisher(participant);

            // 配置QoS
            auto qos = publisher.default_datawriter_qos();
            ApplyDataWriterQosConfig(qos, qos_config);

            // 创建主题和DataWriter
            dds::topic::Topic<MsgT> topic(participant, topic_name);
            dds::pub::DataWriter<MsgT> writer(publisher, topic, qos);

            auto *instance = new Publisher(std::move(participant),
                                           std::move(publisher),
                                           std::move(topic),
                                           std::move(writer));

            return DdsResult<std::shared_ptr<Publisher>>::Success(
                std::shared_ptr<Publisher>(instance));
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<std::shared_ptr<Publisher>>::Failure(
                "DDS exception: " + std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<std::shared_ptr<Publisher>>::Failure(
                "Standard exception: " + std::string(e.what()));
        }
    }

    DdsResult<bool> Publish(const MsgT &msg)
    {
        try
        {
            writer_.write(msg);
            return DdsResult<bool>::Success(true);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS exception: " +
                                            std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<bool>::Failure("Standard exception: " +
                                            std::string(e.what()));
        }
    }

    DdsResult<bool> IsConnected()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<bool>::Success(status.current_count() > 0);
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<bool>::Failure("DDS exception: " +
                                            std::string(e.what()));
        }
    }


    DdsResult<int> GetMatchedSubscribersCount()
    {
        try
        {
            auto status = writer_.publication_matched_status();
            return DdsResult<int>::Success(status.current_count());
        }
        catch (const dds::core::Exception &e)
        {
            return DdsResult<int>::Failure("DDS error: " +
                                           std::string(e.what()));
        }
        catch (const std::exception &e)
        {
            return DdsResult<int>::Failure("System error: " +
                                           std::string(e.what()));
        }
    }

    ~Publisher()
    {
        writer_.close();
    }

private:
    Publisher(dds::domain::DomainParticipant &&participant,
              dds::pub::Publisher &&publisher,
              dds::topic::Topic<MsgT> &&topic,
              dds::pub::DataWriter<MsgT> &&writer)
        : participant_(std::move(participant)),
          publisher_(std::move(publisher)), topic_(std::move(topic)),
          writer_(std::move(writer))
    {
    }

    dds::domain::DomainParticipant participant_;
    dds::pub::Publisher publisher_;
    dds::topic::Topic<MsgT> topic_;
    dds::pub::DataWriter<MsgT> writer_;
};

} // namespace dds_wrapper
} // namespace fotamaster
} // namespace seres