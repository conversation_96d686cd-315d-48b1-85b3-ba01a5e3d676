#pragma once

#include "HPCC_HMI_OTA_Control.hpp"
#include "dds_service_manager/dds_wapper.hpp"

namespace seres
{
namespace fotamaster
{
namespace dds_wrapper
{

using namespace Seres::HPCC_HMI_OTA;

template <>
struct DdsTypeTraits<HPCC_HMI_OTA_Control, int32_t>
{
    template <typename T>
    static constexpr auto type_to_enum()
    {
        if constexpr (std::is_same_v<T, HMI_SeamlessUpgradeSet_OTA_In>)
            return 85448782;
        else if constexpr (std::is_same_v<T, HMI_AutoUpdateSet_OTA_In>)
            return -663628734;
        else if constexpr (std::is_same_v<T, HMI_UpdateModeCtrl_OTA_In>)
            return -1515254661;
        else if constexpr (std::is_same_v<T, HMI_UpdateCtrl_OTA_In>)
            return -154233906;
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }

    template <typename T>
    static const auto &get_value(const HPCC_HMI_OTA_Control &data)
    {
        if constexpr (std::is_same_v<T, HMI_SeamlessUpgradeSet_OTA_In>)
            return data.HMI_SeamlessUpgradeSet_OTA();
        else if constexpr (std::is_same_v<T, HMI_AutoUpdateSet_OTA_In>)
            return data.HMI_AutoUpdateSet_OTA();
        else if constexpr (std::is_same_v<T, HMI_UpdateModeCtrl_OTA_In>)
            return data.HMI_UpdateModeCtrl_OTA();
        else if constexpr (std::is_same_v<T, HMI_UpdateCtrl_OTA_In>)
            return data.HMI_UpdateCtrl_OTA();
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }

    template <typename T>
    static void set_value(HPCC_HMI_OTA_Control &data, const T &value)
    {
        if constexpr (std::is_same_v<T, HMI_SeamlessUpgradeSet_OTA_In>)
            data.HMI_SeamlessUpgradeSet_OTA(value);
        else if constexpr (std::is_same_v<T, HMI_AutoUpdateSet_OTA_In>)
            data.HMI_AutoUpdateSet_OTA(value);
        else if constexpr (std::is_same_v<T, HMI_UpdateModeCtrl_OTA_In>)
            data.HMI_UpdateModeCtrl_OTA(value);
        else if constexpr (std::is_same_v<T, HMI_UpdateCtrl_OTA_In>)
            data.HMI_UpdateCtrl_OTA(value);
        else
            static_assert(always_false_v<T>, "Unsupported type");
    }
private:
    template <class T>
    static constexpr bool always_false_v = false;
};

using HPCC_HMI_OTA_ControlSubscriber = UnionSubscriber<HPCC_HMI_OTA_Control, int32_t>;
using HPCC_HMI_OTA_ControlPublisher = UnionPublisher<HPCC_HMI_OTA_Control, int32_t>;

}
}
}