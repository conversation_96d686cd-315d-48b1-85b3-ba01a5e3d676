#pragma once

#include "dds/dds.hpp"
#include "logger/logger.h"
#include "rpc/OTA_DucInterface_gen_client.hpp"
#include <atomic>
#include <condition_variable>
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <unordered_map>

using namespace seres::ota_duc_service;

namespace seres
{
namespace fotamaster
{

// 服务状态回调函数类型定义
using ServiceStatusCallback = std::function<void(DUCType, bool)>;


// RPC客户端
class DUCServiceClient
    : public dds::pub::NoOpDataWriterListener<DucServiceInterface_Request>,
      dds::sub::NoOpDataReaderListener<DucServiceInterface_Reply>
{
public:
    DUCServiceClient(
        DUCType type,
        const std::string &serviceName,
        const dds::domain::DomainParticipant &participant,
        ServiceStatusCallback &callback,
        dds::pub::qos::DataWriterQos dwQos = dds::pub::qos::DataWriterQos(),
        dds::sub::qos::DataReaderQos drQos = dds::sub::qos::DataReaderQos());
    ~DUCServiceClient();

    bool waitForService();
    bool isConnected() const;
    DucServiceInterfaceClient *getClient();

private:
    void on_publication_matched(
        dds::pub::DataWriter<DucServiceInterface_Request> &writer,
        const dds::core::status::PublicationMatchedStatus &status) override;

    void on_subscription_matched(
        dds::sub::DataReader<DucServiceInterface_Reply> &reader,
        const dds::core::status::SubscriptionMatchedStatus &status) override;

    void on_liveliness_changed(
        dds::sub::DataReader<DucServiceInterface_Reply> &reader,
        const dds::core::status::LivelinessChangedStatus &status) override;

    DUCType m_type;
    std::string m_serviceName;
    ServiceStatusCallback m_callback;
    std::unique_ptr<DucServiceInterfaceClient> m_client;
    std::atomic<bool> m_connected;
    std::mutex m_mutex;
};

} // namespace fotamaster
} // namespace seres