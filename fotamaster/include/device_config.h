#pragma once

#include "nlohmann/json.hpp"
#include <cstdint>
#include <string>
#include <vector>

namespace seres
{
namespace fotamaster
{

namespace ns
{

using Json = nlohmann::json;
// using Json = nlohmann::ordered_json;  // ordered_json 不自动排序

struct DeviceListConfig
{
    std::vector<std::string> cdc_dev_list;
    std::vector<std::string> mdc_dev_list;
    std::vector<std::string> zcu_dev_list;
};

static const std::string kDevListsConfigLabel{"DEV_LISTS"};

inline void from_json(const Json &j, DeviceListConfig &v)
{
    j.at("CDC_DEV").get_to(v.cdc_dev_list);
    j.at("MDC_DEV").get_to(v.mdc_dev_list);
    j.at("ZCU_DEV").get_to(v.zcu_dev_list);
}

} // namespace ns

class DeviceListRecoderConfig
{
public:
    explicit DeviceListRecoderConfig(std::string &&config_path);
    bool GetDeviceListConfig(ns::DeviceListConfig &dev_config);

private:
    std::string config_path_{};
};

} // namespace fotamaster
} // namespace seres
