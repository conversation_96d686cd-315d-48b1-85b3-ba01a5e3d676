#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/timer.h"
#include "logger/logger.h"

#include <atomic>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

using namespace seres::ota_duc_service;

// 进度回调函数类型
using DownloadOverallProgressCallback =
    std::function<void(uint8_t progress,
                       uint32_t allPakageSize,
                       float speed,
                       uint16_t remainingTime)>;

class DownloadProgressManager : public base::Noncopyable
{
public:
    DownloadProgressManager() = default;
    ~DownloadProgressManager();

    bool Initialize(DownloadOverallProgressCallback progressCallback,
                    double reportInterval = 1.0,
                    bool needSub = false);

    bool AddDownloadPackage(
        DUCType ducType,
        const std::vector<DownloadTaskInfo> &download_tasks);

    void onDownloadProgress(DUCType ducType, const DownloadProgress &progress);

    bool Start();

    void Stop();

    void Reset();

private:
    bool subscribeTopic();

    void onTimerCallback();

    // 计算下载速度和剩余时间
    void calculateSpeedAndRemainingTime();

    // 解析包大小字符串为数字
    uint64_t parsePackageSize(const std::string &sizeStr);

private:
    // 初始化状态
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_started{false};

    std::atomic<uint8_t> m_Progress = 0;        //进度
    std::atomic<uint64_t> m_downloadedSize = 0; //已下载大小
    std::atomic<uint64_t> m_totalSize = 0;      //总大小

    // 速度计算相关
    std::atomic<uint32_t> m_currentSpeed = 0;  // 当前下载速度 (bytes/s)
    std::atomic<uint32_t> m_remainingTime = 0; // 预计剩余时间 (秒)

    // 速度计算相关的历史数据
    std::chrono::steady_clock::time_point m_lastUpdateTime;
    uint64_t m_lastDownloadedSize = 0;

    // 用于平滑速度计算的历史记录（最近几次的速度值）
    static constexpr size_t SPEED_HISTORY_SIZE = 5;
    std::vector<uint32_t> m_speedHistory;
    size_t m_speedHistoryIndex = 0;

    // 各域控当前下载进度
    std::unordered_map<DUCType, DownloadProgress> m_domainProgress;

    std::shared_ptr<Timer> m_timer;
    double m_reportInterval = 1.0; // 1秒报告一次
    DownloadOverallProgressCallback m_DownloadOverallProgressCallback;
    mutable std::mutex m_mutex;
};

} // namespace fotamaster
} // namespace seres