#pragma once

/**
 * @file class_factory.h
 * @brief
 */

#include "base/noncopyable.h"
#include "base/singleton.h"
#include <cassert>
#include <functional>
#include <mutex>
#include <string>
#include <type_traits>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{
namespace base
{

/// @brief The singleton class to manage the class fatories.
class ClassFactoryManager : Noncopyable
{
public:
    using FactoryType = std::function<void *()>;
    using ObjectMap = std::unordered_map<std::string, FactoryType>;

    /// @brief Register a class factory for \e Type.
    /// @tparam Type The derived class.
    /// @tparam Base The base class.
    /// @param name The class name in string.
    template <typename Type, typename Base>
    void RegisterClass(const std::string &name)
    {
        static_assert(std::is_base_of<Base, Type>::value,
                      "type is not derived from base");
        if (IsClassRegistered<Base>(name))
        {
            assert(0);
        }
        std::lock_guard<std::mutex> lock(lock_);
        if (factories_.find(typeid(Base).name()) == factories_.end())
        {
            factories_.emplace(typeid(Base).name(), ObjectMap{});
        }
        factories_.at(typeid(Base).name()).emplace(name, []() -> void * {
            return static_cast<Base *>(new Type());
        });
    }
    /// @brief Create a object through the class name.
    /// @tparam Base The base class type.
    /// @param name The class name.
    /// @return Base* The pointer to the object.
    template <typename Base>
    Base *CreateObject(const std::string &name)
    {
        if (!IsClassRegistered<Base>(name))
        {
            return nullptr;
        }
        std::lock_guard<std::mutex> lock(lock_);
        return reinterpret_cast<Base *>(
            factories_.at(typeid(Base).name()).at(name)());
    }
    /// @brief Get the list of registered class.
    /// @tparam Base The base class type.
    /// @return std::vector<std::string> The class name list.
    template <typename Base>
    std::vector<std::string> GetRegisteredClasses()
    {
        std::lock_guard<std::mutex> lock(lock_);
        std::vector<std::string> registered_classes;
        auto base_classes = factories_.find(typeid(Base).name());
        if (base_classes != factories_.end())
        {
            for (auto &factory : base_classes->second)
            {
                registered_classes.push_back(factory.first);
            }
        }
        return registered_classes;
    }
    /// @brief Check if the class is registered.
    /// @tparam Base The base class type.
    /// @param name The class name.
    /// @return True if the class is registered.
    template <typename Base>
    bool IsClassRegistered(const std::string &name)
    {
        std::lock_guard<std::mutex> lock(lock_);
        auto base_classes = factories_.find(typeid(Base).name());
        if (base_classes == factories_.end())
        {
            return false;
        }
        auto factory = base_classes->second.find(name);
        return factory != base_classes->second.end();
    }

private:
    std::mutex lock_;
    /// @brief A map of class name and class factory.
    std::unordered_map<std::string, ObjectMap> factories_;
};

#define __REGISTER_CLASS_INTERNAL__(Name, Type, Base, UniqueID)                \
    namespace                                                                  \
    {                                                                          \
    struct TemporaryType##UniqueID                                             \
    {                                                                          \
        TemporaryType##UniqueID()                                              \
        {                                                                      \
            using namespace seres::fotamaster::base;                           \
            Singleton<ClassFactoryManager>::Instance()                         \
                .RegisterClass<Type, Base>(Name);                              \
        }                                                                      \
    };                                                                         \
    static TemporaryType##UniqueID g_register_class_##UniqueID;                \
    }
#define REGISTER_CLASS_INTERNAL(Name, Type, Base, UniqueID)                    \
    __REGISTER_CLASS_INTERNAL__(Name, Type, Base, UniqueID)
#define REGISTER_CLASS(Name, Type, Base)                                       \
    REGISTER_CLASS_INTERNAL(Name, Type, Base, __COUNTER__)

} // namespace base
} // namespace fotamaster
} // namespace seres
