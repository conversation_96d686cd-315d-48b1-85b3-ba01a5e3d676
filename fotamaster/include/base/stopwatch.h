#pragma once

#include <chrono>
#include <iostream>
#include "base/noncopyable.h"

namespace seres
{
namespace fotamaster
{
namespace base
{

class Stopwatch : Noncopyable
{
public:
    using Clock = std::chrono::steady_clock;
    Stopwatch() : m_start(Clock::now()) {}

    /// @brief Elapsed
    /// @return elapsed ms
    uint64_t ElapsedMS()
    {
        auto diff = Clock::now() - m_start;
        return static_cast<uint64_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(diff).count());
    }

private:
    // std::chrono::time_point<Clock> start_; // or
    Clock::time_point m_start;
};

}  // namespace base
}  // namespace fotamaster
}  // namespace seres
