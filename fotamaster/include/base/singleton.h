#pragma once

#include <utility>
#include "base/noncopyable.h"

namespace seres
{
namespace fotamaster
{
namespace base
{

template <typename T>
class Singleton : public Noncopyable
{
public:
    template <typename... Args>
    static T& Instance(Args&&... args)
    {
        static T instance(std::forward<Args>(args)...);  // C++11以后是线程安全的
        return instance;
    }
};

}  // namespace base
}  // namespace fotamaster
}  // namespace seres
