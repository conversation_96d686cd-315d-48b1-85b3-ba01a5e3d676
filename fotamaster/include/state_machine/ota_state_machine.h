#pragma once

#include "state_machine/state.h"
#include <cstdint>
#include <memory>
#include <string>
#include <functional>
#include <any>
#include "state_machine/ota_state_msg.h"

namespace seres
{
namespace fotamaster
{

enum class StateEvent : uint8_t
{
    kStartDownload,
    kStartSeamlessUpgrade,
    kCheckPreCondition,
    kStartFormalUpgrade,
    kStartRollback,
    kFaultHandling,
    kExitHandling,
};

class OtaStateSnapshot;

class OtaStateMachine : public StateMachine
{
public:
    using EventMsgCallback = std::function<bool(const StateEvent &, const std::any &)>;
    explicit OtaStateMachine(EventLoop *event_loop);
    virtual ~OtaStateMachine();

    void InitState();

    void ChangeState(const std::string &new_state) override;

    void HandleEvent(const OtaStateMsg &ota_info);

    void RegisterEventHandle(EventMsgCallback &&callback)
    {
        event_cb_ = std::move(callback);
    }

    bool EventCallback(const StateEvent &state, const std::any &data = std::any());

    OtaStateMsg GetStateMsg() const
    {
        return ota_state_msg_;
    }

private:
    EventMsgCallback event_cb_{nullptr};
    std::unique_ptr<OtaStateSnapshot> ota_state_snapshot_{nullptr};
    OtaStateMsg ota_state_msg_;
};

} // namespace fotamaster
} // namespace seres