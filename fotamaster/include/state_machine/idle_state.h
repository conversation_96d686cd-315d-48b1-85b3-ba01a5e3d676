#pragma once


#include "state_machine/state.h"
#include "state_machine/ota_state_msg.h"

namespace seres
{
namespace fotamaster
{

class IdleState : public State
{
public:
    IdleState() = default;
    ~IdleState() {};

    bool Enter() override;
    void Process(const EventData &data) override;
    void Exit() override;
private:
    void HandleRequestEvent(const OtaInState& ota_in_state);
};

} // namespace core
} // namespace elias