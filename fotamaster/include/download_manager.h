#pragma once

#include "base/noncopyable.h"
#include "dds_service_manager/duc_service_manager.h"
#include "progress_fitting/download_progress_fitting.h"
#include <cstdint>
#include <functional>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

class DUCServiceManager;
class EventLoop;

class DownloadManager : base::Noncopyable
{
public:
    using ProgressCallback = DownloadOverallProgressCallback;
    using FailReasonCallback = std::function<void(const uint32_t)>;

    /* key: DUCType, value: DownloadTaskLists */
    using DownloadInfoMap = std::unordered_map<DUCType, DownloadTaskLists>;

    /* key: DUCType, value: DownloadConditionLists */
    using DownloadConditionInfoMap =
        std::unordered_map<DUCType, DownloadConditionLists>;

    DownloadManager() = default;
    explicit DownloadManager(DUCServiceManager *duc_service_manager);

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

    void NeedUpgradeDomain(const std::vector<DUCType> &domain_list)
    {
        domain_list_ = domain_list;
    }

    bool CheckDownloadCondition(const DownloadConditionInfoMap &info);

    bool StartDownload(const DownloadInfoMap &info);
    bool StopDownload();
    bool ResumeDownload();
    bool CancelDownload();

private:
    bool Initialize();
    void HandleDownloadProgress(DUCType type, const DownloadProgress &progress);
    bool HandleDownloadStageErr(DUCType type, const CommonStatus &result);
    uint32_t ReasonConvert(DUCType type, uint32_t reason);

private:
    EventLoop *main_loop_{nullptr};
    DUCServiceManager *duc_srv_manager_{nullptr};
    std::unique_ptr<DownloadProgressManager> progress_manager_{nullptr};
    DownloadProgress download_progress_;
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
    std::vector<DUCType> domain_list_;
};

} // namespace fotamaster
} // namespace seres
