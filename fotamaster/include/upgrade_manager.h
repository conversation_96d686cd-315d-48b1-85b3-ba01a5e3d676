#pragma once

#include "OTA_DucData.hpp"
#include "base/noncopyable.h"
#include "progress_fitting/upgrade_progress_fitting.h"
#include <cstdint>
#include <functional>
#include <unordered_map>
#include <vector>

namespace seres
{
namespace fotamaster
{

class DUCServiceManager;
class EventLoop;

class UpgradeManager : base::Noncopyable
{
public:
    using PerProgressInfo = EachUpdateProgressInfo;
    struct ProgressStatus
    {
        uint8_t total_progress{0u};
        bool all_finished{false};
        std::vector<PerProgressInfo> each_progress{};
    };

    using ProgressCallback = std::function<void(const ProgressStatus &)>;
    using FailReasonCallback = std::function<void(const uint32_t)>;

    /* key: DUCType, value: UpdateDeviceList */
    using UpdateInfoMap = std::unordered_map<DUCType, UpdateDeviceList>;

    UpgradeManager() = default;
    explicit UpgradeManager(DUCServiceManager *duc_service_manager);

    bool CheckUpgradeCondition();

    bool StartUpgrade(const UpdateMode &update_mode, const UpdateInfoMap &info);
    bool CancelUpgrade();

    void RegisterProgressCallback(ProgressCallback &&callback)
    {
        progress_callback_ = std::move(callback);
    }

    void RegisterFailReasonCallback(FailReasonCallback &&callback)
    {
        fail_reason_callback_ = std::move(callback);
    }

    void NeedUpgradeDomain(const std::vector<DUCType> &domain_list)
    {
        domain_list_ = domain_list;
    }

private:
    bool Initialize();
    void HandleUpgradeProgress(DUCType type, const UpdateProgress &progress);
    bool HandleUpgradeStageErr(DUCType type, const CommonStatus &result);
    uint32_t ReasonConvert(DUCType type, uint32_t reason);

private:
    EventLoop *main_loop_{nullptr};
    std::unique_ptr<UpgradeProgressManager> progress_manager_{nullptr};
    DUCServiceManager *duc_srv_manager_{nullptr};
    ProgressCallback progress_callback_{nullptr};
    FailReasonCallback fail_reason_callback_{nullptr};
    std::vector<DUCType> domain_list_;
};

} // namespace fotamaster
} // namespace seres