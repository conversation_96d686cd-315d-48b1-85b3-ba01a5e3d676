/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-18 19:12:22.027439
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCC_HMI_OTA_Control.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCC_HMI_OTA_Control.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCC_HMI_OTA {
        typedef boolean UpdateCtrl; // 是否升级控制

        typedef boolean AutoUpdateCmd; // 是否自动更新

        typedef boolean SeamlessUpgradeCmd; // 是否预安装设置

        typedef uint8 Minute; // 分

        typedef uint8 Hour; // 时

        enum DayFlag { // 日标识（今天/明天）
            @value(0x0) TODAY,
            @value(0x1) TOMORROW
        };

        @nested
        struct AppointmentTime { // 预约时间
            DayFlag dayflag; // 今天/明天
            Hour hour; // 时
            Minute minute; // 分
        };

        enum UpdateMode { // 更新模式
            @value(0x0) UPDATE_NONE,
            @value(0x1) UPDATE_IMMEDIATELY,
            @value(0x2) UPDATE_APPOINTMENT,
            @value(0x3) UPDATE_DELAY,
            @value(0x4) UPDATE_AUTO,
            @value(0x5) UPDATE_CANCEL_APPOINTMENT
        };

        @nested
        struct UpdateModeCtrl { // 更新方式控制
            UpdateMode updatemode; // 更新模式
            AppointmentTime appointmenttime; // 预约时间
        };

        @nested
        struct HMI_UpdateModeCtrl_OTA_In {
            UpdateModeCtrl updatemodectrl;
        };

        @nested
        struct HMI_SeamlessUpgradeSet_OTA_In {
            SeamlessUpgradeCmd seamlessupgradecmd;
        };

        @nested
        struct HMI_AutoUpdateSet_OTA_In {
            AutoUpdateCmd autoupdatecmd;
        };

        @nested
        struct HMI_UpdateCtrl_OTA_In {
            UpdateCtrl updatectrl;
        };

        @final
        union HPCC_HMI_OTA_Control switch(int32) {
            // 更新方式控制
            case -1515254661:
                HMI_UpdateModeCtrl_OTA_In HMI_UpdateModeCtrl_OTA;

            // 是否预安装设置
            case 85448782:
                HMI_SeamlessUpgradeSet_OTA_In HMI_SeamlessUpgradeSet_OTA;

            // 是否自动更新设置
            case -663628734:
                HMI_AutoUpdateSet_OTA_In HMI_AutoUpdateSet_OTA;

            // 是否更新控制
            case -154233906:
                HMI_UpdateCtrl_OTA_In HMI_UpdateCtrl_OTA;

        };
    };
};
