/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-18 10:40:53.385779
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/HPCC_OTA_HMI_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/HPCC_OTA_HMI_Status.idl
*/

module Seres {
    module HPCC_OTA_HMI {

        typedef uint32 UpgradeFailStatus; // 升级过程中的失败状态推送

        typedef uint8 Progress; // 进度

        @nested
        struct InstallProgressStatus { // 安装进度状态推送
            Progress progress; // 升级进度
        };

        typedef uint16 InstallPreconditionStatus; // 安装前置条件状态

        typedef uint8 UpgradeCountdown; // 触发升级倒计时

        typedef uint16 RemainTime; // 预计剩余时间

        typedef uint32 TotalSize; // 包总大小

        typedef float Rate; // 速率

        @nested
        struct DownloadProgressStatus { // 下载进度状态推送
            Rate rate; // 下载速率
            TotalSize packagesize; // 升级包总大小
            RemainTime remaintime; // 预计剩余时间
            Progress progress; // 下载进度
        };

        typedef string NewVersionStatus; // 新版本通知

        @final
        struct HPCC_OTA_HMI_Status{
            // 新版本通知
            @optional NewVersionStatus OTAM_notifyNewVersionStatus;

            // 下载进度状态推送
            @optional DownloadProgressStatus OTAM_notifyDownloadProgressStatus;

            // 触发升级倒计时
            @optional UpgradeCountdown OTAM_TriggerUpgradeCountdown;

            // 安装前置条件状态
            @optional InstallPreconditionStatus OTAM_notifyInstallPreconditionStatus;

            // 安装进度状态推送
            @optional InstallProgressStatus OTAM_notifyInstallProgressStatus;

            // 升级过程中的失败状态推送
            @optional UpgradeFailStatus OTAM_notifyUpgradeFailStatus;

        };
    };
};
