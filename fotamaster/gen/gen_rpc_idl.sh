#!/bin/sh

# 检查输入参数是否存在
if [ -z "$1" ]; then
    echo "错误：请提供 .idl 文件名作为参数（如 script.sh example）"
    exit 1
fi

IDL_FILE="$1"
BASENAME="${IDL_FILE%.*}"
OUTPUT="./rpc"

if [ ! -e ${OUTPUT} ];then
    mkdir ${OUTPUT}
fi

# 检查 .idl 文件是否存在
if [ ! -f "$IDL_FILE" ]; then
    echo "错误：找不到文件 $IDL_FILE"
    exit 1
fi

# 执行 idlc 命令
idlc -l cxx -r client "$IDL_FILE" -o "./"
idlc -l cxx -r server "$IDL_FILE" -o "./"
# idlc -l cxx "$IDL_FILE" -o "./"

mv "$BASENAME"*.hpp ./rpc
mv "$BASENAME"*.cpp ./rpc

echo "处理完成！"
exit 0