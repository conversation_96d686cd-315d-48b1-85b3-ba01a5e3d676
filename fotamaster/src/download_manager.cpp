#include "download_manager.h"
#include "base/singleton.h"
#include "ev_loop/eventloop_manager.h"
#include "handle_reason.h"
#include "logger/logger.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

DownloadManager::DownloadManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    main_loop_ = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    assert(main_loop_ != nullptr);

    progress_manager_ = std::make_unique<DownloadProgressManager>();

    auto retval = Initialize();
    assert(retval);
}

bool DownloadManager::Initialize()
{
    //初始化进度拟合管理器
    auto ret = progress_manager_->Initialize([this](uint8_t progress,
                                                    uint64_t allPakageSize,
                                                    float speed,
                                                    uint32_t remainingTime) {
        if (progress_callback_)
        {
            progress_callback_(progress, allPakageSize, speed, remainingTime);
        }
        else
            LOG_ERROR("Download overall progress callback not registered");
    });

    if (!ret)
    {
        LOG_ERROR("ProgressManager Initialize failed");
        return false;
    }
    // 注册下载进度回调
    auto retval = duc_srv_manager_->subscribeDownloadProgress(
        DUCType::CDC,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::CDC, progress);
        });
    retval &= duc_srv_manager_->subscribeDownloadProgress(
        DUCType::MDC,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::MDC, progress);
        });
    retval &= duc_srv_manager_->subscribeDownloadProgress(
        DUCType::ZCU,
        [this](const DownloadProgress &progress) -> void {
            HandleDownloadProgress(DUCType::ZCU, progress);
        });
    return retval;
}

bool DownloadManager::CheckDownloadCondition(
    const DownloadConditionInfoMap &info)
{
    auto CheckDownloadConditionCall =
        [this](DUCType type, const DownloadConditionLists &list) -> bool {
        CommonStatus cond_result;
        auto retval =
            duc_srv_manager_->checkDownloadCondition(type, list, cond_result);
        if (retval != ReturnCode::OK)
        {
            LOG_ERROR("checkDownloadCondition invoke retval: %d",
                      static_cast<int>(retval));
            return false;
        }

        if (!HandleDownloadStageErr(type, cond_result))
        {
            LOG_ERROR("Handle %s download condition failed",
                      ducTypeToString(type).c_str());
            return false;
        }
        return true;
    };

    auto all_ok{true};
    for (const auto &iter : info)
    {
        if (std::find(domain_list_.begin(), domain_list_.end(), iter.first) !=
            domain_list_.end())
        {
            if (!CheckDownloadConditionCall(iter.first, iter.second))
            {
                all_ok = false;
            }
        }
    }

    return all_ok;
}

bool DownloadManager::StartDownload(const DownloadInfoMap &info)
{
    auto StartDownloadCall = [this](DUCType type,
                                    const DownloadTaskLists &list) -> bool {
        // 向进度拟合管理器添加要下载的包
        progress_manager_->AddDownloadPackage(type, list.taskLists());
        // 开始下载通知
        auto retval = duc_srv_manager_->startDownload(type, list);
        LOG_INFO("startDownload %s invoke retval: %d",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(retval));

        return (retval == ReturnCode::OK) ? true : false;
    };

    auto all_ok{true};
    for (const auto &iter : info)
    {
        if (std::find(domain_list_.begin(), domain_list_.end(), iter.first) !=
            domain_list_.end())
        {
            if (!StartDownloadCall(iter.first, iter.second))
            {
                all_ok = false;
            }
        }
    }
    progress_manager_->Start();

    return all_ok;
}

bool DownloadManager::StopDownload()
{
    auto all_ok{true};
    for (const auto &duc_type : domain_list_)
    {
        if (ReturnCode::OK !=
            duc_srv_manager_->downloadCtrl(duc_type, DownloadCtrl::CANCEL))
        {
            all_ok = false;
        }
    }
    return all_ok;
}

bool DownloadManager::ResumeDownload()
{
    auto all_ok{true};
    for (const auto &duc_type : domain_list_)
    {
        if (ReturnCode::OK !=
            duc_srv_manager_->downloadCtrl(duc_type, DownloadCtrl::CANCEL))
        {
            all_ok = false;
        }
    }
    return all_ok;
}

bool DownloadManager::CancelDownload()
{
    auto all_ok{true};
    for (const auto &duc_type : domain_list_)
    {
        if (ReturnCode::OK !=
            duc_srv_manager_->downloadCtrl(duc_type, DownloadCtrl::CANCEL))
        {
            all_ok = false;
        }
    }
    return all_ok;
}

void DownloadManager::HandleDownloadProgress(DUCType type,
                                             const DownloadProgress &progress)
{
    main_loop_->RunInLoopThread([this, type, progress]() -> void {
        assert(main_loop_->IsDefaultLoop());
        LOG_INFO("recv %s download progress info: ",
                 ducTypeToString(type).c_str());
        for (const auto &info : progress.progressLists())
        {
            LOG_INFO("  package name: %s, progress: %d%%, state: %d",
                     info.packageName().c_str(),
                     info.progressPercent(),
                     static_cast<uint32_t>(info.status().status()));

            // 如果检测到某一个包下载失败，抛出异常，后续处理需要暂停所有下载动作
            if (!HandleDownloadStageErr(type, info.status()))
            {
                LOG_ERROR("package name %s download failed",
                          info.packageName().c_str());
                return;
            }
        }
        //进度拟合
        progress_manager_->onDownloadProgress(type, progress);
    });
}

bool DownloadManager::HandleDownloadStageErr(DUCType type,
                                             const CommonStatus &result)
{
    LOG_INFO("Handle %s download stage err..", ducTypeToString(type).c_str());
    if ((result.status() == Status::kStatusFailed))
    {
        auto reason = static_cast<uint32_t>(result.reason());
        LOG_ERROR("Check download stage failed, reason: %u", reason);
        auto convert_reason = ReasonConvert(type, reason);
        if (ReasonTag(convert_reason).IsValid() && fail_reason_callback_)
        {
            fail_reason_callback_(convert_reason);
        }
        return false;
    }
    return true;
}

uint32_t DownloadManager::ReasonConvert(DUCType type, uint32_t reason)
{
    LOG_INFO("reason: %u", reason);
    auto domain{ReasonTag::Domain::kDomainNR};
    switch (type)
    {
    case DUCType::CDC:
    {
        domain = ReasonTag::Domain::kDomainCDC;
        break;
    }
    case DUCType::MDC:
    {
        domain = ReasonTag::Domain::kDomainMDC;
        break;
    }
    case DUCType::ZCU:
    {
        domain = ReasonTag::Domain::kDomainZCU;
        break;
    }
    default:
        LOG_ERROR("Unknown duc type: %d", static_cast<int32_t>(type));
        return ReasonTag::kInvalidIndex;
    }
    auto result = ReasonTag(reason, domain).GetValue();
    return result;
}

} // namespace fotamaster
} // namespace seres