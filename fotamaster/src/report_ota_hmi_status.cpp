#include "report_ota_hmi_status.h"
#include "logger/logger.h"
#include <assert.h>
#include <optional>

namespace seres
{
namespace fotamaster
{

using namespace Seres::HPCC_OTA_HMI;

ReportOtaHmiStatus::ReportOtaHmiStatus()
{
    auto retval = InitPublisher();
    assert(retval);
    LOG_INFO("Init HPCC_OTA_HMI_Status Publisher success");
}

bool ReportOtaHmiStatus::InitPublisher()
{
    auto pub_result =
        HPCC_OTA_HMI_StatusPublisher::Create(42, "HPCC_OTA_HMI_Status");
    if (!pub_result)
    {
        LOG_ERROR("Create HPCC_OTA_HMI_Status publisher failed, error: %s",
                  pub_result.error_msg.c_str());
        return false;
    }
    ota_hmi_status_pub_ = pub_result.GetValue();
    return true;
}

bool ReportOtaHmiStatus::PublisherIsConnected()
{
    auto result = ota_hmi_status_pub_->IsConnected();
    if (!result)
    {
        LOG_ERROR("call publisher IsConnected failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::ReportNewVersion(const std::string &version)
{
    HPCC_OTA_HMI_Status msg;
    msg.OTAM_notifyNewVersionStatus(version);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish new version status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::ReportDownloadProgress(const uint32_t total_size,
                                                const uint16_t remain_time,
                                                const float rate,
                                                const uint8_t progress)
{
    DownloadProgressStatus tmp{rate, total_size, remain_time, progress};
    std::optional<DownloadProgressStatus> download_progress(tmp);

    HPCC_OTA_HMI_Status msg;
    msg.OTAM_notifyDownloadProgressStatus(download_progress);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish download progress status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::ReportInstallPreconditionStatus(
    const uint16_t precondition_status)
{
    std::optional<InstallPreconditionStatus> preconditions{precondition_status};
    HPCC_OTA_HMI_Status msg;
    msg.OTAM_notifyInstallPreconditionStatus(preconditions);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish install precondition status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::ReportInstallProgress(const uint8_t progress)
{
    std::optional<InstallProgressStatus> install_progress{progress};
    HPCC_OTA_HMI_Status msg;
    msg.OTAM_notifyInstallProgressStatus(install_progress);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish install progress status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::TriggerUpgradeCountdown()
{
    std::optional<UpgradeCountdown> default_countdown{2};
    HPCC_OTA_HMI_Status msg;
    msg.OTAM_TriggerUpgradeCountdown(default_countdown);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish upgrade countdown status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

bool ReportOtaHmiStatus::ReportUpgradeFailReason(const uint32_t reason)
{
    HPCC_OTA_HMI_Status msg;
    std::optional<UpgradeFailStatus> fail_reason(reason);
    msg.OTAM_notifyUpgradeFailStatus(fail_reason);
    auto result = ota_hmi_status_pub_->Publish(msg);
    if (!result)
    {
        LOG_ERROR("publish upgrade fail status failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }
    return result.GetValue();
}

} // namespace fotamaster
} // namespace seres