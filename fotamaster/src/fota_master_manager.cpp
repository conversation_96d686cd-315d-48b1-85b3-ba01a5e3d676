#include "fota_master_manager.h"
#include "base/threadinfo.h"
#include "dds_service_manager/duc_service_manager.h"
#include "download_manager.h"
#include "inventory_manager.h"
#include "logger/logger.h"
#include "report_ota_hmi_status.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

using namespace Seres::HPCC_HMI_OTA;

FOTAMasterManager::FOTAMasterManager(EventLoop *event_loop)
    : event_loop_{event_loop}
{
    // TODO 车云建链
    // ...

    // 创建状态机
    InitStateMachine();

    // 初始化dds service
    report_ota_hmi_status_ = std::make_unique<ReportOtaHmiStatus>();
    assert(report_ota_hmi_status_);

    auto retval = InitDucServiceManager();
    retval &= InitHmiOtaCtrlSubscriber();
    assert(retval);

    // 创建资产管理者
    InitInventoryManager();

    InitDownloadManager();

    InitUpgradeManager();
}

FOTAMasterManager::~FOTAMasterManager()
{
}

bool FOTAMasterManager::TriggerInventoryCollection()
{
    inventory_manager_->TriggerInventoryCollection();

    //Test
    // download_manager_->StartDownload();
    // upgrade_manager_->StartUpgrade(UpgradeMode::kFormalUpgrade);
    return true;
}

bool FOTAMasterManager::InitHmiOtaCtrlSubscriber()
{
    // 创建订阅者
    auto sub_result = dds_wrapper::HPCC_HMI_OTA_ControlSubscriber::Create(
        42,
        "HPCC_HMI_OTA_Control");
    if (!sub_result)
    {
        LOG_ERROR("Create HmiOtaCtrl subscriber failed, error: %s",
                  sub_result.error_msg.c_str());
        return false;
    }
    LOG_INFO("Create HmiOtaCtrl subscriber success");

    hmi_ota_ctrl_sub_ = sub_result.GetValue();

    auto result = hmi_ota_ctrl_sub_->Subscribe<HMI_SeamlessUpgradeSet_OTA_In>(
        [this](const HMI_SeamlessUpgradeSet_OTA_In &msg) {
            LOG_INFO("Subscriber thread id: %d", base::curthread::tid());
            LOG_INFO("Recv seamless_upgrade_mode: %d",
                     static_cast<int32_t>(msg.seamlessupgradecmd()));

            // TODO 持久化是否进行无感升级
            support_seamless_upgrade_.store(msg.seamlessupgradecmd(),
                                            std::memory_order_release);
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[SeamlessUpgradeSet] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = hmi_ota_ctrl_sub_->Subscribe<HMI_AutoUpdateSet_OTA_In>(
        [](const HMI_AutoUpdateSet_OTA_In &msg) {
            LOG_INFO("Recv is auto update = %d", msg.autoupdatecmd());
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[AutoUpdateSet] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = hmi_ota_ctrl_sub_->Subscribe<
        HMI_UpdateModeCtrl_OTA_In>([this](
                                       const HMI_UpdateModeCtrl_OTA_In &msg) {
        auto update_mode_ctrl = msg.updatemodectrl();
        LOG_INFO("Recv updatemode = %d",
                 static_cast<int32_t>(update_mode_ctrl.updatemode()));
        auto time = update_mode_ctrl.appointmenttime();
        // std::cout << "收到消息: appointmenttime dayflag = "
        //           << static_cast<int32_t>(time.dayflag())
        //           << ", hour = " << static_cast<int32_t>(time.hour())
        //           << ", minute = " << static_cast<int32_t>(time.minute())
        //           << std::endl;
        if (update_mode_ctrl.updatemode() ==
            Seres::HPCC_HMI_OTA::UpdateMode::UPDATE_IMMEDIATELY)
        {
            //TODO 立即升级
            LOG_INFO("User trigger upgrade immediately");
            event_loop_->RunInLoopThread([this]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInPreFormalUpgradeState;

                // 当前应处于SeamlessUpgradeFinishState状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        }
        else
        {
            // LOG_INFO("User trigger upgrade appointment, time: %s",
            //         msg.time().c_str());

            //TODO 预约升级，启动定时器定时，定时时间到触发升级
            // event_loop_->RunInLoopThread([this]() -> void {
            //     OtaStateMsg state_msg;
            //     state_msg.in_state = OtaInState::kOtaInPreFormalUpgradeState;

            //     // 当前应处于SeamlessUpgradeFinishState状态
            //     ota_state_machine_->HandleEvent(state_msg);
            // });
        }
    });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[UpdateModeCtrl] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    result = hmi_ota_ctrl_sub_->Subscribe<HMI_UpdateCtrl_OTA_In>(
        [this](const HMI_UpdateCtrl_OTA_In &msg) {
            LOG_INFO("is updating = %d", msg.updatectrl());
            if (!msg.updatectrl())
            {
                //TODO 暂不升级
                LOG_INFO("User no upgrade for the time being");
                event_loop_->RunInLoopThread([this]() -> void {
                    OtaStateMsg state_msg;
                    state_msg.in_state = OtaInState::kOtaInIdleState;

                    // 当前应处于PreUpgradeState状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
            }
            else
            {
                //TODO 计时时间到触发升级
                LOG_INFO("It's time to upgrade the timing");
                event_loop_->RunInLoopThread([this]() -> void {
                    OtaStateMsg state_msg;
                    state_msg.in_state = OtaInState::kOtaInFormalUpgradeState;
                    // 当前应处于PreUpgradeState状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
            }
        });
    if (!result)
    {
        LOG_ERROR("Subscribe topic[UpdateCtrl] failed, error: %s",
                  result.error_msg.c_str());
        return false;
    }

    return true;
}

bool FOTAMasterManager::InitDucServiceManager()
{
    duc_service_manager_ = std::make_shared<DUCServiceManager>();
    if (!duc_service_manager_)
    {
        LOG_ERROR("Duc service manager create failed");
        return false;
    }

    bool retval = duc_service_manager_->initialize(1);
    if (!retval)
    {
        LOG_ERROR("Duc service manager initialize failed");
        return false;
    }

    retval = duc_service_manager_->createClient(
        DUCType::CDC,
        std::string(),
        [](DUCType type, bool connected) -> void {
            LOG_INFO("service status change: type: %s, connected: %d",
                     ducTypeToString(type).c_str(),
                     connected);
        });
    retval = duc_service_manager_->createClient(
        DUCType::MDC,
        std::string(),
        [](DUCType type, bool connected) -> void {
            LOG_INFO("service status change: type: %s, connected: %d",
                     ducTypeToString(type).c_str(),
                     connected);
        });
    retval = duc_service_manager_->createClient(
        DUCType::ZCU,
        std::string(),
        [](DUCType type, bool connected) -> void {
            LOG_INFO("service status change: type: %s, connected: %d",
                     ducTypeToString(type).c_str(),
                     connected);
        });

    if (!retval)
    {
        LOG_ERROR("Create CDC_DucService_Service failed");
        return false;
    }
    LOG_INFO("InitDucServiceManager succeed");
    return true;
}

void FOTAMasterManager::InitStateMachine()
{
    ota_state_machine_ = std::make_unique<OtaStateMachine>(event_loop_);
    assert(ota_state_machine_);
    ota_state_machine_->InitState();
    ota_state_machine_->RegisterEventHandle(
        [this](const StateEvent &state, const std::any &data) -> bool {
            return EventHandleCb(state, data);
        });
    ota_state_machine_->ChangeState("IdleState");
}

void FOTAMasterManager::InitInventoryManager()
{
    inventory_manager_ =
        std::make_unique<InventoryManager>(duc_service_manager_.get());
    assert(inventory_manager_);
    inventory_manager_->RegisterInventoryCallback(
        [this](const InventoryManager::InventoryInfoList &info_list) -> void {
            LOG_INFO("Inventory collection finished, report...");
            // TODO 资产上报
        });
}

void FOTAMasterManager::InitDownloadManager()
{
    download_manager_ =
        std::make_unique<DownloadManager>(duc_service_manager_.get());
    assert(download_manager_);
    download_manager_->RegisterFailReasonCallback(
        [this](const uint32_t &reason) -> void {
            // TODO 暂停下载
            download_manager_->StopDownload();

            event_loop_->RunInLoopThread([this, reason]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInFaultState;
                state_msg.state_param = reason;
                // 当前应处于Download状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        });

    download_manager_->RegisterProgressCallback([this](uint8_t progress,
                                                       uint64_t allPakageSize,
                                                       float speed,
                                                       uint32_t remainingTime)
                                                    -> void {
        LOG_INFO("Download progress: %d%%, allPakageSize: %lu, speed: %f, "
                 "remainingTime: %u",
                 progress,
                 allPakageSize,
                 speed,
                 remainingTime);
        // TODO 进度上报
        report_ota_hmi_status_->ReportDownloadProgress(
            static_cast<uint16_t>(allPakageSize),
            static_cast<uint16_t>(remainingTime),
            speed,
            progress);


        // 下载完成，进入下一阶段
        if (progress == 100)
        {
            event_loop_->RunInLoopThread([this]() -> void {
                // TODO 如果用户同意预安装，进入预安装阶段
                OtaStateMsg state_msg;
                if (support_seamless_upgrade_.load(std::memory_order_acquire))
                {
                    state_msg.in_state = OtaInState::kOtaInSeamlessUpgradeState;
                }
                else
                {
                    state_msg.in_state =
                        OtaInState::kOtaInSeamlessUpgradeFinishState;
                }

                // 当前应处于Download状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        }
    });
}

void FOTAMasterManager::InitUpgradeManager()
{
    upgrade_manager_ =
        std::make_unique<UpgradeManager>(duc_service_manager_.get());
    assert(upgrade_manager_);
    upgrade_manager_->RegisterFailReasonCallback(
        [this](const uint32_t &reason) -> void {
            event_loop_->RunInLoopThread([this, reason]() -> void {
                OtaStateMsg state_msg;
                state_msg.in_state = OtaInState::kOtaInFaultState;
                state_msg.state_param = reason;
                // 当前应处于SeamlessUpgrade or FormalUpgrade状态
                ota_state_machine_->HandleEvent(state_msg);
            });
        });

    upgrade_manager_->RegisterProgressCallback(
        [this](const UpgradeManager::ProgressStatus &status) -> void {
            LOG_INFO("update total progress: %hhu", status.total_progress);
            LOG_INFO("update each_progress size: %lu",
                     status.each_progress.size());
            for (const auto &iter : status.each_progress)
            {
                LOG_INFO("device id: %s, progress: %hhu",
                         iter.DeviceId.c_str(),
                         iter.currentProgress);
            }
            report_ota_hmi_status_->ReportInstallProgress(
                status.total_progress);

            // 所有件升级完成之后跳转到退出状态
            if (status.all_finished)
            {
                event_loop_->RunInLoopThread([this]() -> void {
                    OtaStateMsg state_msg;
                    auto current_state = ota_state_machine_->GetCurrentState();
                    if (current_state == "SeamlessUpgradeState")
                    {
                        state_msg.in_state =
                            OtaInState::kOtaInSeamlessUpgradeFinishState;
                    }
                    else if (current_state == "FormalUpgradeState")
                    {
                        state_msg.in_state = OtaInState::kOtaInExitState;
                    }
                    else
                    {
                        LOG_WARN("ota state machine Anomaly, current state: %s",
                                 current_state.c_str());
                        return;
                    }
                    // 当前应处于SeamlessUpgrade or FormalUpgrade状态
                    ota_state_machine_->HandleEvent(state_msg);
                });
            }
        });
}

bool FOTAMasterManager::EventHandleCb(const StateEvent &state,
                                      const std::any &data)
{
    switch (state)
    {
    case StateEvent::kStartDownload:
    {
        LOG_INFO("Start download...");
        // UsageMode=Convenience 或 Driving 或 remote & PowerMode=On时启动下载
        return HandleStartDownload();
    }
    case StateEvent::kStartSeamlessUpgrade:
    {
        LOG_INFO("Start seamless upgrade...");
        // TODO
        return HandleStartUpgrade(UpdateMode::SeamlessMode);
    }
    case StateEvent::kCheckPreCondition:
    {
        LOG_INFO("Start check precondition...");
        // TODO
        break;
    }
    case StateEvent::kStartFormalUpgrade:
    {
        LOG_INFO("Start formal upgrade...");
        // TODO
        return HandleStartUpgrade(UpdateMode::FormalMode);
    }
    case StateEvent::kFaultHandling:
    {
        LOG_INFO("Start handle fault...");
        HandleFault();
        break;
    }
    case StateEvent::kExitHandling:
    {
        LOG_INFO("Start handle exit...");
        HandleExit();
        break;
    }
    default:
        break;
    }
    return true;
}

bool FOTAMasterManager::HandleStartDownload()
{
    // 下载前置条件检查
    // TODO 下载的前置条件提取
    auto state_msg = ota_state_machine_->GetStateMsg();
    if (state_msg.in_state != OtaInState::kOtaInDownloadState)
    {
        LOG_ERROR("State is not in DownloadState, current state: %s",
                  g_ota_state_to_str.at(state_msg.in_state).c_str());
        return false;
    }

    OtaTaskInfo task_info;
    if (std::holds_alternative<OtaTaskInfo>(state_msg.state_param))
    {
        task_info = std::get<OtaTaskInfo>(state_msg.state_param);
        LOG_INFO("upgrade info = %s, package info = %s",
                 task_info.upgrade_info.c_str(),
                 task_info.package_info.c_str());
    }

    // test
    std::vector<DUCType> domain_list;
    domain_list.reserve(3);
    domain_list.push_back(DUCType::CDC);
    download_manager_->NeedUpgradeDomain(domain_list);

    DownloadConditionLists downloadConditions;
    DownloadRequirement downloadReq;
    downloadReq.deviceId("dv001");
    downloadReq.diskRequirement(500);
    downloadConditions.downloadRequirementLists().push_back(downloadReq);
    DownloadManager::DownloadConditionInfoMap info;
    info.insert(std::make_pair(DUCType::CDC, downloadConditions));
    auto retval = download_manager_->CheckDownloadCondition(info);
    if (!retval)
    {
        LOG_ERROR("CheckDownloadCondition invoke failed");
        return false;
    }

    // test
    DownloadTaskLists tasks;
    DownloadTaskInfo task;
    task.taskId("TASK001");
    task.packageVersion("1.0.0");
    task.packageName("test_package");
    task.packageUrl("http://test.com/package");
    task.packageSize("1024");
    task.packageMd5("md5sum");
    tasks.taskLists().push_back(task);
    DownloadManager::DownloadInfoMap download_info;
    download_info.insert(std::make_pair(DUCType::CDC, tasks));

    // 触发下载
    retval = download_manager_->StartDownload(download_info);
    if (!retval)
    {
        LOG_ERROR("StartDownload invoke failed");
        return false;
    }
    return true;
}

bool FOTAMasterManager::HandleStartUpgrade(UpdateMode mode)
{
    // test
    std::vector<DUCType> domain_list;
    domain_list.reserve(3);
    domain_list.push_back(DUCType::CDC);
    download_manager_->NeedUpgradeDomain(domain_list);

    auto retval = upgrade_manager_->CheckUpgradeCondition();
    if (!retval)
    {
        LOG_ERROR("CheckUpgradeCondition invoke failed");
        return false;
    }

    // test
    UpdateDeviceList updateList;
    std::string deviceId = "TEST001";
    updateList.updateDeviceLists().push_back(deviceId);
    UpgradeManager::UpdateInfoMap update_info;
    update_info.insert(std::make_pair(DUCType::CDC, updateList));

    retval = upgrade_manager_->StartUpgrade(mode, update_info);
    return retval;
}

bool FOTAMasterManager::HandleFault()
{
    auto state_msg = ota_state_machine_->GetStateMsg();
    if (state_msg.in_state != OtaInState::kOtaInFaultState)
    {
        LOG_ERROR("State is not in FaultState, current state: %s",
                  g_ota_state_to_str.at(state_msg.in_state).c_str());
        return false;
    }

    uint32_t reason{0u};
    if (std::holds_alternative<uint32_t>(state_msg.state_param))
    {
        reason = std::get<uint32_t>(state_msg.state_param);
        LOG_INFO("fault reason: %u", reason);

        // TODO report reason
        // report reason to hmi
        report_ota_hmi_status_->ReportUpgradeFailReason(reason);
    }
    return true;
}

bool FOTAMasterManager::HandleExit()
{
    // TODO
    // cancel update ctrl..
    // activate ctrl..
    return true;
}

} // namespace fotamaster
} // namespace seres