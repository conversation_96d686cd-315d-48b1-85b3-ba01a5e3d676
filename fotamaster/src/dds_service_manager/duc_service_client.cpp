#include "dds_service_manager/duc_service_client.h"
#include <chrono>
#include <iostream>
#include <thread>

namespace seres
{
namespace fotamaster
{


//===================== DUCServiceClient Implementation ======================

DUCServiceClient::DUCServiceClient(
    DUCType type,
    const std::string &serviceName,
    const dds::domain::DomainParticipant &participant,
    ServiceStatusCallback &callback,
    dds::pub::qos::DataWriterQos dwQos,
    dds::sub::qos::DataReaderQos drQos)
    : m_type(type), m_serviceName(serviceName), m_callback(callback)
{

    m_connected = false;

    try
    {
        // Create RPC client parameters
        dds::rpc::ClientParams params(participant);
        params.serviceName(serviceName);

        // Set QoS policies
        params.dataWriterQos(dwQos);
        params.dataReaderQos(drQos);

        // Create client
        m_client = std::make_unique<DucServiceInterfaceClient>(params);

        m_client->request_datawriter().listener(
            this,
            dds::core::status::StatusMask::publication_matched());

        m_client->reply_datareader().listener(
            this,
            dds::core::status::StatusMask::liveliness_changed());

        LOG_INFO("Created RPC client, service: %s", serviceName.c_str());
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Failed to create RPC client: %s", e.what());
        throw;
    }
}

DUCServiceClient::~DUCServiceClient()
{
    if (m_client)
    {
        // Remove listeners
        m_client->request_datawriter().listener(
            nullptr,
            dds::core::status::StatusMask::none());
        m_client->reply_datareader().listener(
            nullptr,
            dds::core::status::StatusMask::none());
    }
}

bool DUCServiceClient::waitForService()
{
    try
    {
        if (!m_client)
        {
            LOG_ERROR("Client not initialized");
            return false;
        }

        // Try to wait for service
        m_client->wait_for_service();

        if (m_connected)
        {
            LOG_INFO("Service connected: %s", m_serviceName.c_str());
            return true;
        }
        return false;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception while waiting for service: %s", e.what());
        return false;
    }
}

bool DUCServiceClient::isConnected() const
{
    return m_connected;
}

DucServiceInterfaceClient *DUCServiceClient::getClient()
{
    return m_client.get();
}

void DUCServiceClient::on_publication_matched(
    dds::pub::DataWriter<DucServiceInterface_Request> & /* writer */,
    const dds::core::status::PublicationMatchedStatus &status)
{
    LOG_INFO("Server reader matched, service name: %s, current count: %d",
             m_serviceName.c_str(),
             status.current_count());
    if (status.current_count_change() > 0)
    {
        this->m_connected = true;
        if (m_callback)
            m_callback(m_type, true);
    }
    else
    {
        this->m_connected = false;
    }
}

void DUCServiceClient::on_subscription_matched(
    dds::sub::DataReader<DucServiceInterface_Reply> & /* reader */,
    const dds::core::status::SubscriptionMatchedStatus &status)
{
    LOG_INFO("Server writer matched, service name: %s, current count: %d",
             m_serviceName.c_str(),
             status.current_count());
    if (status.current_count_change() > 0)
    {
        this->m_connected = true;
    }
    else
    {
        this->m_connected = false;
    }
}

void DUCServiceClient::on_liveliness_changed(
    dds::sub::DataReader<DucServiceInterface_Reply> & /* reader */,
    const dds::core::status::LivelinessChangedStatus &status)
{
    LOG_INFO(
        "Server writer liveliness changed, service name: %s, alive count: %d",
        m_serviceName.c_str(),
        status.alive_count());
    if (status.alive_count() == 0)
    {
        this->m_connected = false;
        if (m_callback)
            m_callback(m_type, false);
    }
}

} // namespace fotamaster
} // namespace seres