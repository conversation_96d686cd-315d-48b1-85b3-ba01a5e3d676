#include "dds_service_manager/duc_service_manager.h"
#include <chrono>
#include <thread>

namespace seres
{
namespace fotamaster
{

std::string ducTypeToString(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return "CDC";
    case DUCType::MDC:
        return "MDC";
    case DUCType::VDC:
        return "VDC";
    default:
        return "UNKNOWN";
    }
}
std::string returnCodeToString(ReturnCode code)
{
    switch (code)
    {
    case ReturnCode::OK:
        return "OK";
    case ReturnCode::ERROR:
        return "ERROR";
    case ReturnCode::REFUSED:
        return "REFUSED";
    default:
        return "UNKNOWN";
    }
}

DUCServiceManager::DUCServiceManager() : m_initialized(false)
{
}

DUCServiceManager::~DUCServiceManager()
{
    shutdown();
}

bool DUCServiceManager::initialize(int domainId)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (m_initialized)
    {
        LOG_WARN("DUCServiceManager already initialized");
        return true;
    }

    try
    {
        LOG_INFO("Initializing DUCServiceManager, domain ID: %d", domainId);
        m_participant = std::make_shared<dds::domain::DomainParticipant>(domainId);
        m_subscriber = std::make_shared<dds::sub::Subscriber>(*m_participant);

        m_initialized = true;
        return true;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Failed to initialize DUCServiceManager: %s", e.what());
        return false;
    }
}

bool DUCServiceManager::createClient(DUCType type, std::string serviceName, ServiceStatusCallback statusCallback)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized)
    {
        LOG_ERROR("DUCServiceManager not initialized");
        return false;
    }

    try
    {
        // 检查是否已经创建
        if (m_clients.find(type) != m_clients.end() || m_TopicSubscribers.find(type) != m_TopicSubscribers.end())
        {
            LOG_WARN("Client already exists: %s", ducTypeToString(type).c_str());
            return true;
        }

        //创建rpc客户端
        if (serviceName.empty())
            serviceName = getServiceName(type);
        auto client = std::make_unique<DUCServiceClient>(type, serviceName, *m_participant, statusCallback);
        m_clients[type] = std::move(client);

        //订阅话题
        m_TopicSubscribers[type] = std::make_unique<TopicSubscriber<OTA_DucDataUnion>>(
            *m_participant,
            getTopicName(type),
            [this, type](const OTA_DucDataUnion &data) { onTopicDataReceived(type, data); },
            m_subscriber);
        return true;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Failed to create client: %s", e.what());
        return false;
    }
}

void DUCServiceManager::onTopicDataReceived(DUCType type, const OTA_DucDataUnion &data)
{
    switch (data._d())
    {
    case OTA_TopicData::INVENTORY_RESULT:
        LOG_INFO("Received inventory result from DUC=%s", ducTypeToString(type).c_str());
        if (m_InventoryResultCallbacks.find(type) != m_InventoryResultCallbacks.end())
        {
            m_InventoryResultCallbacks[type](data.inventoryResult());
        }
        else
        {
            LOG_WARN("No inventory result callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::DOWNLOAD_PROGRESS:
        LOG_INFO("Received download progress from DUC=%s", ducTypeToString(type).c_str());
        if (m_DownloadProgressCallbacks.find(type) != m_DownloadProgressCallbacks.end())
        {
            for (auto &callback : m_DownloadProgressCallbacks[type])
            {
                callback(data.downloadProgress());
            }
        }
        else
        {
            LOG_WARN("No download progress callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::UZIP_PACKAGES_RESULT:
        LOG_INFO("Received unzip packages result from DUC=%s", ducTypeToString(type).c_str());
        if (m_UzipPackagesResultCallbacks.find(type) != m_UzipPackagesResultCallbacks.end())
        {
            m_UzipPackagesResultCallbacks[type](data.uzipPackagesResult());
        }
        else
        {
            LOG_WARN("No unzip packages result callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::PACKAGES_VERIFY_RESULT:
        LOG_INFO("Received packages verify result from DUC=%s", ducTypeToString(type).c_str());
        if (m_PackagesVerifyResultCallbacks.find(type) != m_PackagesVerifyResultCallbacks.end())
        {
            m_PackagesVerifyResultCallbacks[type](data.packagesVerifyResult());
        }
        else
        {
            LOG_WARN("No packages verify result callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::CHECK_UPDATE_CONDITION_RESULT:
        LOG_INFO("Received check update condition result from DUC=%s", ducTypeToString(type).c_str());
        if (m_CheckUpdateConditionResultCallbacks.find(type) != m_CheckUpdateConditionResultCallbacks.end())
        {
            m_CheckUpdateConditionResultCallbacks[type](data.checkUpdateConditionResult());
        }
        else
        {
            LOG_WARN("No check update condition result callback registered for "
                     "DUC=%s",
                     ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::UPDATE_PROGRESS:
        LOG_INFO("Received update progress from DUC=%s", ducTypeToString(type).c_str());
        if (m_UpdateProgressCallbacks.find(type) != m_UpdateProgressCallbacks.end())
        {
            for (const auto &callback : m_UpdateProgressCallbacks[type])
            {
                callback(data.updateProgress());
            }
        }
        else
        {
            LOG_WARN("No update progress callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    case OTA_TopicData::ROLLBACK_PROGRESS:
        LOG_INFO("Received roolback progress from DUC=%s", ducTypeToString(type).c_str());
        if (m_RollbackProgressCallbacks.find(type) != m_RollbackProgressCallbacks.end())
        {
            for (const auto &callback : m_RollbackProgressCallbacks[type])
            {
                callback(data.updateProgress());
            }
        }
        else
        {
            LOG_WARN("No roolback progress callback registered for DUC=%s", ducTypeToString(type).c_str());
        }
        break;

    default:
        LOG_ERROR("Received unknown topic data from DUC=%s", ducTypeToString(type).c_str());
        break;
    }
}

bool DUCServiceManager::subscribeInventoryResult(DUCType type, const InventoryResultCallback &callback, bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_InventoryResultCallbacks[type] = callback;
    else
        m_InventoryResultCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribeDownloadProgress(DUCType type, const DownloadProgressCallback &callback, bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_DownloadProgressCallbacks[type].push_back(callback);
    else
        m_DownloadProgressCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribeUpdateProgress(DUCType type, const UpdateProgressCallback &callback, bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_UpdateProgressCallbacks[type].push_back(callback);
    else
        m_UpdateProgressCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribeRollbackProgress(DUCType type, const UpdateProgressCallback &callback, bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_RollbackProgressCallbacks[type].push_back(callback);
    else
        m_RollbackProgressCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribeUzipPackagesResult(DUCType type,
                                                    const UzipPackagesResultCallback &callback,
                                                    bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_UzipPackagesResultCallbacks[type] = callback;
    else
        m_UzipPackagesResultCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribePackagesVerifyResult(DUCType type,
                                                      const PackagesVerifyResultCallback &callback,
                                                      bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_PackagesVerifyResultCallbacks[type] = callback;
    else
        m_PackagesVerifyResultCallbacks.clear();
    return true;
}

bool DUCServiceManager::subscribeCheckUpdateConditionResult(DUCType type,
                                                            const CheckUpdateConditionResultCallback &callback,
                                                            bool un_sub)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (!un_sub)
        m_CheckUpdateConditionResultCallbacks[type] = callback;
    else
        m_CheckUpdateConditionResultCallbacks.clear();
    return true;
}

DucServiceInterfaceClient *DUCServiceManager::getDUCClient(DUCType type)
{
    auto it = m_clients.find(type);
    if (it == m_clients.end())
    {
        LOG_ERROR("Client does not exist: %s", ducTypeToString(type).c_str());
        return nullptr;
    }
    return it->second->getClient();
}

bool DUCServiceManager::checkClientExist(DUCType type)
{
    return m_clients.find(type) != m_clients.end();
}

bool DUCServiceManager::checkClientConnected(DUCType type)
{
    auto it = m_clients.find(type);
    if (it == m_clients.end())
    {
        LOG_ERROR("Client does not exist: %s", ducTypeToString(type).c_str());
        return false;
    }

    if (!it->second->isConnected())
    {
        LOG_ERROR("Client not connected: %s", ducTypeToString(type).c_str());
        return false;
    }

    return true;
}

std::string DUCServiceManager::getServiceName(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return CDC_SERVICE_NAME;
    case DUCType::MDC:
        return MDC_SERVICE_NAME;
    case DUCType::VDC:
        return VDC_SERVICE_NAME;
    default:
        return "";
    }
}

std::string DUCServiceManager::getTopicName(DUCType type)
{
    switch (type)
    {
    case DUCType::CDC:
        return CDC_TOPIC_NAME;
    case DUCType::MDC:
        return MDC_TOPIC_NAME;
    case DUCType::VDC:
        return VDC_TOPIC_NAME;
    default:
        return "";
    }
}

bool DUCServiceManager::waitConnect(DUCType type)
{
    int try_count = 0;
    while (!checkClientConnected(type))
    {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        try_count++;
        if (try_count > 2)
        {
            LOG_ERROR("Failed to connect to DUC=%s ", ducTypeToString(type).c_str());
            return false;
        }
    }
    return true;
}

ReturnCode DUCServiceManager::inventoryCollection(DUCType type, const SelectedInventoryList &inventory_list)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->inventoryCollection(inventory_list);
        LOG_INFO("Requested inventoryCollection from DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while requesting inventoryCollection "
                  "from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::stopInventoryCollection(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->stopInventoryCollection();
        LOG_INFO("Sent stop asset information collection request to DUC=%s: "
                 "result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending stop asset information "
                  "collection request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getInventoryResult(DUCType type, InventoryResult &result)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getInventoryResult(result);
        LOG_INFO("Retrieved asset information result from DUC=%s: result=%s, "
                 "count=%zu",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 result.InventoryLists().size());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving asset information "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Download pre-check related methods
ReturnCode DUCServiceManager::checkDownloadCondition(DUCType type,
                                                     const DownloadConditionLists &conditions,
                                                     CommonStatus &result)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->checkDownloadCondition(conditions, result);
        LOG_INFO("Sent download condition check request to DUC=%s: result=%s, "
                 "check result=%d",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 static_cast<int>(result.reason()));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending download condition check "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Download related methods
ReturnCode DUCServiceManager::startDownload(DUCType type, const DownloadTaskLists &tasks)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startDownload(tasks);
        LOG_INFO("Sent start download request to DUC=%s: result=%s, task "
                 "count=%zu",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 tasks.taskLists().size());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending start download request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::downloadCtrl(DUCType type, DownloadCtrl command)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->downloadCtrl(command);
        LOG_INFO("Sent download control request to DUC=%s: command=%d, "
                 "result=%s",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(command),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending download control request "
                  "to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getDownloadProgress(DUCType type, DownloadProgress &download_progress)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getDownloadProgress(download_progress);
        LOG_INFO("Retrieved download progress from DUC=%s: result=%s, all "
                 "finished=%d",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 download_progress.allFinished());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving download progress from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Unzip related methods
ReturnCode DUCServiceManager::uzipPackages(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->uzipPackages();
        LOG_INFO("Sent unzip request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending unzip request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getuzipPackagesResult(DUCType type, CommonStatus &uzip_Result)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getuzipPackagesResult(uzip_Result);
        LOG_INFO("Retrieved unzip result from DUC=%s: result=%s, unzip "
                 "success=%d",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 static_cast<int>(uzip_Result.status()));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving unzip result from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Package verification related methods
ReturnCode DUCServiceManager::startPackagesVerify(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startPackagesVerify();
        LOG_INFO("Sent package verification request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending package verification "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getPackagesVerifyResult(DUCType type, CommonStatus &verify_Result)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getPackagesVerifyResult(verify_Result);
        LOG_INFO("Retrieved package verification result from DUC=%s: "
                 "result=%s, verification success=%d",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 static_cast<int>(verify_Result.status()));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving package verification "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Update related methods
ReturnCode DUCServiceManager::checkUpdateCondition(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->checkUpdateCondition();
        LOG_INFO("Sent update condition check request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending update condition check "
                  "request to DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getCheckUpdateConditionResult(DUCType type, CommonStatus &checkcondition_Result)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getCheckUpdateConditionResult(checkcondition_Result);
        LOG_INFO("Retrieved update condition check result from DUC=%s: "
                 "result=%s, passed=%d, error code=%d",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str(),
                 static_cast<int>(checkcondition_Result.status()),
                 static_cast<int>(checkcondition_Result.reason()));
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while retrieving update condition check "
                  "result from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::startUpdate(DUCType type, UpdateMode mode, const UpdateDeviceList &update_list)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->startUpdate(mode, update_list);
        LOG_INFO("Sent start update request to DUC=%s: mode=%d, device "
                 "count=%zu, result=%s",
                 ducTypeToString(type).c_str(),
                 static_cast<int>(mode),
                 update_list.updateDeviceLists().size(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending start update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getUpdateProgress(DUCType type, UpdateProgress &updateProgress)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getUpdateProgress(updateProgress);
        LOG_INFO("Sent get progress request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while getting progress request from "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::resumeUpdate(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->resumeUpdate();
        LOG_INFO("Sent resume update request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending resume update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::pauseUpdate(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->pauseUpdate();
        LOG_INFO("Sent pause update request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending pause update request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Activation method
ReturnCode DUCServiceManager::activate(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->activate();
        LOG_INFO("Sent activation request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending activation request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Rollback related methods
ReturnCode DUCServiceManager::rollback(DUCType type, const RollbackComponentList &components)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->rollback(components);
        LOG_INFO("Sent rollback request to DUC=%s: component count=%zu, "
                 "result=%s",
                 ducTypeToString(type).c_str(),
                 components.rollbackLists().size(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending rollback request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

ReturnCode DUCServiceManager::getRollbackProgress(DUCType type, UpdateProgress &rollbackProgress)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->getRollbackProgress(rollbackProgress);
        LOG_INFO("Sent get rollback progress request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while getting rollback progress request "
                  "from DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

// Log upload method
ReturnCode DUCServiceManager::uploadLog(DUCType type)
{
    if (!waitConnect(type))
        return ReturnCode::ERROR;
    auto client = getDUCClient(type);
    try
    {
        ReturnCode ret = client->uploadLog();
        LOG_INFO("Sent log upload request to DUC=%s: result=%s",
                 ducTypeToString(type).c_str(),
                 returnCodeToString(ret).c_str());
        return ret;
    }
    catch (const dds::core::Exception &e)
    {
        LOG_ERROR("Exception occurred while sending log upload request to "
                  "DUC=%s: %s",
                  ducTypeToString(type).c_str(),
                  e.what());
        return ReturnCode::ERROR;
    }
}

void DUCServiceManager::shutdown()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized)
    {
        return;
    }

    LOG_INFO("Shutting down DUCServiceManager");

    // Clean up subscriptions
    m_TopicSubscribers.clear();

    // Clean up clients
    m_clients.clear();

    m_initialized = false;
}

} // namespace fotamaster
} // namespace seres