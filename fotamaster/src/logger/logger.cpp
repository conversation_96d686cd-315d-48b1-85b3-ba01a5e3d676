#include "logger/logger.h"
#include "logger/logger_config.h"

#include <spdlog/async.h>
#include <spdlog/pattern_formatter.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/stdout_sinks.h>
#include <cassert>

namespace seres
{
namespace fotamaster
{

void Logger::Init(std::string &&config_path)
{
    LogRecoderConfig config{std::move(config_path)};
    ns::LoggerConfig logger_config{};
    auto retval = config.GetLogConfig(logger_config);
    assert(retval);

    LogConfig log_config{};
    auto file_path = logger_config.log_file_path + "/fotamaster.log";
    log_config.log_file_path = std::move(file_path);
    log_config.max_file_size = logger_config.max_file_size * 1024 * 1024; // MB
    log_config.max_files = logger_config.max_file_count;
    log_config.log_level = static_cast<LogLevel>(logger_config.log_level);
    log_config.console_output = logger_config.enable_console;
    Init(log_config);
}

void Logger::Init(const LogConfig &config)
{
    // std::lock_guard<std::mutex> lock(mutex_);

    // 如果已经初始化过，先关闭之前的日志器
    if (logger_)
    {
        logger_->flush();
        spdlog::drop(logger_->name());
    }

    // 创建sink列表
    std::vector<spdlog::sink_ptr> sinks;
    sinks.clear();

    // 添加控制台输出
    if (config.console_output)
    {
        auto console_sink =
            std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        assert(console_sink);
        console_sink->set_level(
            static_cast<spdlog::level::level_enum>(config.log_level));
        console_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v");
        sinks.push_back(console_sink);
    }

    // 添加文件输出
    if (!config.log_file_path.empty())
    {
        try
        {
            auto file_sink =
                std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
                    config.log_file_path,
                    config.max_file_size,
                    config.max_files);
            assert(file_sink);

            file_sink->set_level(
                static_cast<spdlog::level::level_enum>(config.log_level));
            file_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] %v");
            sinks.push_back(file_sink);
        }
        catch (const spdlog::spdlog_ex &ex)
        {
            // 如果创建文件sink失败，至少确保控制台输出可用
            if (sinks.empty())
            {
                auto console_sink =
                    std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
                console_sink->set_level(
                    static_cast<spdlog::level::level_enum>(config.log_level));
                console_sink->set_pattern(
                    "[%Y-%m-%d %H:%M:%S.%e] [%^%l%$] %v");
                sinks.push_back(console_sink);
            }
        }
    }

    // 创建多sink日志器
    logger_ = std::make_shared<spdlog::logger>("fotamaster_logger",
                                               sinks.begin(),
                                               sinks.end());
    assert(logger_);

    SetLogLevel(config.log_level);

    // 设置为全局默认日志器
    spdlog::set_default_logger(logger_);

    // 设置刷新策略
    logger_->flush_on(spdlog::level::err);
    spdlog::flush_every(std::chrono::seconds(2));
}

void Logger::SetLogLevel(LogLevel level)
{
    // std::lock_guard<std::mutex> lock(mutex_);
    if (logger_)
    {
        logger_->set_level(static_cast<spdlog::level::level_enum>(level));
    }
}

std::shared_ptr<spdlog::logger> Logger::GetLogger() const
{
    // std::lock_guard<std::mutex> lock(mutex_);
    return logger_;
}

} // namespace fotamaster
} // namespace seres