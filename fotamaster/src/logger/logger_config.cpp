#include "logger/logger_config.h"
#include <cassert>
#include <fstream>
#include <filesystem>

namespace seres
{
namespace fotamaster
{

LogRecoderConfig::LogRecoderConfig(std::string &&config_path)
    : config_path_{std::move(config_path)}
{
    assert(std::filesystem::exists(config_path_));
}

bool LogRecoderConfig::GetLogConfig(ns::LoggerConfig &log_config)
{
    std::ifstream file(config_path_, std::ios::in | std::ios::binary);
    if (!file.is_open())
    {
        return false;
    }

    ns::Json j;
    file >> j;

    j.at(ns::kLoggerConfigLabel).get_to(log_config);

    file.close();
    return true;
}

} // namespace fotamaster
} // namespace seres