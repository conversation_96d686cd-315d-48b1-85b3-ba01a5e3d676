#include "state_machine/exit_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool ExitState::Enter()
{
    LOG_INFO("=====Enter exit state");
    if (!GetStateMachine()->EventCallback(StateEvent::kExitHandling))
    {
        LOG_ERROR("Call Exit handling failed");
        return false;
    }
    return true;
}

void ExitState::Process(const EventData &data)
{
    LOG_INFO("=====Process exit state");
}

void ExitState::Exit()
{
    LOG_INFO("=====Exit exit state");
}

REGISTER_STATE("ExitState", ExitState)

} // namespace fotamaster
} // namespace seres