#include "state_machine/formal_upgrade_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool FormalUpgradeState::Enter()
{
    LOG_INFO("=====Enter formal upgrade state");
    if (!GetStateMachine()->EventCallback(StateEvent::kStartFormalUpgrade))
    {
        LOG_ERROR("Call start formal upgrade failed");
        return false;
    }
    return true;
}

void FormalUpgradeState::Process(const EventData &data)
{
    LOG_INFO("=====Process formal upgrade state");
    HandleRequestEvent(std::any_cast<OtaInState>(data.data));
}

void FormalUpgradeState::Exit()
{
    LOG_INFO("=====Exit formal upgrade state");
}

void FormalUpgradeState::HandleRequestEvent(const OtaInState &ota_in_state)
{
    switch (ota_in_state)
    {
    case OtaInState::kOtaInFaultState:
    {
        GetStateMachine()->ChangeState("FaultState");
        break;
    }
    case OtaInState::kOtaInRollbackState:
    {
        GetStateMachine()->ChangeState("RollbackState");
        break;
    }
    case OtaInState::kOtaInExitState:
    {
        GetStateMachine()->ChangeState("ExitState");
        break;
    }
    default:
    {
        auto iter = g_ota_state_to_str.find(ota_in_state);
        if (iter != g_ota_state_to_str.end())
        {
            LOG_WARN("Current ota in state[FormalUpgradeState], not support change to state[%s]",
                     iter->second.c_str());
        }
        else
        {
            LOG_WARN("Unkown ota in state: %hhu",
                    static_cast<uint8_t>(ota_in_state));
        }
        break;
    }
    }
}

REGISTER_STATE("FormalUpgradeState", FormalUpgradeState)

} // namespace fotamaster
} // namespace seres