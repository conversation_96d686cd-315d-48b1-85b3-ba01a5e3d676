#include "state_machine/fault_state.h"
#include "logger/logger.h"
#include "state_machine/ota_state_machine.h"

namespace seres
{
namespace fotamaster
{

bool FaultState::Enter()
{
    LOG_INFO("=====Enter fault state");
    if (!GetStateMachine()->EventCallback(StateEvent::kFaultHandling))
    {
        LOG_ERROR("Call fault handling failed");
        return false;
    }

    GetStateMachine()->ChangeState("IdleState");
    return true;
}

void FaultState::Process(const EventData &data)
{
    LOG_INFO("=====Process fault state");
}

void FaultState::Exit()
{
    LOG_INFO("=====Exit fault state");
}

REGISTER_STATE("FaultState", FaultState)

} // namespace fotamaster
} // namespace seres