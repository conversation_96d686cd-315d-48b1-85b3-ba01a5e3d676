#include "ev_loop/eventloop_manager.h"
#include "progress_fitting/download_progress_fitting.h"

#include <algorithm>
#include <cmath>
#include <iostream>
#include <sstream>
namespace seres
{
namespace fotamaster
{

DownloadProgressManager::~DownloadProgressManager()
{
    if (m_timer)
    {
        m_timer->Cancel();
    }
}

bool DownloadProgressManager::Initialize(
    DownloadOverallProgressCallback progressCallback,
    double reportInterval,
    bool needSub)
{
    if (m_initialized.load())
    {
        LOG_WARN("DownloadProgressManager already initialized");
        return true;
    }

    if (!progressCallback)
    {
        LOG_ERROR("Progress callback is null");
        return false;
    }

    // 保存回调函数
    m_DownloadOverallProgressCallback = progressCallback;
    m_reportInterval = reportInterval;

    // 创建定时器
    auto eventLoop =
        base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    if (!eventLoop)
    {
        LOG_ERROR("Failed to get event loop");
        return false;
    }

    m_timer = std::make_shared<Timer>(eventLoop);
    if (!m_timer)
    {
        LOG_ERROR("Failed to create timer");
        return false;
    }

    // 订阅主题（如果需要）
    if (needSub)
    {
        if (!subscribeTopic())
        {
            LOG_ERROR("Failed to subscribe to download progress topics");
            return false;
        }
    }

    // 重置状态
    Reset();

    m_initialized.store(true);
    LOG_INFO("DownloadProgressManager initialized successfully");
    return true;
}

bool DownloadProgressManager::AddDownloadPackage(
    DUCType ducType,
    const std::vector<DownloadTaskInfo> &download_tasks)
{
    if (!m_initialized.load())
    {
        LOG_ERROR("DownloadProgressManager not initialized");
        return false;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 计算该域控的总大小
    uint64_t domainTotalSize = 0;
    for (const auto &task : download_tasks)
    {
        uint64_t packageSize = parsePackageSize(task.packageSize());
        domainTotalSize += packageSize;
        LOG_INFO("Added package %s for domain %s, size: %lu bytes",
                 task.packageName().c_str(),
                 ducTypeToString(ducType).c_str(),
                 packageSize);
    }

    // 更新总大小
    m_totalSize.fetch_add(domainTotalSize);

    LOG_INFO("Added %zu packages for domain %s, total domain size: %lu bytes, "
             "overall total: %lu bytes",
             download_tasks.size(),
             ducTypeToString(ducType).c_str(),
             domainTotalSize,
             m_totalSize.load());

    return true;
}

bool DownloadProgressManager::Start()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    if (!m_initialized.load())
    {
        LOG_ERROR("UpgradeProgressManager not initialized");
        return false;
    }

    if (m_started.load())
    {
        LOG_WARN("UpgradeProgressManager already started");
        return true;
    }

    // base::Singleton<EventLoopManager>::Instance()
    //     .GetDefaultLoop()
    //     ->RunInLoopThread([this] {
    auto result =
        this->m_timer->SetCallback([this]() { this->onTimerCallback(); });
    if (result.has_value())
    {
        LOG_ERROR("Failed to set timer callback: %d",
                  static_cast<int>(result.value()));
        return false;
    }
    result = this->m_timer->Every(m_reportInterval);
    if (result.has_value())
    {
        LOG_ERROR("Failed to start timer: %d",
                  static_cast<int>(result.value()));
        return false;
    }
    LOG_INFO("Download progress timer started");
    // });
    m_started.store(true);
    LOG_INFO("UpgradeProgressManager started with report interval: %f seconds",
             m_reportInterval);
    return true;
}

void DownloadProgressManager::Stop()
{
    // 停止定时器
    if (m_timer)
    {
        m_timer->Cancel();
    }

    m_started.store(false);
    LOG_INFO("UpgradeProgressManager stopped");
}

void DownloadProgressManager::onDownloadProgress(
    DUCType ducType,
    const DownloadProgress &progress)
{
    if (!m_initialized.load())
    {
        LOG_WARN("DownloadProgressManager not initialized, ignoring "
                 "progress update");
        return;
    }
    if (!m_started.load())
    {
        LOG_WARN("DownloadProgressManager not started");
        return;
    }
    // 更新域控进度
    m_domainProgress[ducType] = progress;
    uint64_t totalDownloaded = 0;
    for (const auto &[domainType, domainProgress] : m_domainProgress)
    {
        for (const auto &progressInfo : domainProgress.progressLists())
        {
            totalDownloaded += progressInfo.downloadedSize();
        }
    }
    // 更新已下载大小
    m_downloadedSize.store(totalDownloaded);
    LOG_DEBUG("Domain %s progress updated, total downloaded: %lu",
              ducTypeToString(ducType).c_str(),
              totalDownloaded);
}

void DownloadProgressManager::onTimerCallback()
{
    if (!m_initialized.load() || !m_DownloadOverallProgressCallback)
    {
        LOG_ERROR("DownloadProgressManager not initialized or "
                  "callback not set");
        return;
    }

    std::lock_guard<std::mutex> lock(m_mutex);

    // 计算当前进度百分比
    uint64_t totalSize = m_totalSize.load();
    uint64_t downloadedSize = m_downloadedSize.load();

    uint8_t progressPercent = 0;
    if (totalSize > 0)
    {
        progressPercent = static_cast<uint8_t>(
            std::min(100UL, (downloadedSize * 100) / totalSize));
    }

    // 更新进度
    m_Progress.store(progressPercent);

    // 计算速度和剩余时间
    calculateSpeedAndRemainingTime();

    // 调用回调函数报告进度
    static uint32_t totalSizeMB =
        static_cast<uint32_t>(totalSize / (1024 * 1024)); // 转换为MB

    float currentSpeed =
        static_cast<float>(m_currentSpeed.load()) / 1024; // 转换为KB/s
    uint16_t remainingTime = static_cast<uint16_t>(
        std::min(65535U, m_remainingTime.load())); // 限制在uint16_t范围内

    LOG_DEBUG("Progress: %u%%, Total: %u MB, Speed: %f KB/s, Remaining: %u s",
              progressPercent,
              totalSizeMB,
              currentSpeed,
              remainingTime);

    m_DownloadOverallProgressCallback(progressPercent,
                                      totalSizeMB,
                                      currentSpeed,
                                      remainingTime);
    if (m_Progress >= 100 || !m_started.load())
    {
        LOG_INFO("All packages downloaded, stopping progress monitoring");
        m_timer->Cancel();
    }
}

void DownloadProgressManager::calculateSpeedAndRemainingTime()
{
    auto currentTime = std::chrono::steady_clock::now();
    uint64_t currentDownloadedSize = m_downloadedSize.load();

    // 初始化时间点
    if (m_lastUpdateTime == std::chrono::steady_clock::time_point{})
    {
        m_lastUpdateTime = currentTime;
        m_lastDownloadedSize = currentDownloadedSize;
        m_currentSpeed.store(0);
        m_remainingTime.store(0);
        return;
    }

    // 计算时间间隔
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(
                        currentTime - m_lastUpdateTime)
                        .count();

    // 如果时间间隔太短，不更新速度（避免频繁计算和数值波动）
    if (timeDiff < 500) // 至少500ms间隔
    {
        return;
    }

    // 计算瞬时速度 (bytes/s)
    uint32_t instantSpeed = 0;
    if (timeDiff > 0 && currentDownloadedSize >= m_lastDownloadedSize)
    {
        uint64_t downloadedDiff = currentDownloadedSize - m_lastDownloadedSize;
        instantSpeed = static_cast<uint32_t>((downloadedDiff * 1000) /
                                             timeDiff); // 转换为bytes/s
    }

    // 使用滑动窗口平均来平滑速度
    if (m_speedHistory.size() < SPEED_HISTORY_SIZE)
    {
        m_speedHistory.push_back(instantSpeed);
    }
    else
    {
        m_speedHistory[m_speedHistoryIndex] = instantSpeed;
        m_speedHistoryIndex = (m_speedHistoryIndex + 1) % SPEED_HISTORY_SIZE;
    }

    // 计算平均速度
    uint64_t totalSpeed = 0;
    for (uint32_t speed : m_speedHistory)
    {
        totalSpeed += speed;
    }
    uint32_t averageSpeed =
        static_cast<uint32_t>(totalSpeed / m_speedHistory.size());

    // 更新当前速度
    m_currentSpeed.store(averageSpeed);

    // 计算剩余时间
    uint64_t totalSize = m_totalSize.load();
    uint32_t remainingTime = 0;

    if (averageSpeed > 0 && totalSize > currentDownloadedSize)
    {
        uint64_t remainingBytes = totalSize - currentDownloadedSize;
        remainingTime = static_cast<uint32_t>(remainingBytes / averageSpeed);

        // 限制最大剩余时间（避免显示过大的数值）
        remainingTime = std::min(remainingTime, 86400U); // 最大24小时
    }

    m_remainingTime.store(remainingTime);

    // 更新历史数据
    m_lastUpdateTime = currentTime;
    m_lastDownloadedSize = currentDownloadedSize;

    LOG_DEBUG(
        "Speed calculation - Instant: %u B/s, Average: %u B/s, Remaining: %u s",
        instantSpeed,
        averageSpeed,
        remainingTime);
}

uint64_t DownloadProgressManager::parsePackageSize(const std::string &sizeStr)
{
    if (sizeStr.empty())
    {
        LOG_WARN("Empty package size string");
        return 0;
    }

    try
    {
        // 尝试直接解析为数字（假设单位是字节）
        return std::stoull(sizeStr);
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("Failed to parse package size '%s': %s",
                  sizeStr.c_str(),
                  e.what());
        return 0;
    }
}

void DownloadProgressManager::Reset()
{
    std::lock_guard<std::mutex> lock(m_mutex);

    // 停止定时器
    if (m_timer && m_started.load())
    {
        base::Singleton<EventLoopManager>::Instance()
            .GetDefaultLoop()
            ->RunInLoopThread([this] { this->m_timer->Cancel(); });
    }

    // 重置所有状态
    m_started.store(false);
    m_Progress.store(0);
    m_downloadedSize.store(0);
    m_totalSize.store(0);
    m_currentSpeed.store(0);
    m_remainingTime.store(0);

    // 清空数据
    m_domainProgress.clear();

    LOG_INFO("DownloadProgressManager reset");
}

bool DownloadProgressManager::subscribeTopic()
{
    auto &ducManager = base::Singleton<DUCServiceManager>::Instance();
    auto res = ducManager.subscribeDownloadProgress(
        DUCType::CDC,
        [this](const DownloadProgress &progress) {
            this->onDownloadProgress(DUCType::CDC, progress);
        });
    if (!res)
    {
        LOG_ERROR("Failed to subscribe to progress topic");
        return false;
    }
    res = ducManager.subscribeDownloadProgress(
        DUCType::MDC,
        [this](const DownloadProgress &progress) {
            this->onDownloadProgress(DUCType::MDC, progress);
        });
    if (!res)
    {
        LOG_ERROR("Failed to subscribe to progress topic");
        return false;
    }
    res = ducManager.subscribeDownloadProgress(
        DUCType::ZCU,
        [this](const DownloadProgress &progress) {
            this->onDownloadProgress(DUCType::ZCU, progress);
        });
    if (!res)
    {
        LOG_ERROR("Failed to subscribe to progress topic");
        return false;
    }
    LOG_INFO("Download progress topic subscription success");
    return true;
}

} // namespace fotamaster
} // namespace seres