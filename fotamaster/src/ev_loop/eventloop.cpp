#include "ev_loop/eventloop.h"

#include <ev.h>

#include <cassert>
#include <thread>
#include <type_traits>

namespace seres
{
namespace fotamaster
{

class EventLoop::JobsQueue
{
public:
    JobsQueue(EventLoop *loop);
    ~JobsQueue();

    JobsQueue(JobsQueue const &) = delete;
    JobsQueue &operator=(JobsQueue const &) = delete;

    void Push(EventLoop::Func job);
    void HandleJobs();
    void StartConsuming();
    void StopConsuming();
    std::size_t Size() const noexcept;

    void set_max_jobs_at_once(std::size_t max_jobs)
    {
        max_jobs_at_once_ = max_jobs;
    }
    std::size_t pushed_size() const
    {
        return pushed_size_.load(std::memory_order_relaxed);
    }
    std::size_t handled_size() const
    {
        return handled_size_.load(std::memory_order_relaxed);
    }

private:
    static void AsyncCallback(struct ev_loop *backend, struct ev_async *w, int)
    {
        auto loop = reinterpret_cast<EventLoop *>(ev_userdata(backend));
        auto self = reinterpret_cast<EventLoop::JobsQueue *>(w->data);
        assert(self->loop_ == loop);

        self->HandleJobs();
    }

    EventLoop *loop_;
    struct ev_async watcher_;
    mutable std::mutex jobs_mutex_;
    std::list<EventLoop::Func> pending_jobs_;
    std::list<EventLoop::Func> actived_jobs_;
    std::atomic<std::size_t> pushed_size_{0};
    std::atomic<std::size_t> handled_size_{0};
    std::size_t max_jobs_at_once_{16};
};

EventLoop::JobsQueue::JobsQueue(EventLoop *loop) : loop_(loop)
{
    ev_async_init(&watcher_, &JobsQueue::AsyncCallback);
    watcher_.data = reinterpret_cast<void *>(this);
}

EventLoop::JobsQueue::~JobsQueue()
{
    StopConsuming();
}

void EventLoop::JobsQueue::Push(EventLoop::Func job)
{
    pushed_size_.fetch_add(1, std::memory_order_relaxed);
    {
        std::lock_guard<std::mutex> lock{jobs_mutex_};
        pending_jobs_.emplace_back(std::move(job));
    }

    ev_async_send(loop_->GetEventLoopBackend(), &watcher_);
}

void EventLoop::JobsQueue::HandleJobs()
{
    if (actived_jobs_.size() < max_jobs_at_once_)
    {
        std::lock_guard<std::mutex> lock{jobs_mutex_};
        actived_jobs_.splice(actived_jobs_.cend(), pending_jobs_);
    }

    for (std::size_t i = 0; max_jobs_at_once_ == 0 || i < max_jobs_at_once_;
         ++i)
    {
        if (actived_jobs_.empty())
        {
            // all job are done.
            break;
        }

        handled_size_.fetch_add(1, std::memory_order_relaxed);

        auto &job = actived_jobs_.front();
        job();
        actived_jobs_.pop_front();
    }

    if (Size() > 0)
    {
        ev_async_send(loop_->GetEventLoopBackend(), &watcher_);
    }
}

void EventLoop::JobsQueue::StartConsuming()
{
    ev_async_start(loop_->GetEventLoopBackend(), &watcher_);
}

void EventLoop::JobsQueue::StopConsuming()
{
    ev_async_stop(loop_->GetEventLoopBackend(), &watcher_);
    std::lock_guard<std::mutex> lock{jobs_mutex_};
    pending_jobs_.clear();
    actived_jobs_.clear();
}

std::size_t EventLoop::JobsQueue::Size() const noexcept
{
    return pushed_size() - handled_size();
}

EventLoop::EventLoop(const std::string &name, bool default_loop)
    : name_(std::move(name)), event_backend_(nullptr), loop_thread_(),
      jobs_queue_()
{
    //
    event_backend_ = default_loop ? ev_default_loop(0) : ev_loop_new(0);
    assert(event_backend_ != nullptr);

    ev_set_userdata(event_backend_, reinterpret_cast<void *>(this));
    jobs_queue_.reset(new JobsQueue{this});
    jobs_queue_->StartConsuming();
}

EventLoop::~EventLoop()
{
    jobs_queue_.reset();
    ev_loop_destroy(event_backend_);
    event_backend_ = nullptr;
}

std::size_t EventLoop::LoopOnce() noexcept
{
    auto thread_id = loop_thread_.exchange(std::this_thread::get_id(),
                                           std::memory_order_release);
    assert(thread_id == std::thread::id());
    (void)thread_id;

    int actived = ev_run(event_backend_, EVRUN_ONCE);

    loop_thread_.store(std::thread::id(), std::memory_order_release);
    return static_cast<size_t>(actived);
}

void EventLoop::LoopForever()
{
    auto thread_id = loop_thread_.exchange(std::this_thread::get_id(),
                                           std::memory_order_release);
    assert(thread_id == std::thread::id());
    (void)thread_id;

    ev_run(event_backend_, 0);
    loop_thread_.store(std::thread::id(), std::memory_order_release);
}

void EventLoop::WaitUntilLoopRunning()
{
    while (loop_thread_.load(std::memory_order_acquire) == std::thread::id())
    {
        std::this_thread::yield();
    }
}

void EventLoop::TerminateLoop()
{
    if (IsInRunningLoopThread())
    {
        ev_break(event_backend_, EVBREAK_ALL);
    }
    else if (IsRunning())
    {
        jobs_queue_->Push([this]() { ev_break(event_backend_, EVBREAK_ALL); });
    }
    else
    {
        // do nothing
    }
}

bool EventLoop::IsDefaultLoop() const noexcept
{
    return event_backend_ == ev_default_loop(0);
}

void EventLoop::RunInLoopThread(Func job) noexcept
{
    if (IsInLoopThread())
    {
        job();
    }
    else
    {
        RunInRunningLoopThread(std::move(job));
    }
}

void EventLoop::RunInRunningLoopThread(Func job, bool always_enqueue) noexcept
{
    assert(jobs_queue_);
    if (!always_enqueue && IsInRunningLoopThread())
    {
        job();
    }
    else
    {
        jobs_queue_->Push(std::move(job));
    }
}

std::size_t EventLoop::GetJobsQueueSize() const noexcept
{
    assert(jobs_queue_);
    return jobs_queue_->Size();
}

std::size_t EventLoop::GetHandledJobSize() const noexcept
{
    assert(jobs_queue_);
    return jobs_queue_->handled_size();
}

void EventLoop::SetMaxJobsAtOnce(std::size_t max_jobs)
{
    assert(jobs_queue_);
    jobs_queue_->set_max_jobs_at_once(max_jobs);
}

} // namespace fotamaster
} // namespace seres
