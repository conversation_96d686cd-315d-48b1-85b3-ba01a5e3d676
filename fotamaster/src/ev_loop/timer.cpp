#include "ev_loop/timer.h"
#include <cassert>

#include "ev_loop/eventloop.h"

namespace seres
{
namespace fotamaster
{

Timer::Timer(EventLoop *loop) : TimeoutHandler(loop)
{
}

Timer::~Timer()
{
    //TODO
}

std::optional<ErrCode> Timer::SetCallback(TimeoutCallback &&callback)
{
    if (IsRegistered())
    {
        assert(on_timeout_);
        return std::optional<ErrCode>{ErrCode::kRegisteredEvent};
    }

    on_timeout_ = std::move(callback);

    return std::optional<ErrCode>{};
}

std::optional<ErrCode> Timer::After(double timeout)
{
    if (!on_timeout_)
    {
        return std::optional<ErrCode>{ErrCode::kInvalidCallback};
    }

    if (IsRegistered())
    {
        return std::optional<ErrCode>{ErrCode::kRegisteredEvent};
    }

    return RegisterTimeout(timeout);
}

std::optional<ErrCode> Timer::Every(double timeout)
{
    if (!on_timeout_)
    {
        return std::optional<ErrCode>{ErrCode::kInvalidCallback};
    }

    if (IsRegistered())
    {
        return std::optional<ErrCode>{ErrCode::kRegisteredEvent};
    }

    return RegisterTimeout(timeout, false);
}

void Timer::Cancel()
{
    UnregisterTimeout();
}

void Timer::OnTimeout()
{
    assert(on_timeout_);

    on_timeout_();
}

} // namespace fotamaster
} // namespace seres
