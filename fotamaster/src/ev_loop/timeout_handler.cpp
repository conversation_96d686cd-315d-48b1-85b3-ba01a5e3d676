#include "ev_loop/timeout_handler.h"
#include <cassert>
#include <cmath>

#include "ev_loop/eventloop.h"
#include <ev.h>

namespace seres
{
namespace fotamaster
{

void TimeoutHandler::TimeoutCallback(struct ev_loop *backend,
                                     struct ev_timer *watcher,
                                     int)
{
    EventLoop *loop = reinterpret_cast<EventLoop *>(ev_userdata(backend));
    TimeoutHandler *self = reinterpret_cast<TimeoutHandler *>(watcher->data);

    assert(self->loop_ == loop);
    self->OnTimeout();
}

TimeoutHandler::TimeoutHandler(EventLoop *loop) : loop_(loop)
{
    assert(loop_ != nullptr);

    timer_watcher_.reset(new ev_timer);
    ev_init(timer_watcher_.get(), &TimeoutHandler::TimeoutCallback);
    timer_watcher_->data = reinterpret_cast<void *>(this);
}

TimeoutHandler::~TimeoutHandler()
{
    assert(!ev_is_active(timer_watcher_.get()));
    timer_watcher_.reset();
}

std::optional<ErrCode> TimeoutHandler::RegisterTimeout(double value, bool is_one_shot) noexcept
{
    if (std::fabs(value) < 0.0000001)
    {
        return std::optional<ErrCode>{ErrCode::kInvalidArgs};
    }

    assert(loop_ && timer_watcher_ && loop_->IsInLoopThread());

    auto backend = loop_->GetEventLoopBackend();
    auto watcher = timer_watcher_.get();
    ev_timer_stop(backend, watcher);
    if (is_one_shot)
    {
        ev_timer_set(watcher, value, 0.0);
    }
    else
    {
        ev_timer_set(watcher, value, value);
    }
    ev_timer_start(backend, watcher);

    return std::optional<ErrCode>{};
}

void TimeoutHandler::UnregisterTimeout() noexcept
{
    assert(loop_ && timer_watcher_ && loop_->IsInLoopThread());

    ev_timer_stop(loop_->GetEventLoopBackend(), timer_watcher_.get());
}

bool TimeoutHandler::IsRegistered() const noexcept
{
    assert(loop_ && timer_watcher_ && loop_->IsInLoopThread());

    return ev_is_active(timer_watcher_.get());
}

} // namespace fotamaster
} // namespace seres
