#include "ev_loop/signal_manager.h"
#include <cassert>

#include "base/singleton.h"
#include "ev_loop/eventloop.h"
#include "ev_loop/eventloop_manager.h"

namespace seres
{
namespace fotamaster
{

SignalManager::SignalManager(EventLoop *loop)
    : Signal<PERSON><PERSON><PERSON>(loop), signal_handlers_()
{
    // do nothing
}

SignalManager::~SignalManager()
{
    signal_handlers_.clear();
}

std::optional<ErrCode> SignalManager::RegisterHandler(int signum,
                                                      SignalCallback &&callback)
{
    auto retval = SignalHandler::RegisterSignal(signum);
    if (retval)
    {
        return retval;
    }

    auto result = signal_handlers_.emplace(signum, std::move(callback));
    assert(result.second);

    return std::optional<ErrCode>{};
}

void SignalManager::UnregisterHandler(int signum)
{
    auto iter = signal_handlers_.find(signum);
    if (iter != signal_handlers_.cend())
    {
        signal_handlers_.erase(iter);
        SignalHandler::UnregisterSignal(signum);
    }
}

void SignalManager::HandleSignal(int signum) noexcept
{
    auto iter = signal_handlers_.find(signum);
    if (iter != signal_handlers_.cend())
    {
        iter->second(signum);
    }
}

} // namespace fotamaster
} // namespace seres