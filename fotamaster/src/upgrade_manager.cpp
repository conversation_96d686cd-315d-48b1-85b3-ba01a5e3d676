#include "upgrade_manager.h"
#include "base/singleton.h"
#include "dds_service_manager/duc_service_manager.h"
#include "ev_loop/eventloop_manager.h"
#include "handle_reason.h"
#include "logger/logger.h"
#include <cassert>

namespace seres
{
namespace fotamaster
{

UpgradeManager::UpgradeManager(DUCServiceManager *duc_service_manager)
    : duc_srv_manager_{duc_service_manager}
{
    main_loop_ = base::Singleton<EventLoopManager>::Instance().GetDefaultLoop();
    assert(main_loop_ != nullptr);

    progress_manager_ = std::make_unique<UpgradeProgressManager>();
    assert(progress_manager_);

    auto retval = Initialize();
    assert(retval);
}

bool UpgradeManager::CheckUpgradeCondition()
{
    // 更新条件检查
    auto all_ok{true};
    for (const auto &duc_type : domain_list_)
    {
        if (ReturnCode::OK != duc_srv_manager_->checkUpdateCondition(duc_type))
        {
            all_ok = false;
        }
    }
    return all_ok;
}

bool UpgradeManager::StartUpgrade(const UpdateMode &upgrade_mode,
                                  const UpdateInfoMap &info)
{
    auto all_ok{true};
    for (const auto &iter : info)
    {
        if (std::find(domain_list_.begin(), domain_list_.end(), iter.first) !=
            domain_list_.end())
        {
            //向进度拟合管理器添加要升级的设备
            progress_manager_->AddUpdateDevice(iter.first,
                                               iter.second.updateDeviceLists());

            if (ReturnCode::OK != duc_srv_manager_->startUpdate(iter.first,
                                                                upgrade_mode,
                                                                iter.second))
            {
                all_ok = false;
            }
        }
    }

    //开始定时，周期性计算总体进度并触发回调
    progress_manager_->Start();

    return all_ok;
}

bool UpgradeManager::CancelUpgrade()
{
    return false;
}

bool UpgradeManager::Initialize()
{
    //初始化进度拟合管理器
    auto ret = progress_manager_->Initialize(
        [this](uint8_t progress,
               bool allFinished,
               std::vector<EachUpdateProgressInfo> eachProgress) {
            if (progress_callback_)
            {
                ProgressStatus progress_status;
                progress_status.total_progress = progress;
                progress_status.all_finished = allFinished;
                progress_status.each_progress = eachProgress;
                progress_callback_(progress_status);
            }
            else
                LOG_ERROR("Update overall progress callback not registered");

            if (allFinished)
            {
                duc_srv_manager_->subscribeUpdateProgress(DUCType::CDC,
                                                          nullptr,
                                                          true);
                progress_manager_->Reset();
                progress_manager_->Stop();
            }
        },
        0.5);
    if (!ret)
    {
        LOG_ERROR("ProgressManager Initialize failed");
        return false;
    }
    LOG_WARN("upgrade progress initialized...");
    // 注册下载进度回调
    auto UpdateSubCallback = [this](DUCType type) -> bool {
        auto retval = duc_srv_manager_->subscribeUpdateProgress(
            type,
            [this, type](const UpdateProgress &progress) -> void {
                HandleUpgradeProgress(type, progress);
            });

        retval &= duc_srv_manager_->subscribeCheckUpdateConditionResult(
            type,
            [this, type](const CommonStatus &result) -> void {
                if (!HandleUpgradeStageErr(type, result))
                {
                    LOG_ERROR("Handle %s upgrade condition failed",
                              ducTypeToString(type).c_str());
                }
            });
        return retval;
    };

    auto retval = UpdateSubCallback(DUCType::CDC);
    retval &= UpdateSubCallback(DUCType::MDC);
    retval &= UpdateSubCallback(DUCType::ZCU);

    return retval;
}

void UpgradeManager::HandleUpgradeProgress(DUCType type,
                                           const UpdateProgress &progress)
{
    main_loop_->RunInLoopThread([this, type, progress]() -> void {
        assert(main_loop_->IsDefaultLoop());
        LOG_INFO("recv %s upgrade progress info: ",
                 ducTypeToString(type).c_str());
        for (const auto &info : progress.progressLists())
        {
            LOG_INFO("  device: %s, progress: %d%%, status: %d",
                     info.deviceName().c_str(),
                     info.progressPercent(),
                     static_cast<int>(info.status().status()));
            // 如果检测到某一件升级失败，抛出异常，后续处理需要重试或者回滚
            if (!HandleUpgradeStageErr(type, info.status()))
            {
                LOG_ERROR("device name %s upgrade failed",
                          info.deviceName().c_str());
                // TODO 重试 or 回滚
            }
        }
        // 进度拟合
        progress_manager_->onUpdateProgress(type, progress);
    });
}

bool UpgradeManager::HandleUpgradeStageErr(DUCType type,
                                           const CommonStatus &result)
{
    LOG_INFO("Handle %s upgrade stage err..", ducTypeToString(type).c_str());
    if ((result.status() == Status::kStatusFailed) && fail_reason_callback_)
    {
        auto reason = static_cast<uint32_t>(result.reason());
        LOG_ERROR("Check upgrade stage failed, reason: %u", reason);
        auto convert_reason = ReasonConvert(type, reason);
        if (ReasonTag(convert_reason).IsValid())
        {
            fail_reason_callback_(convert_reason);
        }
        return false;
    }
    return true;
}

uint32_t UpgradeManager::ReasonConvert(DUCType type, uint32_t reason)
{
    LOG_INFO("reason: %u", reason);
    auto domain{ReasonTag::Domain::kDomainNR};
    switch (type)
    {
    case DUCType::CDC:
    {
        domain = ReasonTag::Domain::kDomainCDC;
        break;
    }
    case DUCType::MDC:
    {
        domain = ReasonTag::Domain::kDomainMDC;
        break;
    }
    case DUCType::ZCU:
    {
        domain = ReasonTag::Domain::kDomainZCU;
        break;
    }
    default:
        LOG_ERROR("Unknown duc type: %d", static_cast<int32_t>(type));
        return ReasonTag::kInvalidIndex;
    }
    auto result = ReasonTag(reason, domain).GetValue();
    return result;
}

} // namespace fotamaster
} // namespace seres
