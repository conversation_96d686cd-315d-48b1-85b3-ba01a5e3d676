# EEA3.0 OTA Master(VUC)设计方案

## 车端升级管理方案

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6Z42J29veTO1LQ0g/f6eeade3f3e143688ceae71d37446fd51680.png)

## OTA Master(VUC)框架

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6Z42J29veTO1LQ0g/55a92b5e0988464189646e46e0cf5d721680.png)

## OTA Master(VUC)功能需求

OTA主控单元，功能包括车云交互，OTA任务处理，整车版本管理、控制整车域控分布式下载、控制整车安全刷写、回滚策略、OTA模式控制、整车高压电源控制、OTA前置条件检查，唤醒车辆（远程下载或升级），上传 OTA 升级日志，下载/升级进度拟合等。

|  **功能单元**  |  **一级功能**  |  **二级功能**  |
| --- | --- | --- |
|  OTA Master  |  车云建链  |  OTA server proxy（MQTT<->DDS）服务调用  |
|  |  OTA设置  |  接收并存储 下载方式设置  |
|  |  |  接收并存储 是否预安装设置  |
|  |  版本管理  |  版本收集与整理  |
|  |  |  版本上报  |
|  |  |  版本校验（升级完是否回滚需要）  |
|  |  OTA任务管理  |  任务解析  |
|  |  |  新版本通知推送  |
|  |  整车升级模式控制（usagemode）  |   |
|  |  下载控制  |  查询下载条件  |
|  |  |  通知DUC启动/暂停/恢复下载  |
|  |  |  查询各DUC下载进度  |
|  |  |  拟合下载进度  |
|  |  |  拟合进度/失败结果推送   |
|  |  升级控制  |  查询各DUC升级条件  |
|  |  |  通知各DUC升级  |
|  |  |  查询各DUC下所有件升级进度  |
|  |  |  拟合升级进度  |
|  |  |  各件进度和拟合进度/失败结果推送  |
|  |  退出控制  |  取消安装  |
|  |  |  激活控制  |
|  |  |  清除故障码  |
|  |  回滚控制  |  检测到版本不一致，执行回滚 涉及关联件的回滚。  |
|  |  前置条件检查：休眠拉起时条件信息不及时的考虑  |  收集前置条件信息  |
|  |  |  条件检查（OTA任务推送中的阈值）  |
|  |  唤醒车辆控制（远程和静默升级时需要）  |   |
|  |  整车高/低压电源控制  |   |
|  |  日志管理（集体看需求）  |  日志收集  |
|  |  |  日志上报，并下发上报任务给DUC  |
|  |  DDS服务管理  |  DUC、OTA master proxy，CanDatabease，HMI  |

## 模块分解与功能映射

|  **模块名称**  |  **功能描述**  |
| --- | --- |
|  **TaskManager**  |  OTA任务解析、分布式任务分发（座舱DUC/智驾DUC并行）  |
|  **DDSServiceManager**  |  各件交互的DDS服务管理  |
|  **PersistenceSetting**  |  用户设置以及状态流转等持久化，持久化方式  |
|  **VersionManager**  |  版本收集（有3次重试机制）、校验、上报  |
|  **StateMachine**  |  整车升级状态机管理（Idle/Download/Upgrade等）  |
|  **DownloadCtrl**  |  1.  下载方式管理      2.  下载条件检查、DUC下载控制（启动/暂停/恢复）       |
|  **UpgradeCtrl**  |  1.  升级方式管理      2.  升级条件检查、DUC升级/回滚控制、进度监控       |
|  **ExitCtrl**  |  取消升级、激活、清dtc  |
|  **RollbackCtrl**  |  回滚策略执行（版本比对、安全恢复）  |
|  **PowerManager**  |  整车高压/低压电源控制（升级前供电、异常断电保护，智能补电等）  |
|  **PreCheckCondition**  |  前置条件检查（网络、电量、车速等阈值）  |
|  **LogCollector**  |  日志收集、压缩、上报至云端  |
|  **VehicleWakeup**  |  远程唤醒车辆  |
|  **ProgressFitting**  |  下载/升级进 度拟合  |
|  **FaultManager**  |  故障管理  |

## VUC模块中设计主控制状态流转

### 主要状态

|  **状态**  |  **描述**  |  前置条件  |  触发条件  |
| --- | --- | --- | --- |
|  Idle  |  空闲状态：没有升级任务  |  /  |  /  |
|  Download {start, stop, resume,}  |  下载状态：OTA任务下发  |  本地下载(&)： A:UsageMode=Convenience或Driving B:PowerMode=On  |  本地下载(\|)： A:VUC 接收到 OTA 平台推送的 OTA 任务（自动下载） ; B:用户通过 CDC 大屏点击“立即下载” ; C:CDC-HMI 设定为自动下载;  |
|  |  |  远程下载(&) A:手机 APP 接收到 OTA 任务推送； B:升级包未下载或下载未完成；  |  远程下载 A:用户通过手机 APP 点击立即下载；  |
|  |  |  静默下载 A:UsageMode=Inactive 或 Convenience；  |  静默下载 A:OTA Server 发布静默升级任务  |
|  SeamlessUpgrade  |  无感安装状态  |  Download状态完成  |  用户启用无感预安装功能  |
|  SeamlessUpgradeFinish  |  无感安装完成状态  |   |  无感安装完成  |
|  PreUpgrade  |  有感的前安装：各升级方式处理如高低电压控制等；前置条件检测  |  本地立即升级(&) A：UsageMode=Convenience； B： PowerMode=ON； C：升级包下载完成；  |  本地立即升级 用户点击立即升级； 本地预约升级 用户在中控屏上设置的预约升级时间到  |
|  |  |  远程立即升级(&) A： 车端升级包下载完成; B： 手机 APP 激活; C： 车端未升级；  |  远程立即升级 A： 用户在手机 APP 上点击立即升级； 远程预约升级 A： 用户在手机 APP 上设置的预约升级时间到；  |
|  |  |  静默升级  |  静默升级  |
|  Upgrading  |  有感正式安装  |  升级前置条件满足  |  PreUpgrade状态完成  |
|  Rollback  |  回滚状态  |   |  有件升级失败后的回滚  |
|  Fault  |  故障状态  |   |  各阶段有故障  |
|  Exit  |  退出升级  |  各域全部升级完成  |   |

注：

1.  其中download状态：包含三种下载方式管理（立即下载，远程下载，静默下载）；包含三种下载状态管理（启动下载、暂停下载、恢复下载）；
    
2.  其中PreUpgrade状态：管理三种升级方式的前置处理（本地立即/预约升级、远程立即/预约升级、静默升级）；
    
3.  其中Fault状态：各阶段的故障都流传到Fault状态处理；
    
4.  其中Exit状态：管理取消升级、激活等管理。
    

### 粗略流转图

```plantuml
@startuml
  state IdleState <<State>>
  state AutoDownloadChoice <<choice>>
  state DownloadState <<State>>
  state SeamlessUpgradeChoice <<choice>>
  state SeamlessUpgradeState <<State>>
  state SeamlessUpgradeFinishState <<State>>
  state PreUpgradeState <<State>>
  state UpgradingChoice <<choice>>
  state UpgradingState <<State>>
  state UpgradeTimerChoice <<choice>>
  state RollbackState <<State>>
  state FaultState <<State>>
  state ExitState <<State>>
 
  [*] --> IdleState

  IdleState : 版本检测
  IdleState: 任务解析
  IdleState --> DownloadState: 1. 静默升级任务;\n2. 设置了自动下载;
  IdleState -[#DD00AA,dashed]-> DownloadState: 上一次任务未下载/下载未完成
  IdleState --> AutoDownloadChoice: 用户点击下载
  
  IdleState -[#DD00AA,dashed]-> SeamlessUpgradeState: 上一次任务预安装中断后上电
  IdleState -[#DD00AA,dashed]-> PreUpgradeState: 上一次任务未安装


  AutoDownloadChoice --> DownloadState: [同意]
  AutoDownloadChoice --> IdleState: [暂不]

  DownloadState: 启动下载\n暂停下载\n恢复下载
  DownloadState -[#red]-> FaultState: 下载故障
  DownloadState --> SeamlessUpgradeChoice: 是否无感预安装

  SeamlessUpgradeChoice --> SeamlessUpgradeState: [yes]
  SeamlessUpgradeChoice --> UpgradingChoice: [no]

  SeamlessUpgradeState -[#red]-> FaultState: 无感升级故障:\n[版本检测不匹配]
  SeamlessUpgradeState --> SeamlessUpgradeFinishState: 1. 无感升级完成\n2. 除版本不匹配之外的升级失败

  SeamlessUpgradeFinishState -->UpgradingChoice
  UpgradingChoice --> PreUpgradeState: 用户同意安装

  PreUpgradeState: 前置条件检查
  PreUpgradeState: 计时处理
  PreUpgradeState --> UpgradeTimerChoice: 通知HMI 2min倒计时
  PreUpgradeState -[#red]-> FaultState: 前置条件不满足故障

  UpgradeTimerChoice --> UpgradingState: [yes]倒计时时间到
  UpgradeTimerChoice --> IdleState: [no]倒计时期间用户点击暂不升级

  UpgradingState --> ExitState: 所有域都升级成功
  UpgradingState --> RollbackState: 升级失败
  UpgradingState -[#red]-> FaultState: 升级故障[高低压上电失败]

  RollbackState -[#red]-> FaultState: 升级故障\n[任何一个件失败]
  RollbackState: 单分区件回滚到上一版本
  RollbackState: 双分区的件进行重试
  FaultState --> IdleState

  ExitState: 取消升级
  ExitState: 激活
  ExitState: 清DTC
  ExitState -[#red]-> FaultState: 激活时版本校验失败
  ExitState --> [*]
@enduml
  
```

## VUC和DUC正向交互时序

![image](https://alidocs.oss-cn-zhangjiakou.aliyuncs.com/a/6Z42J29veTO1LQ0g/158fd9f5329c4d648847215c6656f0b71680.png)