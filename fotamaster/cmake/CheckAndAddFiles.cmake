# check if the files are exist, add add them to files_list
#
# e.g. check_and_add_files(src_list "path/to/" src/a.cc src/b.cc)
#
function(check_and_add_files files_list path_prefix)
  set(new_list "${${files_list}}")
  foreach(file ${ARGN})
    get_filename_component(full_file "${path_prefix}/${file}" ABSOLUTE)
    if (NOT EXISTS "${full_file}")
      message(FATAL_ERROR "${full_file} is NOT exist!")
    endif()

    list(APPEND new_list "${path_prefix}/${file}")
  endforeach()

  set(${files_list} "${new_list}" PARENT_SCOPE)
endfunction()