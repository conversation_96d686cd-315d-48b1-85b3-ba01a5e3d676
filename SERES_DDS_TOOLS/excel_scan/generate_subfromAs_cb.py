from excel_parser import ExcelParser
import platform
import os


class GenerateSubFromAsCb:
    SPACE: str = "    "

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate tk file.")
        self._out_path = os.path.join(parser.output_dir, "subfromAs_Cb")
        self._service = parser._service

    def generate(self) -> int:
        content = ""
        content += self.contents()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "subfromAsCb")))
        return 0

    def contents(self):
        content = (f"subfromAs_acquisitionState_interface_cb cb = {{"
                   f"{self.content()}"
                   f"}};")
        return content

    def content(self) -> str:
        content = ""

        for service in self._service:
            for method in service.method:
                content += (f"{method.method_name}, ")
        return content[:-2]

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


if __name__ == "__main__":
    parser = ExcelParser("test\\【JM3.0项目】整车服务接口定义_控制器对外通信7.xlsx", "output")
    generate_data_reporting = GenerateSubFromAsCb(parser)
    generate_data_reporting.generate()
