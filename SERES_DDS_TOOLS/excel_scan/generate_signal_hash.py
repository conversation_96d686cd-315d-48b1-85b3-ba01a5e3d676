from excel_parser import ExcelParser
import platform
import os


class Public:
    SPACE: str = "    "

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


class GenerateSignalHashJava(Public):
    package_name: str = "package com.seres.dds.server.consts\n\n"

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate signal hash file.")
        self._out_path = os.path.join(parser.output_dir, "signal_hash")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.package_name
        content += self.class_content()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "signal_hash.java")))
        return 0

    def class_content(self) -> str:
        content = ("enum class SignalHash(val hashValue: Int) {"
                   f"{self.contents()}"
                   "\n}")
        return content

    def contents(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE}{sub_data['Parameter Name'].replace('-', '_')}_hash ({sub_data['Hash']}),"
        return content[:-1]


class GenerateSignalHashCpp(Public):
    include_header: str = ("#include <iostream>\n"
                           "#include \"signalHash.h\"\n\n")

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate c++ signal hash source file.")
        self._out_path = os.path.join(parser.output_dir, "signal_hash_cpp")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.include_header
        content += self.function_implementation()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "signalHash.cpp")))
        return 0

    def function_implementation(self) -> str:
        content = ("// 辅助函数，用于获取每个枚举常量对应的 hashValue"
                   "\nint getHashValue(SignalHash signal) {"
                   f"\n{self.SPACE}switch (signal) {{"
                   f"{self.contents()}"
                   f"\n{self.SPACE * 2}default: return -1; // 处理未知枚举值的情况"
                   f"\n{self.SPACE}}}"
                   "\n}")
        return content

    def contents(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE * 2}case SignalHash::{sub_data['Parameter Name'].replace('-', '_')}_Hash: return {sub_data['Hash']};"
        return content


class GenerateSignalHashH(Public):
    include_header: str = ("#ifndef SIGNAL_HASH\n"
                           "#define SIGNAL_HASH\n\n")

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate c++ signal hash header file.")
        self._out_path = os.path.join(parser.output_dir, "signal_hash_h")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.include_header
        content += self.enum_class()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "signalHash.h")))
        return 0

    def enum_class(self) -> str:
        content = ("// 定义 SignalHash 枚举类型"
                   "\nenum class SignalHash {"
                   f"{self.contents()}"
                   "\n};"
                   "\n"
                   "\n// 辅助函数，用于获取每个枚举常量对应的 hashValue"
                   "\nint getHashValue(SignalHash signal);"
                   "\n"
                   "\n#endif")
        return content

    def contents(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE}{sub_data['Parameter Name'].replace('-', '_')}_Hash,"
        return content[:-1]
