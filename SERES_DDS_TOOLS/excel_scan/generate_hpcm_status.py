from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateHPCMStatus:
    SPACE: str = "    "

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate data reporting file.")
        self._out_path = os.path.join(parser.output_dir, "HPCM_Status")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.contents()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "hpcmStatus.idl")))
        return 0

    def contents(self) -> str:
        content = ("module HPCM_status {"
                   "\n"
                   f"\n{self.SPACE}struct DataTransmission {{"
                   f"{self.signal_and_value()}"
                   f"\n{self.SPACE}}};"
                   "\n};")
        return content

    def signal_and_value(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE * 2}uint8 {sub_data['Parameter Name'].replace('-', '')};"
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


if __name__ == "__main__":
    parser = ExcelParser("test\\【JM3.0项目】整车服务接口定义_控制器对外通信5.xlsx", "output")
    generate_data_reporting = GenerateHPCMStatus(parser)
    generate_data_reporting.generate()
