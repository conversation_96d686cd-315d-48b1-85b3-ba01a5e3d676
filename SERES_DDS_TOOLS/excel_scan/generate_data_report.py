from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateDataReport:
    SPACE: str = "    "
    package_name: str = "package com.seres.communication.DataReportSub\n\n"
    import_content: str = \
        ("import DataReporting.DataTransmission\n"
         "import DataReporting.SignalAndvalue\n"
         "import android.os.Bundle\n"
         "import com.seres.dds.commsvc.ipc.IpcClientManager\n"
         "import com.seres.dds.sdk.DataReader\n"
         "import com.seres.dds.sdk.DomainParticipant\n"
         "import com.seres.dds.sdk.Topic\n"
         "import com.seres.dds.server.IpcServer.Companion.dataListenerMap\n"
         "import com.seres.dds.server.consts.InvokeConsts\n"
         "import com.seres.dds.server.consts.SignalHash\n\n")

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate data report file.")
        self._out_path = os.path.join(parser.output_dir, "data_report")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.package_name
        content += self.import_content
        content += self.subscriber_function()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "data_report.java")))
        return 0

    def subscriber_function(self) -> str:
        content = ("val TAG = \"DataReportingSubscriber-- \""
                   "\n\nfun Subscriber(){"
                   f"\n"
                   f"\n{self.SPACE}println(TAG + \"enter subMain success\")"
                   f"\n"
                   f"\n{self.SPACE}val par = DomainParticipant(1)"
                   f"\n{self.SPACE}val topic = Topic(par, \"HPCM_MCU_STATUS\", DataTransmission())"
                   f"\n{self.SPACE}val reader = DataReader(par, topic)"
                   f"\n"
                   f"\n{self.SPACE}while(true){{"
                   f"\n"
                   f"\n{self.SPACE * 2}println(TAG + \"enter subMain while success\")"
                   f"\n{self.SPACE * 2}val samples = reader.take()"
                   f"\n"
                   f"\n{self.SPACE * 2}samples.sample_list!!.forEach{{ sample ->"
                   f"\n{self.SPACE * 3}println(TAG + \"enter subMain foreach success\")"
                   f"\n{self.SPACE * 3}var data = sample.type as DataTransmission"
                   f"\n"
                   f"\n{self.SPACE * 3}// 将DataTransmission转换为Bundle集合"
                   f"\n{self.SPACE * 3}val bundleList = arrayListOf<Bundle>().apply {{"
                   f"{self.add_sigal_to_bundle()}"
                   f"\n{self.SPACE * 3}}}"
                   f"\n{self.SPACE * 3}// 上报给所有注册的APP（假设使用广播模式）"
                   f"\n{self.SPACE * 3}bundleList.forEach {{ bundle ->"
                   f"\n{self.SPACE * 4}dataListenerMap.keys.forEach {{ appId ->"
                   f"\n{self.SPACE * 5}IpcClientManager.pushData(appId, bundle)"
                   f"\n{self.SPACE * 4}}}"
                   f"\n{self.SPACE * 3}}}"
                   f"\n{self.SPACE * 2}}}"
                   f"\n{self.SPACE * 2}Thread.sleep(1000)"
                   f"\n{self.SPACE}}}"
                   f"\n}}"
                   f"\n{self.add_signal_to_bundle_function()}")
        return content

    def add_sigal_to_bundle(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE * 4}addSignalToBundle(data.{sub_data.get('Parameter Name')}, SignalHash.{sub_data.get('Parameter Name')}_hash.ordinal)"
        return content

    def add_signal_to_bundle_function(self) -> str:
        content = (f"// 扩展函数：SignalAndvalue转Bundle"
                   f"\nprivate fun ArrayList<Bundle>.addSignalToBundle(signal: SignalAndvalue, hash: Int) {{"
                   f"\n{self.SPACE}Bundle().apply {{"
                   f"\n{self.SPACE * 2}putInt(InvokeConsts.KEY_SIGNAL_HASH, hash)"
                   f"\n{self.SPACE * 2}putInt(InvokeConsts.KEY_APP_ID, signal.appid)"
                   f"\n{self.SPACE * 2}putDouble(InvokeConsts.KEY_SIGNAL_VALUE, signal.value)"
                   f"\n"
                   f"\n{self.SPACE * 2}<EMAIL>(this)"
                   f"\n{self.SPACE}}}"
                   f"\n}}")
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")