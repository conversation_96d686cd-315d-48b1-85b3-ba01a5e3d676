from excel_parser import ExcelParser
from datetime import datetime
from topic import *
from service import Service
from typing import List
from data_structure import *
import os
import platform

rpcService = ['HPCC_Service_eSrv.idl']

idl_des="/**\n"\
        "* SOA SPY generate code\n"\
        "* Auto Generate file, Do not modify manually !!\n"\
        "* Generate date: {}\n".format(datetime.now())


class GenerateDDSSpy:

    def __init__(self, parser: ExcelParser):
        print("Start generate DDS Spy")
        self._out_path = parser.output_dir + "/spy"
        self.parser    = parser
        self.inner     = Inner(self.parser)
        

    def generate(self):
        for filename_h, content_h, filename_c, content_c in self.inner.generate():

            filepath_h = self.generate_file_path(os.path.join(self._out_path, filename_h))
            self.write_file(content_h, filepath_h)

            filepath_c = self.generate_file_path(os.path.join(self._out_path, filename_c))
            self.write_file(content_c, filepath_c)


        for service in self.parser.service:
            pass



    def write_file(self, content: str, file_path: str):
        file_content = (f"{idl_des}"
                        f"* Version       : {self.parser.change_data['版本']}\n"
                        f"* Change Log    : {self.parser.change_data['变更内容']}\n"
                        f"* Change Author : {self.parser.change_data['编辑者']}\n"
                        f"* Change Date   : {self.parser.change_data['日期']}\n"
                        f"*/\n\n"
                        f"{content}\n")

        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(file_content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")

    
    def generate_file_path(self, filename: str) -> str:

        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)


class Inner:

    def __init__(self, parser: ExcelParser):
        self.topics = parser.topic
        self.data = parser.data
        self.space  = "    " 

    
    def generate(self):

        for topic in self.topics:
            filename_h = (f"{topic.name}_inner.h")
            filename_c = (f"{topic.name}_inner.c")

            content_h = self.generate_h(topic)
            content_c = self.generate_c(topic, filename_h)

            yield filename_h, content_h, filename_c, content_c


    def generate_h(self, topic:Topic):
        return (f"#pragma once\n"
                f"#include \"{topic.name}.h\"\n"
                f"#ifdef __cplusplus\n"
                f"extern \"C\" {{\n"
                f"#endif\n"
                f"\n"
                f"void* Seres_{topic.name.replace('_STATUS', '')}_{topic.name}_alloc_wrapper(void);\n"
                f"\n"
                f"char* {topic.name}_to_json(void *src);\n"
                f"\n"
                f"#ifdef __cplusplus\n"
                f"}}\n"
                f"#endif\n")
    

    def generate_c(self, topic:Topic, headfile:str):

        datapara = self.generatepara(topic)

        content = (f"#include \"{headfile}\"\n"
                   f"#include \"cJSON.h\"\n"
                   f"\n"
                   f"void* Seres_{topic.name.replace('_STATUS', '')}_{topic.name}_alloc_wrapper(void)\n"
                   f"{{\n"
                   f"    return Seres_{topic.name.replace('_STATUS', '')}_{topic.name}__alloc();\n"
                   f"}}\n"
                   f"\n"
                   f"char* {topic.name}_to_json(void *src)\n"
                   f"{{\n"
                   f"    Seres_{topic.name.replace('_STATUS', '')}_{topic.name} *info = (Seres_{topic.name.replace('_STATUS', '')}_{topic.name} *)src;\n"
                   f"    long start_time = 0;\n"
                   f"    cJSON *root = cJSON_CreateObject();\n"
                   f"    cJSON_AddStringToObject(root, \"topic\", \"{topic.name}\");\n"
                   f"    cJSON *data = cJSON_CreateObject();\n"
                   f"    cJSON_AddItemToObject(root, \"data\", data);\n"
                   f"{datapara}"
                   f"\n"
                   f"    char *json_str = cJSON_PrintUnformatted(root);\n"
                   f"    cJSON_Delete(root);\n"
                   f"    return json_str;\n"
                   f"}}")

        return content

    
    def generatepara(self, topic:Topic):
        content = ""

        for member in topic.members:
            content = content + self.generateinstanceparam(member.parameter_type, member.parameter_Name)
            
        return content

    
    def generateinstanceparam(self, member, name:str, parentList:List[Data]=[], parentnameList:List[str]=[]):
        content = ""

        genmember = next((m for m in self.data if m.type_name == member.type_name), None)
        if genmember == None:
            error_list.append("** Generate Instance Param Error ! {}  type name [{}] not found".format(member.type_name))
            return None

        if genmember.category == "Struct":
            parentList.append(genmember)
            parentnameList.append(name)

            for i in genmember.members:
                content += self.generateinstanceparam(i, i.name, parentList, parentnameList)
        else:
            if len(parentList) == 0:
                content += (f"    cJSON_AddNumberToObject(data, \"{member.type_name}\", info->{name});\n")
            else:
                parentprefix = ""
                for i in parentList:
                    parentprefix = parentprefix + i.type_name + "."
                nameprefix = ""
                for i in parentnameList:
                    nameprefix = nameprefix + i + "."
                
                content += (f"    cJSON_AddNumberToObject(data, \"{parentprefix + member.type_name}\", info->{nameprefix + name});\n")

        return content


    

class InnerClient:
    def __init__(self, services: List[Service]):
        self.services = services
        self.space  = "    " 

    
    def generate(self):

        for service in self.services:
            if service.service_name in rpcService:
                pass
            else:
                pass


