from excel_parser import ExcelParser
import platform
import os


class GeneratePublishC:
    SPACE: str = "    "
    include_header: str = ("#include \"DataReporting_publisher.hpp\"\n"
                           "#include \"../sub/customMap/customMap.hpp\"\n\n"
                           "extern CustomMap customMap;\n")

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate c publish source file.")
        self._out_path = os.path.join(parser.output_dir, "publish_c")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.include_header
        content += self.function_implementation1()
        content += self.function_implementation2()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "publish.c")))
        return 0

    def function_implementation1(self) -> str:
        content = ("int DataReport(const std::vector<std::tuple<int, int, std::any>>& changedNodes) {"
                   f"\n{self.SPACE}try {{"
                   f"\n{self.SPACE * 2}std::cout << \"=== [Publisher] Create writer.\" << std::endl;"
                   f"\n{self.SPACE * 2}dds::domain::DomainParticipant participant(domain::default_id());"
                   f"\n{self.SPACE * 2}dds::topic::Topic<DataReporting::DataTransmission> topic(participant, \"DataReporting_Msg\");"
                   f"\n{self.SPACE * 2}dds::pub::Publisher publisher(participant);"
                   f"\n{self.SPACE * 2}dds::pub::DataWriter<DataReporting::DataTransmission> writer(publisher, topic);"
                   f"\n{self.SPACE * 2}std::cout << \"=== [Publisher] Waiting for subscriber.\" << std::endl;"
                   f"\n{self.SPACE * 2}while (writer.publication_matched_status().current_count() == 0) {{"
                   f"\n{self.SPACE * 3}std::this_thread::sleep_for(std::chrono::milliseconds(20));"
                   f"\n{self.SPACE * 2}}}"
                   f"\n"
                   f"\n{self.SPACE * 2}// 调用函数进行赋值"
                   f"\n{self.SPACE * 2}DataReporting::DataTransmission msg = assignChangedNodesToDataTransmission(changedNodes);"
                   f"\n{self.SPACE * 2}std::cout << \"=== [Publisher] Write sample: \" << msg << std::endl;"
                   f"\n{self.SPACE * 2}writer.write(msg);"
                   f"\n{self.SPACE * 2}std::cout << \"=== [Publisher] Waiting for sample to be accepted.\" << std::endl;"
                   f"\n{self.SPACE * 2}while (writer.publication_matched_status().current_count() > 0) {{"
                   f"\n{self.SPACE * 3}std::this_thread::sleep_for(std::chrono::milliseconds(50));"
                   f"\n{self.SPACE * 2}}}"
                   f"\n{self.SPACE}}}catch (const dds::core::Exception& e) {{"
                   f"\n{self.SPACE * 2}std::cerr << \"=== [Publisher] Exception: \" << e.what() << std::endl;"
                   f"\n{self.SPACE * 2}return EXIT_FAILURE;"
                   f"\n{self.SPACE}}}"
                   f"\n{self.SPACE}std::cout << \"=== [Publisher] Done.\" << std::endl;"
                   f"\n{self.SPACE}return EXIT_SUCCESS;"
                   "\n}\n\n")
        return content

    def function_implementation2(self) -> str:
        content = \
            (
                "DataReporting::DataTransmission assignChangedNodesToDataTransmission(const std::vector<std::tuple<int, int, std::any>>& changedNodes) {"
                "\n"
                f"\n{self.SPACE}DataReporting::DataTransmission data;"
                f"\n"
                f"\n{self.SPACE}for (const auto& tuple : changedNodes) {{"
                f"\n{self.SPACE * 2}int appid = std::get<0>(tuple);"
                f"\n{self.SPACE * 2}int32_t hash = std::get<1>(tuple);"
                f"\n"
                f"\n{self.SPACE * 2}try {{"
                f"\n{self.SPACE * 3}double value = std::any_cast<double>(std::get<2>(tuple));"
                f"\n{self.SPACE * 3}std::string service = customMap.findServiceAttributeByHash(hash);"
                f"\n"
                f"\n{self.SPACE * 3}if (!service.empty()) {{"
                f"\n{self.SPACE * 4}std::cout << \"Found service: \" << service << std::endl;"
                f"\n"
                f"\n{self.SPACE * 4}DataReporting::signalAndvalue signal(appid, hash, value);"
                f"\n"
                f"{self.service_assign()}"
                f"\n{self.SPACE * 3}}} else {{"
                f"\n{self.SPACE * 4}std::cout << \"Service not found.\" << std::endl;"
                f"\n{self.SPACE * 3}}}"
                f"\n{self.SPACE * 2}}}catch (const std::bad_any_cast& e) {{"
                f"\n{self.SPACE * 3}std::cerr << \"Bad any cast for hash \" << hash << \": \" << e.what() << std::endl;"
                f"\n{self.SPACE * 2}}}"
                f"\n{self.SPACE}}}"
                f"\n{self.SPACE}return data;"
                "\n}")

        return content

    def service_assign(self):
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                if not bool(content):
                    content += f"\n{self.SPACE * 4}if (service == \"{sub_data['Parameter Name'].replace('-', '')}\") {{"
                else:
                    content += f"else if (service == \"{sub_data['Parameter Name'].replace('-', '')}\") {{"
                content += (f"\n{self.SPACE * 5}data.{sub_data['Parameter Name'].replace('-', '')}(signal);"
                            f"\n\n{self.SPACE * 4}}}")
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")
