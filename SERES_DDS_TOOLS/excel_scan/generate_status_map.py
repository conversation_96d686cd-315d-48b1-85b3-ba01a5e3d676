from excel_parser import ExcelParser
import platform
import os


class GenerateStatusMap:
    SPACE = "    "

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate status map file.")
        self._out_path = os.path.join(parser.output_dir, "status_map")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += ("KeyValuePair map[] = {"
                    f"{self.content()}"
                    "\n}")
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, f"status_map")))
        return 0

    def content(self):
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE}{{\"{sub_data['Parameter Name']}\", {sub_data['Hash']}}},"
        return content[:-1]

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


if __name__ == "__main__":
    parser = ExcelParser("test\\【JM3.0项目】整车服务接口定义_控制器对外通信4.xlsx", "output")
    generate_status_map = GenerateStatusMap(parser)
    generate_status_map.generate()
