from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateDataReporting:
    SPACE: str = "    "

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate data reporting file.")
        self._out_path = os.path.join(parser.output_dir, "data_reporting")
        self.pub_sub_data = parser.pub_sub_data

    def generate(self) -> int:
        content = ""
        content += self.subscriber_function()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "data_reporting.java")))
        content = self.print_param()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "print_param.java")))
        return 0

    def subscriber_function(self) -> str:
        content = ("module DataReporting {"
                   "\n"
                   f"\n{self.SPACE}struct signalAndvalue {{"
                   f"\n{self.SPACE * 2}int32 appid;"
                   f"\n{self.SPACE * 2}int32 hash;"
                   f"\n{self.SPACE * 2}double value;"
                   f"\n{self.SPACE}}};"
                   f"\n"
                   f"\n{self.SPACE}struct DataTransmission {{"
                   f"{self.signal_and_value()}"
                   f"\n{self.SPACE}}};"
                   "\n};")
        return content

    def signal_and_value(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\n{self.SPACE * 2}signalAndvalue {sub_data['Parameter Name'].replace('-', '')};"
        return content

    def print_param(self) -> str:
        content = ""
        for sub_data in self.pub_sub_data:
            if sub_data.get('Parameter Name') and sub_data.get('Hash'):
                content += f"\nprintln(\"data.{sub_data['Parameter Name'].replace('-', '')} is: \"+data.{sub_data['Parameter Name'].replace('-', '')})"
        return content[1:]

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


if __name__ == "__main__":
    parser = ExcelParser("test\\【JM3.0项目】整车服务接口定义_控制器对外通信4.xlsx", "output")
    generate_data_reporting = GenerateDataReporting(parser)
    generate_data_reporting.generate()
