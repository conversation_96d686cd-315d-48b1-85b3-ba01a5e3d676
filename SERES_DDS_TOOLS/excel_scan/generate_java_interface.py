from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateJavaInterface:
    SPACE: str = "    "
    package_name: str = "package com.seres.dds.server.consts;\n\n"
    str_key_content: str = \
        (f"\n{SPACE}String KEY_METHOD_ID = \"method_name\";\n"
         f"{SPACE}String KEY_RESULT = \"invoke_result\";\n"
         f"{SPACE}String KEY_CHANGE_PROP_LIST = \"change_prop_list\";\n"
         f"{SPACE}String KEY_CHANGE_PROP = \"change_prop\";\n"
         f"{SPACE}String KEY_CHANGE_PROP_VALUE = \"change_value\";\n"
         f"{SPACE}String KEY_CHANGE_VALUE_TYPE = \"change_value_type\";\n"
         f"{SPACE}String KEY_SIGNAL_HASH = \"signal_hash\";\n"
         f"{SPACE}String KEY_APP_ID = \"app_id\";\n"
         f"{SPACE}String KEY_SIGNAL_VALUE = \"signal_value\";\n\n")

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate java interface file.")
        self._out_path = os.path.join(parser.output_dir, "java_interface")
        self._service = parser._service

    def generate(self) -> int:
        content = ""
        content += self.package_name
        content += self.invoke_consts()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "test.java")))
        return 0

    def invoke_consts(self) -> str:
        content = ("public interface InvokeConsts {"
                   f"\n{self.str_key_content}"
                   f"\n"
                   f"\n{self.function_interface()}"
                   "\n}")
        return content

    def function_interface(self) -> str:
        content = ""

        for service in self._service:
            for method in service.method:
                content += (
                    f"\n{self.SPACE}/**"
                    f"\n{self.SPACE} * {method.description}"
                    f"\n{self.SPACE} */"
                    f"\n{self.SPACE}interface Method{method.method_name[0].upper()}{method.method_name[1:]} {{"
                    f"\n"
                    f"\n{self.SPACE * 2}String NAME = \"{method.method_name}\";"
                    f"\n{self.params(method)}"
                    f"\n{self.SPACE}}}\n\n")
        return content[:-2]

    def params(self, method: Method) -> str:
        content = ""
        if method.input_param:
            for key, value in method.input_param.items():
                content += f"\n{self.SPACE * 2}String PARAM_{value.upper()} = \"{value}\";"
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")