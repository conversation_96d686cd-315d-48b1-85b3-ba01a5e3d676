from excel_parser import ExcelParser
import platform
import os
import re


class GenerateRpcServerC:
    SPACE = "    "
    include_part: str = ("#include \"server.hpp\"\n"
                         "#include \"../sub/customMap/customMap.hpp\"\n\n")

    def __init__(self) -> None:
        print("Start generate c++ rpc server source file.")
        self._out_path = ""
        self._rpc_server_input = ""

    @property
    def rpc_server_input(self):
        return self._rpc_server_input

    @rpc_server_input.setter
    def rpc_server_input(self, value):
        self._rpc_server_input = value

    @property
    def out_path(self):
        return self._out_path

    @out_path.setter
    def out_path(self, value):
        self._out_path = value

    def server_data_writer_listener(self):
        return ("\nclass ServerDataWriterListener : public dds::pub::NoOpDataWriterListener<ReplyType>{"
                "\n"
                "\npublic:"
                f"\n{self.SPACE}ServerDataWriterListener() {{"
                f"\n{self.SPACE * 2}std::cout << \"Creating server data writer listener for topic: \"<< std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_offered_deadline_missed(dds::pub::DataWriter<ReplyType>& writer, const dds::core::status::OfferedDeadlineMissedStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Writer] on_offered_deadline_missed\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_offered_incompatible_qos(dds::pub::DataWriter<ReplyType>& writer, const dds::core::status::OfferedIncompatibleQosStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Writer] on_offered_incompatible_qos\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_liveliness_lost(dds::pub::DataWriter<ReplyType>& writer, const dds::core::status::LivelinessLostStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Writer] on_liveliness_lost\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_publication_matched (dds::pub::DataWriter<ReplyType>& writer,const dds::core::status::PublicationMatchedStatus& status)  {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Writer] on_publication_matched\" << std::endl;"
                f"\n{self.SPACE}}}"
                "\n};")

    def server_data_reader_listener(self):
        return ("\nclass ServerDataReaderListener : public dds::sub::NoOpDataReaderListener<RequestType>{"
                "\n"
                "\npublic:"
                f"\n{self.SPACE}ServerDataReaderListener() {{"
                f"\n{self.SPACE * 2}std::cout << \"Creating server data reader listener for topic: \"<< std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_requested_deadline_missed(dds::sub::DataReader<RequestType>& reader, const dds::core::status::RequestedDeadlineMissedStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader] on_requested_deadline_missed\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_requested_incompatible_qos(dds::sub::DataReader<RequestType>& reader, const dds::core::status::RequestedIncompatibleQosStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader] on_requested_incompatible_qos\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_sample_rejected(dds::sub::DataReader<RequestType>& reader, const dds::core::status::SampleRejectedStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader] on_sample_rejected\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_liveliness_changed(dds::sub::DataReader<RequestType>& reader, const dds::core::status::LivelinessChangedStatus& status)  {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader]   on_liveliness_changed : [ \" << status.alive_count() << \" ]\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_data_available(dds::sub::DataReader<RequestType>& reader) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader]   on_data_available\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_subscription_matched(dds::sub::DataReader<RequestType>& reader, const dds::core::status::SubscriptionMatchedStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader]   on_subscription_matched\" << std::endl;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}void on_sample_lost(dds::sub::DataReader<RequestType>& reader, const dds::core::status::SampleLostStatus& status) {{"
                f"\n{self.SPACE * 2}std::cout << \"server-[Reader]   on_sample_lost\" << std::endl;"
                f"\n{self.SPACE}}}"
                "\n};")

    def generate(self) -> int:
        content = ""
        content += self.include_part
        content += self.server_data_writer_listener()
        content += "\n\n"
        content += self.server_data_reader_listener()
        content += "\n\n"
        content += self.server_implemention()
        self.write_file(content, self.generate_file_path(os.path.join(self.out_path, "rpc_server_c", "rpc_server.c")))
        return 0

    def server_implemention(self) -> str:
        content = ""
        module, interface_name, interface_content = self.interface_info()
        content += f"\nclass RPCTest_impl : public {module}::Interface_{interface_name}_BASE{{"
        for func_info in interface_content:
            return_code = func_info['return_code']
            function_name = func_info['function_name']
            param_str = ""
            on_off_sts = ""
            for param in func_info['params']:
                param_str += f"{module}::{param[1]} &onOffSts, "
                on_off_sts = f"onOffSts.onOffSts({module}::{param[1]}::ON);"

            content += (f"\n{self.SPACE}virtual {module}::{return_code} {function_name}({param_str[:-2]}) {{"
                        f"\n{self.SPACE * 2}std::cout << \"enter {function_name} success\" << std::endl;"
                        f"\n"
                        f"\n{self.SPACE * 2}/*根据服务获取对应的hash*/"
                        f"\n{self.SPACE * 2}std::string {function_name} = \"{function_name}\";"
                        f"\n{self.SPACE * 2}int {function_name}_hashValue = getValueByKey(map, mapSize, {function_name});"
                        f"\n{self.SPACE * 2}if ({function_name}_hashValue != -1) {{"
                        f"\n{self.SPACE * 3}std::cout << \"Hash value for \" << {function_name} << \" is: \" << {function_name}_hashValue << std::endl;"
                        f"\n{self.SPACE * 2}}} else {{"
                        f"\n{self.SPACE * 3}std::cout << {function_name} << \" not found.\" << std::endl;"
                        f"\n{self.SPACE * 2}}}"
                        f"\n{self.SPACE * 2}//将收到的状态信息设置到数据库"
                        f"\n{self.SPACE * 2}Database& db = Database::getInstance();"
                        f"\n"
                        f"\n{self.SPACE * 2}/*读数据库中的该状态对out赋值*/"
                        f"\n{self.SPACE * 2}std::any retrievedValue = db.dbGetNodeValue({function_name}_hashValue);"
                        f"\n"
                        f"\n{self.SPACE * 2}if (retrievedValue.has_value()) {{"
                        f"\n{self.SPACE * 3}if (auto* intValue = std::any_cast<int>(&retrievedValue)) {{"
                        f"\n{self.SPACE * 4}std::cout << \"Retrieved node value: \" << *intValue << std::endl;"
                        f"\n{self.SPACE * 3}}}else {{"
                        f"\n{self.SPACE * 4}std::cout << \"Retrieved value is not of type int.\" << std::endl;"
                        f"\n{self.SPACE * 3}}}"
                        f"\n{self.SPACE * 2}}}else {{"
                        f"\n{self.SPACE * 3}std::cout << \"No value retrieved from the node.\" << std::endl;"
                        f"\n{self.SPACE * 2}}}"
                        f"\n{self.SPACE * 2}return {module}::ReturnCode::OK;"
                        f"\n{self.SPACE}}}")

        content += "\n};"
        content += self.function_implemention(module, interface_name)

        return content

    def function_implemention(self, module, interface_name):
        content = ("\nint server(){"
                   f"\n{self.SPACE}printf(\"enter server_main success\\n\");"
                   f"\n{self.SPACE}dds::domain::DomainParticipant participant(10);"
                   f"\n{self.SPACE}dds::rpc::Server server(dds::rpc::ServerParam(4));"
                   f"\n"
                   f"\n{self.SPACE}dds::rpc::ServiceParams param(participant);"
                   f"\n{self.SPACE}param.serviceName(\"YOCTO_Service\");"
                   f"\n"
                   f"\n{self.SPACE}std::shared_ptr<RPCTest_impl> impl = std::make_shared<RPCTest_impl>();"
                   f"\n{self.SPACE}{module}::{interface_name}Service service(impl, server, param);"
                   f"\n"
                   f"\n{self.SPACE}server.run();"
                   f"\n{self.SPACE}return 0;"
                   "\n}")

        return content

    def read_idl(self) -> str and list[str]:
        with open(self.rpc_server_input, encoding="utf-8") as rf:
            file_info = rf.read()
        rf.close()

        module = re.findall("module\s*(\w*)\s*{", file_info)
        if not bool(module):
            raise NameError(f"Failed to retrieve module name from {'subfromAs.idl'} file.")

        interface_info = file_info.split("interface")[-1]
        interface_name = interface_info.split("{")[0].strip()
        interface_content = interface_info.split("{")[-1].split("}")[0]

        return module[0], interface_name, [info.replace(";", "") for info in interface_content.split("\n")
                                           if info and "//" not in info and not info.isspace()]

    def interface_info(self) -> str and list[str]:

        functions_info = []

        module, interface_name, interface_content = self.read_idl()

        for acq_state in interface_content:
            di = {}
            di["return_code"], di["function_name"] = [ret for ret in acq_state.split("(")[0].split(" ") if bool(ret)]
            _params_ = [ret.strip() for ret in acq_state.split("(")[1].replace(")", "").replace(";", "").split(",")
                        if
                        bool(ret)]
            di["params"] = [p.strip().split() for p in _params_]
            functions_info.append(di)

        return module, interface_name, functions_info

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")
