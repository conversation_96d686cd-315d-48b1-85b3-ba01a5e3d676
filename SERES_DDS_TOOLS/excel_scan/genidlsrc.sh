#!/bin/bash


set -e

export PATH=../../cicd/output/x86/bin:$PATH

target_topicdir='./output/IDL/topic'
target_servicecdir='./output/IDL/service'

# generate topic
if [ ! -d $target_topicdir ];then
    echo "$target_topicdir  not exist"
fi

cd $target_topicdir
rm -rf gen
mkdir gen

for item in *; do

  # 显示相对路径
  rel_path="${item#$dir/}"
  
  if [ -f $rel_path ];then
    echo "[I ] start generate $rel_path"
    ../../../../../cicd/output/x86/bin/idlc -Wno-implicit-extensibility -Wno-enum-consecutive -o ./gen $rel_path
    echo "[OK] generate $rel_path"
  fi

done

cd -

#generate service
if [ ! -d $target_servicecdir ];then
    echo "$target_servicecdir  not exist"
fi

cp ./rpcCommon.idl $target_servicecdir
cd $target_servicecdir
rm -rf gen
mkdir gen

for item in *; do

  # 显示相对路径
  rel_path="${item#$dir/}"
  
  if [ -f $rel_path ];then
    echo "[I ] start generate $rel_path"

    if [ "$item" = "HPCC_Service_eSrv.idl" ];then
      ../../../../../cicd/output/x86/bin/idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r client -o ./gen $rel_path
    else
      ../../../../../cicd/output/x86/bin/idlc -Wno-implicit-extensibility -Wno-enum-consecutive -o ./gen $rel_path
    fi

    echo "[OK] generate $rel_path"
  fi

done

echo "[I] success generate"