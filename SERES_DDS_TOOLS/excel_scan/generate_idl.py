from excel_parser import ExcelParser
from service import *
import platform
import os
from datetime import datetime

rpcservice=['HPCC_Service_eSrv']

idl_des="/**\n"\
        "* SOA service or topic generate idl\n"\
        "* Auto Generate file, Do not modify manually !!\n"\
        "* Generate date: {}\n".format(datetime.now())


class GenerateIdl:
    _space_: str = "    "

    def __init__(self, parser: ExcelParser):
        self.out_path = None
        self.parser = parser

    def generate(self):
        class_list = [GenerateService, GenerateTopic]
        for cls in class_list:
            cls(self.parser).generate()

    @staticmethod
    def generate_file_path(filename: str) -> str:

        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    def write_file(self, content: str, file_path: str):

        file_content = (f"{idl_des}"
                        f"* Version       : {self.parser.change_data['版本']}\n"
                        f"* Change Log    : {self.parser.change_data['变更内容']}\n"
                        f"* Change Author : {self.parser.change_data['编辑者']}\n"
                        f"* Change Date   : {self.parser.change_data['日期']}\n"
                        f"* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive {file_path}\n"
                        f"* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> {file_path}\n"
                        f"*/\n\n"
                        f"{content}\n")

        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(file_content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


class GenerateService(GenerateIdl):

    def __init__(self, parser: ExcelParser):
        super().__init__(parser)
        self.out_path = os.path.join(parser.output_dir, "IDL", "service")
        self.service = parser.service
        self.generate_service_data = GenerateServiceData(parser)

    def generate(self):
        print("Start generate service idl file.")
        for service_info in self._generate_module_name_interface_name():
            content = "#include \"rpcCommon.idl\"\n\n"
            content += f"module Seres {{\n{self._space_}{service_info[1]}}};"
            file_path = self.generate_file_path(os.path.join(self.out_path, f"{service_info[0]}.idl"))
            self.write_file(content, file_path)

    def _generate_module_name_interface_name(self):

        for service in self.service:
            content = "module "
            modulename = ""
            
            datas = self.generate_service_data.generate_data(self.generate_service_data.generate_types(service.method))
            if not bool(datas):
                print(
                    f"**Service IDL Error: Service Name = {service.service_name}, datas = {datas}")
                continue

            if "_Service" in service.service_name:
                    modulename = service.service_name.replace('_Service', '')
            elif "_Control" in service.service_name:
                modulename = service.service_name.replace('_Control', '')
            else:
                modulename = service.service_name + "_Space"

            if service.service_name in rpcservice:
                funcs = self._generate_funcs(service.service_name)
                if not bool(funcs):
                    print(
                        f"**Service IDL Error: Service Name = {service.service_name}, funcs = {funcs}")
                    continue

                content += (f"{modulename} {{\n"
                            f"{datas}"
                            f"{self._space_ * 2}interface {service.service_name} {{\n"
                            f"{funcs}\n"
                            f"{self._space_ * 2}}};\n"
                            f"{self._space_}}};\n")
            else:
                unions = self._generate_union(service.service_name)
                inparam = self._generate_inparam(service.service_name)

                if not bool(unions) and not bool(inparam):
                    print(
                        f"**Service IDL Error: Service Name = {service.service_name}, unions = {unions}, inparam = {inparam}")
                    continue

                content += (f"{modulename} {{\n"
                            f"{datas}"
                            f"{inparam}"
                            f"{self._space_ * 2}@final\n"
                            f"{self._space_ * 2}union {service.service_name} switch(int32) {{\n"
                            f"{unions}\n"
                            f"{self._space_ * 2}}};\n"
                            f"{self._space_}}};\n")
                
            yield service.service_name, content

    def _generate_funcs(self, service_name: str) -> str:
        content = ""
        for service in self.service:
            if service_name == service.service_name:
                for method in service.method:
                    des = "// " + method.description.replace("\n", " ").replace("\r", " ") if method.description else ""
                    content += f"{self._space_ * 3}{des}\n"
                    content += (f"{self._space_ * 3}"
                                f"{method.get_ret_param_types() if method.get_ret_param_types() else 'void'} "
                                f"{method.method_name}({self._generate_inout_param(method)});\n\n")
                    
        return content.rstrip()

    @staticmethod
    def _generate_inout_param(method: Method) -> str:
        pars = ""
        for in_type, in_par in method.input_param.items():
            pars += f"in {in_type.type_name} {in_par.lower()}, "
        if not pars:
            for out_type, out_par in method.output_param.items():
                pars += f"out {out_type.type_name} {out_par.lower()}, "

        return pars[:-2]


    def _generate_union(self, service_name: str) -> str:
        content = ""

        for service in self.service:
            if service_name == service.service_name:
                for method in service.method:
                    des = "// " + method.description.replace("\n", " ").replace("\r", " ") if method.description else ""
                    methodparamIn = method.method_name + "_In"
                    content += f"{self._space_ * 3}{des}\n"
                    content += f"{self._space_ * 3}case {method.hash}:\n"
                    content += (f"{self._space_ * 4}{methodparamIn} {method.method_name};\n\n")
                    
        return content.rstrip()

    
    def _generate_uniondata(self, service: Service) -> str:

        content = ""

        for method in service.method:
            struct_info = ""

            for indata, name in method.input_param.items():
                paramname = name.replace("-","")
                struct_info += f"{self._space_ * 3}{indata.type_name} {paramname.lower()};\n"

            if struct_info == "":
                struct_info = f"{self._space_ * 3}uint8 _default;\n"
            content += (f"{self._space_ * 2}@nested\n{self._space_ * 2}"
                        f"struct {method.method_name}_In {{\n{struct_info[:-1]}\n{self._space_ * 2}}};\n\n")

        return content
    

    def _generate_inparam(self, service_name: str) -> str:
        content = ""

        for service in self.service:
            if service_name == service.service_name:
                content += self._generate_uniondata(service)
        
        return content

    

class GenerateServiceData(GenerateIdl):

    def __init__(self, parser: ExcelParser):
        super().__init__(parser)
        self.out_path = os.path.join(parser.output_dir, "IDL", "service")
        self.service = parser.service
        self.data = parser.data

    def generate(self):
        print("Start generate service data idl file.")
        for service_data_info in self._generate_module_name():
            content = f"module Seres {{\n{self._space_}{service_data_info[1]}}};"
            file_path = self.generate_file_path(os.path.join(self.out_path, f"{service_data_info[0]}_DATA.idl"))
            self.write_file(content, file_path)

    def _generate_module_name(self):
        for service in self.service:
            content = "module "
            datas = self.generate_data(self.generate_types(service.method))
            if not bool(datas):
                continue
            content += (f"{service.service_name.replace('_Service', '')} {{\n"
                        f"{datas}\n"
                        f"{self._space_}}};\n")

            yield service.service_name, content

    def generate_types(self, method) -> list[str]:
        temp_list = []
        methods_type = []
        result_type = []
        for _method_ in method:
            temp_list.extend(_method_.get_input_param_types())
            temp_list.extend(_method_.get_output_param_types())
            temp_list.append(_method_.get_ret_param_types())

        for _type_ in temp_list:
            if _type_ not in methods_type:
                methods_type.append(_type_)

        sub_list = self.generate_sub_type(methods_type, [])
        for sub in reversed(sub_list):
            if sub in result_type:
                continue
            else:
                result_type.append(sub)
        
        return result_type

    def generate_data(self, data_types: list[str]) -> str:
        content = ""
        for method_type in data_types:
            for data in self.data:
                description = " // " + data.description.replace("\n", " ").replace("\r",
                                                                                   " ") if data.description else ""
                if method_type == data.type_name:
                    if data.category == "Enumeration":
                        enum = self.generate_enum(data.discrete_value_defination)
                        content += (f"{self._space_ * 2}enum {data.type_name} "
                                    f"{{{description}\n{enum}\n{self._space_ * 2}}};\n\n")
                        break
                    elif data.category == "Struct":
                        struct_info = self.generate_struct(data.members)
                        if not bool(struct_info):
                            continue
                        content += (f"{self._space_ * 2}@nested\n{self._space_ * 2}"
                                    f"struct {data.type_name} {{{description}\n{struct_info}\n{self._space_ * 2}}};\n\n")
                        break
                    elif data.category == "Integer" or data.category == "Boolean" or data.category == "Float" or data.category == "String":
                        content += f"{self._space_ * 2}typedef {data.base_type} {data.type_name};{description}\n\n"
                        break
                    elif data.category == "Array":
                        content += f"{self._space_ * 2}typedef sequence<{data.member.type_name}, {data.length if data.length else 255}> {data.type_name};{description}\n\n"
                        break

        return content

    def generate_sub_type(self, types: list[str], lists: list[str]) -> list[str]:

        for method_type in types:
            for data in self.data:
                if method_type == data.type_name:
                    if data.category == "Struct":
                        _types_ = []
                        lists.append(data.type_name)
                        for member in data.members:
                            if member.type_name:
                                _types_.append(member.type_name)
                        self.generate_sub_type(_types_, lists)
                        break
                    elif data.category == "Array":
                        lists.append(data.type_name)
                        self.generate_sub_type([data.member.type_name], lists)
                        break
                    else:
                        lists.append(method_type)
                        break
        return lists

    def generate_enum(self, discrete_value_defination: str) -> str:
        content = ""

        for sub in discrete_value_defination.split('\n'):
            if ':' in sub:
                enumkey = sub.split(':')[1].strip()

                if enumkey.startswith('_'):
                    enumkey = enumkey[enumkey.find('_')+1:]
                if "%" in enumkey:
                    enumkey= enumkey.replace("%","")

                content += f"{self._space_ * 3}@value({sub.split(':')[0].strip()}) {enumkey},\n"

        return content[:-2]


    def generate_struct(self, members) -> str:
        content = ""
        for member in members:
            if not member.type_name:
                continue
            description = " // " + member.member_des.replace("\n", " ").replace("\r", " ") if member.member_des else ""
            content += f"{self._space_ * 3}{member.type_name} {member.name.lower()};{description}\n"
        return content[:-1]


class GenerateTopic(GenerateIdl):

    def __init__(self, parser: ExcelParser):
        super().__init__(parser)
        self.out_path = os.path.join(parser.output_dir, "IDL", "topic")
        self.topic = parser.topic
        self.generate_topic_data = GenerateTopicData(parser)

    def generate(self):
        print("Start generate topic idl file.")
        for topic_info in self._generate_module_name_struct_name():
            content = f"module Seres {{\n{self._space_}{topic_info[1]}}};"
            file_path = self.generate_file_path(os.path.join(self.out_path, f"{topic_info[0]}.idl"))
            self.write_file(content, file_path)

    def _generate_module_name_struct_name(self):
        for topic in self.topic:
            content = "module "
            optional = self._generate_optional(topic.members, self._space_ * 3)
            datas = self.generate_topic_data.generate_data(self.generate_topic_data.generate_types(topic.members))
            if not bool(optional) and not bool(datas):
                print(
                    f"**Topic IDL Error: Topic Name = {topic.name}, optional = {optional}, datas = {datas}")
                continue
            content += (f"{topic.name.replace('_STATUS', '')} {{\n\n"
                        f"{datas}"
                        f"{self._space_ * 2}@final\n"
                        f"{self._space_ * 2}struct {topic.name}{{\n"
                        f"{optional}\n"
                        f"{self._space_ * 2}}};\n"
                        f"{self._space_}}};\n")
            yield topic.name, content

    @staticmethod
    def _generate_optional(members, space: str) -> str:
        content = ""
        for member in members:
            des = "// " + member.description.replace("\n", " ").replace("\r", " ") if member.description else ""
            content +=  (f"{space}{des}\n"
                         f"{space}@optional {member.parameter_type.type_name} {member.parameter_Name};\n\n")
        return content[:-1]


class GenerateTopicData(GenerateServiceData):

    def __init__(self, parser: ExcelParser):
        super().__init__(parser)
        self.out_path = os.path.join(parser.output_dir, "IDL", "topic")
        self.topic = parser.topic

    def generate(self):
        print("Start generate topic data idl file.")
        for topic_data_info in self._generate_module_name():
            content = f"module Seres {{\n{self._space_}{topic_data_info[1]}}};"
            file_path = self.generate_file_path(os.path.join(self.out_path, f"{topic_data_info[0]}_DATA.idl"))
            self.write_file(content, file_path)

    def _generate_module_name(self):
        for topic in self.topic:
            content = "module "
            datas = self.generate_data(self.generate_types(topic.members))
            if not bool(datas):
                continue
            content += (f"{topic.name.replace('_STATUS', '')} {{\n"
                        f"{datas}\n"
                        f"{self._space_}}};\n")

            yield topic.name, content


    def generate_types(self, members) -> list[str]:
        members_type = []
        result_type = []

        for member in members:
            if member.parameter_type.type_name not in members_type:
                members_type.append(member.parameter_type.type_name)

        sub_list = self.generate_sub_type(members_type, [])

        for sub in reversed(sub_list):
            if sub in result_type:
                continue
            else:
                result_type.append(sub)

        return result_type
