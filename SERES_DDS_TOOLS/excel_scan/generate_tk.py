from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateTk:
    SPACE: str = "    "
    package_name: str = "package com.seres.dds.server\n\n"
    import_content: str = \
        ("import android.os.Bundle\n"
         "import com.seres.dds.commsvc.rpc.ClientManager.getClientHPCM\n"
         "import com.seres.dds.commsvc.rpc.ClientManager.getClientZcuF\n"
         "import com.seres.dds.commsvc.rpc.ClientManager.getClientZcuFR\n"
         "import com.seres.dds.commsvc.rpc.ClientManager.getClientZcuR\n"
         "import com.seres.dds.sdk.ClientParam\n"
         "import com.seres.dds.sdk.DomainParticipant\n"
         "import com.seres.dds.server.api.BCM_SeatEnhanceFunc\n"
         "import com.seres.dds.server.api.BCM_SeatEnhanceFuncStatus\n"
         "import com.seres.dds.server.api.BCM_SeatLearnCMD\n"
         "import com.seres.dds.server.api.BCM_SeatPosition\n"
         "import com.seres.dds.server.api.BCM_SeatPositonByStep\n"
         "import com.seres.dds.server.api.DoorLock\n"
         "import com.seres.dds.server.api.LightReqType\n"
         "import com.seres.dds.server.api.OnOffCmd\n"
         "import com.seres.dds.server.api.OnOffSts\n"
         "import com.seres.dds.server.api.Win_FL_Operate\n"
         "import com.seres.dds.server.consts.InvokeConsts\n"
         "import com.seres.dds.server.consts.ServiceHash\n"
         "import com.seres.dds.utils.LogUtils\n"
         "import seres.hpcm.HPCM_ControlClient\n"
         "import seres.s2s.internal.IAsyncResultCallback\n\n")

    service_dict: dict = {
        "ZCU_R_Service": "getClientZcuR",
        "ZCU_FR_Service": "getClientZcuFR",
        "ZCU_FL_Service": "getClientZcuF",
        "HPCC_Service_eSrv": "getClientHPCM"
    }

    flag_dict: dict = {
        "ZCU_R_Service": "flag_zcur",
        "ZCU_FR_Service": "flag_zcufr",
        "ZCU_FL_Service": "flag_zcuf ",
        "HPCC_Service_eSrv": "flag_hpcm"
    }

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate tk file.")
        self._out_path = os.path.join(parser.output_dir, "TK")
        self._service = parser._service

    def generate(self) -> int:
        content = ""
        content += self.package_name
        content += self.import_content
        content += self.class_content()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "ipcserver.tk")))
        return 0

    def class_content(self) -> str:
        content = ("object IpcServerHandler {"
                   f"\n{self.SPACE}private const val TAG = \"ServerImpl\""
                   f"\n"
                   f"\n{self.SPACE}fun handleRequestSync(appId: Int?, serviceHashId: Int, params: Bundle?): Bundle {{"
                   f"\n"
                   f"\n{self.SPACE * 2}return handleRequest(serviceHashId, params)"
                   f"\n"
                   f"\n{self.SPACE}}}"
                   f"\n"
                   f"\n{self.SPACE}fun handleRequestAsync(appId: Int, serviceHashId: Int, params: Bundle?, callback: IAsyncResultCallback?) {{"
                   f"\n"
                   f"\n{self.SPACE * 2}val resultBundle = handleRequest(serviceHashId, params)"
                   f"\n"
                   f"\n{self.SPACE * 2}callback?.onResult(resultBundle)"
                   f"\n{self.SPACE}}}"
                   f"\n"
                   f"{self.handle_request()}"
                   "}")
        return content

    def handle_request(self) -> str:
        content = (f"\n{self.SPACE}private fun handleRequest(serviceHashId: Int, params: Bundle?): Bundle {{"
                   f"\n{self.SPACE * 2}val bundle = Bundle()"
                   f"\n{self.SPACE * 2}val ret = -1"
                   f"\n{self.SPACE * 2}try{{"
                   f"\n{self.SPACE * 3}when (serviceHashId) {{"
                   f"{self.service_content()}"
                   f"\n{self.SPACE * 3}}}"
                   f"\n{self.SPACE * 2}}}catch(e: Exception){{"
                   f"\n{self.SPACE * 3}println(TAG + \"发生异常: ${{e.message}}\")"
                   f"\n{self.SPACE * 2}}}"
                   f"\n{self.SPACE * 2}return bundle"
                   f"\n{self.SPACE}}}\n")
        return content

    def service_content(self) -> str:
        content = ""

        for service in self._service:
            service_name = self.service_dict.get(service.service_name)
            if not bool(service_name):
                raise KeyError(f"{service.service_name} is not in the service name configuration.")

            flag_name = self.flag_dict.get(service.service_name)
            if not bool(flag_name):
                raise KeyError(f"{service.service_name} is not in the flag name configuration.")

            for method in service.method:
                content += (
                    f"\n{self.SPACE * 4}ServiceHash.HASH_{method.method_name.upper()} -> {{{'  // ' + method.description if method.description else ''}"
                    f"\n"
                    f"\n{self.SPACE * 5}LogUtils.i(TAG,\"serviceHashId = $serviceHashId\")"
                    f"\n"
                    f"{self.class_loader(method)}"
                    f"{self.params(method)}"
                    f"{self.return_content(service_name, flag_name, method)}"
                    f"\n{self.SPACE * 4}}}\n\n")
        return content[:-2]

    def class_loader(self, method: Method) -> str:
        content = ""
        if not bool(method.get_input_param_types()):
            return content

        if method.input_param:
            for key, value in method.input_param.items():
                if key.category != "Integer" and key.category != "Enumeration":
                    content += f"\n{self.SPACE * 5}params?.classLoader = {key.type_name}::class.java.classLoader"
                    content += "\n"
        return content

    def params(self, method: Method) -> str:
        content = ""
        if method.input_param:
            for key, value in method.input_param.items():
                if key.category == "Integer" or key.category == "Enumeration":
                    content += self.int_param(value, method.method_name, key.type_name)
                else:
                    content += self.parcelable_param(value, key.type_name, method.method_name, key.type_name)
                content += "\n"
        return content

    def int_param(self, input_param_name: str, method_name: str, input_param_type: str) -> str:
        return f"\n{self.SPACE * 5}val {input_param_name} = params?.getInt(InvokeConsts.{method_name}.{input_param_type})"

    def parcelable_param(self, input_param_name: str, data_type: str, method_name: str, input_param_type: str) -> str:
        return f"\n{self.SPACE * 5}val {input_param_name} = params?.getParcelable<{data_type}>(InvokeConsts.{method_name}.{input_param_type})"

    def return_content(self, service_name: str, flag_name: str, method: Method) -> str:
        content = ""
        param = ""
        if_param = ""
        if bool(method.input_param):
            for key, value in method.input_param.items():
                param += f"{service_name}.{value}, "
                if_param += f"{value} != null && "
            if_param += f"{flag_name} == 1"
        content += f"\n{self.SPACE * 5}println(TAG + \"{flag_name} is : \" + {flag_name})"
        content += f"\n{self.SPACE * 5}if({if_param}){{"
        content += f"\n{self.SPACE * 6}ret = {service_name}().{method.method_name}({param[:-2]})"
        content += f"\n{self.SPACE * 6}LogUtils.e(TAG, \"{method.method_name} ret is : \" + ret)"
        content += f"\n{self.SPACE * 5}}}"
        content += f"\n{self.SPACE * 5}bundle.putInt(InvokeConsts.KEY_RESULT, ret)"
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")