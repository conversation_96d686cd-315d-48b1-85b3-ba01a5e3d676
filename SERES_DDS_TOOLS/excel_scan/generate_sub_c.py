from excel_parser import ExcelParser
import platform
import os


class ZcuName:
    SPACE: str = "    "

    def __init__(self):
        pass

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


class GenerateSubC(ZcuName):

    def __init__(self, parser: ExcelParser) -> None:
        super().__init__()
        print("Start generate c++ Hpcm sub c file.")
        self._out_path = os.path.join(parser.output_dir, "sub_c")
        self._topic = parser.topic
        self.module = "HPCM_status"
        self.struct = "DataTransmission"

    def generate(self) -> int:
        self.write_file(self.content(), self.generate_file_path(os.path.join(self._out_path, "Hpcmsub.c")))
        return 0

    def content(self):
        content = ""
        content += self.include_part()
        content += self.function_implemention()
        return content

    def include_part(self) -> str:
        return (f"#include \"HPCM_status_subscriber.hpp\""
                f"\n#include \"../database/include/Database.h\""
                "\n#include \"./customMap/customMap.hpp\""
                "\n#include \"../database/subdatabase/subdatabase.h\""
                "\n")

    def function_implemention(self) -> str:
        return ("\nint Subscriber() {"
                f"\n"
                f"\n{self.SPACE}try {{"
                f"\n"
                f"\n{self.SPACE * 2}std::cout << \"=== [Subscriber] Create reader.\" << std::endl;"
                f"\n{self.SPACE * 2}dds::domain::DomainParticipant participant(domain::default_id());"
                f"\n{self.SPACE * 2}dds::topic::Topic<{self.module}::{self.struct}> topic(participant, \"HelloWorldData_Msg\");"
                f"\n{self.SPACE * 2}dds::sub::Subscriber subscriber(participant);"
                f"\n{self.SPACE * 2}dds::sub::DataReader<{self.module}::{self.struct}> reader(subscriber, topic);"
                f"\n{self.SPACE * 2}std::cout << \"=== [Subscriber] Wait for message.\" << std::endl;"
                f"\n"
                f"\n{self.SPACE * 2}bool poll = true;"
                f"\n"
                f"\n{self.SPACE * 2}while (poll) {{"
                f"\n"
                f"\n{self.SPACE * 3}dds::sub::LoanedSamples<{self.module}::{self.struct}> samples;"
                f"\n"
                f"\n{self.SPACE * 3}samples = reader.take();"
                f"\n"
                f"\n{self.SPACE * 3}if (samples.length() > 0) {{"
                f"\n"
                f"\n{self.SPACE * 4}dds::sub::LoanedSamples<{self.module}::{self.struct}>::const_iterator sample_iter;"
                f"\n"
                f"\n{self.SPACE * 4}for (sample_iter = samples.begin(); sample_iter < samples.end(); ++sample_iter) {{"
                f"\n"
                f"\n{self.SPACE * 5}const {self.module}::{self.struct}& msg = sample_iter->data();"
                f"\n{self.SPACE * 5}const dds::sub::SampleInfo& info = sample_iter->info();"
                f"\n"
                f"\n{self.SPACE * 5}// 将收到的状态信息设置到数据库"
                f"\n{self.SPACE * 5}Database& db = Database::getInstance();"
                f"\n{self.SPACE * 5}int setResult = -1;"
                f"\n"
                f"\n{self.SPACE * 5}if (info.valid()) {{"
                f"\n{self.SPACE * 6}std::cout << \"=== [Subscriber] Message received: \" << msg << std::endl;"
                f"{self.find_hash_and_set_database()}"
                f"\n{self.SPACE * 6}poll = false;"
                f"\n{self.SPACE * 5}}}"
                f"\n{self.SPACE * 4}}}"
                f"\n{self.SPACE * 3}}} else {{"
                f"\n{self.SPACE * 4}std::this_thread::sleep_for(std::chrono::milliseconds(20));"
                f"\n{self.SPACE * 3}}}"
                f"\n{self.SPACE * 2}}}"
                f"\n{self.SPACE}}} catch (const dds::core::Exception& e) {{"
                f"\n{self.SPACE * 2}std::cerr << \"=== [Subscriber] DDS exception: \" << e.what() << std::endl;"
                f"\n{self.SPACE * 2}return EXIT_FAILURE;"
                f"\n{self.SPACE}}}catch (const std::exception& e) {{"
                f"\n{self.SPACE * 2}std::cerr << \"=== [Subscriber] C++ exception: \" << e.what() << std::endl;"
                f"\n{self.SPACE * 2}return EXIT_FAILURE;"
                f"\n{self.SPACE}}}"
                f"\n"
                f"\n{self.SPACE}std::cout << \"=== [Subscriber] Done.\" << std::endl;"
                f"\n{self.SPACE}return EXIT_SUCCESS;"
                f"\n}}")

    def find_hash_and_set_database(self):
        content = ""
        for topic in self._topic:
            for member in topic.members:
                content += (f"\n\n{self.SPACE * 6}// 查找元素 {member.parameter_Name}"
                            f"\n{self.SPACE * 6}std::string {member.parameter_Name} = \"{member.parameter_Name}\";"
                            f"\n{self.SPACE * 6}int {member.parameter_Name}_hashValue = getValueByKey(map, mapSize, {member.parameter_Name});"
                            f"\n{self.SPACE * 6}if ({member.parameter_Name}_hashValue != -1) {{"
                            f"\n{self.SPACE * 7}std::cout << \"Hash value for \" << {member.parameter_Name} << \" is: \" << {member.parameter_Name}_hashValue << std::endl;"
                            f"\n{self.SPACE * 6}}} else {{"
                            f"\n{self.SPACE * 7}std::cout << {member.parameter_Name} << \" not found.\" << std::endl;"
                            f"\n{self.SPACE * 6}}}"
                            f"\n"
                            f"\n{self.SPACE * 6}// 调用 dbSetNodeValue 接口设置节点值"
                            f"\n{self.SPACE * 6}setResult = db.dbSetNodeValue({member.parameter_Name}_hashValue, msg.{member.parameter_Name}());"
                            f"\n{self.SPACE * 6}if (setResult == 0) {{"
                            f"\n{self.SPACE * 7}std::cout << \"Node value set successfully.\" << std::endl;"
                            f"\n{self.SPACE * 6}}} else if (setResult == 1) {{"
                            f"\n{self.SPACE * 7}std::cout << \"Type mismatch error when setting node value.\" << std::endl;"
                            f"\n{self.SPACE * 6}}} else if (setResult == 2) {{"
                            f"\n{self.SPACE * 7}std::cout << \"Value did not change when setting node value.\" << std::endl;"
                            f"\n{self.SPACE * 6}}}")
        return content
