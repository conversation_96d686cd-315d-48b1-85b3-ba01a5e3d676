from excel_parser import ExcelParser
import platform
import os


class ZcuName:
    SPACE: str = "    "

    def __init__(self):
        self.__zcu_name = None

    @property
    def zcu_name(self):
        return self.__zcu_name

    @zcu_name.setter
    def zcu_name(self, topic_name):
        zcu_name_dict: dict = {
            "ZCU_R_STATUS": "ZcuR",
            "ZCU_FR_STATUS": "ZcuFR",
            "ZCU_FL_STATUS": "ZcuF",
            "HPCC_STATUS": "Hpcm"
        }
        _zcu_name_ = zcu_name_dict.get(topic_name)
        if not bool(_zcu_name_):
            raise KeyError(f"{topic_name} is not in the zcu name configuration.")
        self.__zcu_name = _zcu_name_

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


class GenerateStatusDatabaseH(ZcuName):

    def __init__(self, parser: ExcelParser) -> None:
        super().__init__()
        print("Start generate c++ status database header file.")
        self._out_path = os.path.join(parser.output_dir, "status_database_h")
        self._topic = parser.topic

    def generate(self) -> int:

        for content, zcu_name in self.status_database_content():
            self.write_file(content, self.generate_file_path(os.path.join(self._out_path, f"{zcu_name}Status.h")))
        return 0

    def status_database_content(self):
        for topic in self._topic:
            self.zcu_name = topic.name
            yield (f"{self.include_header()}"
                   f"\n"
                   f"{self.class_content()}"), self.zcu_name

    def include_header(self) -> str:
        return (f"#ifndef {self.zcu_name.upper()}STATUS_H"
                f"\n#define {self.zcu_name.upper()}STATUS_H"
                "\n#include \"../../database/include/S2sBtree.h\"")

    def class_content(self) -> str:
        return (f"\nclass {self.zcu_name}Status {{"
                f"\n\n"
                f"public:"
                f"\n{self.SPACE} void {self.zcu_name.lower()}StatusNodeInit();"
                f"\n\n}};"
                f"\n\n"
                f"#endif")


class GenerateStatusDatabaseCpp(ZcuName):

    def __init__(self, parser: ExcelParser) -> None:
        super().__init__()
        print("Start generate c++ status database source file.")
        self._out_path = os.path.join(parser.output_dir, "status_database_cpp")
        self._topic = parser.topic
        self._data = parser.data

    def generate(self) -> int:

        for content, zcu_name in self.status_database_content():
            self.write_file(content, self.generate_file_path(os.path.join(self._out_path, f"{zcu_name}Status.cpp")))
        return 0

    def status_database_content(self):
        for topic in self._topic:
            self.zcu_name = topic.name
            yield (f"{self.include_header()}"
                   f"\n"
                   f"{self.function_implemention(topic.members)}"), self.zcu_name

    def include_header(self) -> str:
        return (f"#include \"../include/{self.zcu_name}Status.h\""
                f"\n#include \"../../hash/signalHash.h\"")

    def function_implemention(self, members) -> str:
        return (f"\n\nvoid {self.zcu_name}Status::{self.zcu_name.lower()}StatusNodeInit() {{"
                f"\n"
                f"{self.node_create(members)}"
                f"\n}}")

    def node_create(self, members):
        content = ""
        for member in members:
            for data in self._data:
                if (member.parameter_type.type_name == data.type_name
                        and (data.category == "Integer" or data.category == "Boolean")):
                    base_value = 'false' if data.category == "Boolean" else '0'
                    if member.parameter_Name in content:
                        continue
                    content += f"\n{self.SPACE}S2sBTree::getInstance().nodeCreate(getHashValue(SignalHash::{member.parameter_Name}_Hash), typeid({data.base_type}_t), {base_value});"
        return content
