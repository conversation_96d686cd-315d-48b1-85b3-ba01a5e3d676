from excel_parser import ExcelParser
from service import *
import platform
import os


class GenerateSubFromAs:
    SPACE: str = "    "

    def __init__(self, parser: ExcelParser) -> None:
        print("Start generate tk file.")
        self._out_path = os.path.join(parser.output_dir, "subfromAs")
        self._service = parser._service

    def generate(self) -> int:
        content = ""
        content += self.contents()
        self.write_file(content, self.generate_file_path(os.path.join(self._out_path, "subfromAs.idl")))
        return 0

    def contents(self) -> str:
        content = ("#include \"rpcCommon.idl\""
                   "\n\n"
                   "module subfromAs {"
                   "\n"
                   f"\n{self.SPACE}enum ReturnCode {{"
                   f"\n{self.SPACE * 2}OK,"
                   f"\n{self.SPACE * 2}ERROR,"
                   f"\n{self.SPACE * 2}REFUSED"
                   f"\n{self.SPACE}}};"
                   f"\n"
                   f"\n{self.SPACE}typedef uint8 temp;"
                   f"\n"
                   f"\n{self.SPACE}struct temp_struct{{"
                   f"\n{self.SPACE * 2}temp outParam;"
                   f"\n{self.SPACE}}};"
                   f"\n"
                   f"\n{self.SPACE}interface acquisitionState{{"
                   f"\n"
                   f"{self.content()}"
                   f"\n{self.SPACE}}};"
                   "\n};")
        return content

    def content(self) -> str:
        content = ""

        for service in self._service:
            for method in service.method:
                if f"ReturnCode {method.method_name}(out temp_struct temp_param_name)" not in content:
                    content += (f"\n{self.SPACE * 2}ReturnCode {method.method_name}(out temp_struct temp_param_name);")
        return content

    @staticmethod
    def generate_file_path(filename: str) -> str:
        if os.path.isdir(filename):
            file_path = filename
        else:
            file_path = filename.rsplit("\\" if "Windows" in platform.platform() else "/", 1)[0]
        if not os.path.exists(file_path):
            os.makedirs(file_path)

        return os.path.join(filename)

    @staticmethod
    def write_file(content: str, file_path: str) -> None:
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            print(f"-- Written to {file_path}")
        except IOError as e:
            print(f"An error occurred while writing to the file: {e}")


if __name__ == "__main__":
    parser = ExcelParser("test\\【JM3.0项目】整车服务接口定义_控制器对外通信8.xlsx", "output")
    generate_data_reporting = GenerateSubFromAs(parser)
    generate_data_reporting.generate()
