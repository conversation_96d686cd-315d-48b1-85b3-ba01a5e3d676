<?xml version="1.0" encoding="UTF-8"?>
<dds xmlns="http://www.omg.org/dds/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <qos_library name="TestQoSLibrary">

        <!-- 最简单的配置 -->
        <qos_profile name="SimpleProfile">
            <datawriter_qos>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>5</depth>
                </history>
            </datawriter_qos>
        </qos_profile>

        <!-- 最简单的配置 -->
        <qos_profile name="ReaderSimpleProfile">
            <datareader_qos>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>5</depth>
                </history>
            </datareader_qos>
        </qos_profile>

        <!-- 添加可靠性策略 -->
        <qos_profile name="BestEffortProfile">
            <datawriter_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
            </datawriter_qos>
        </qos_profile>

        <qos_profile name="ReaderBestEffort">
            <datareader_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
            </datareader_qos>
        </qos_profile>

    </qos_library>
</dds>