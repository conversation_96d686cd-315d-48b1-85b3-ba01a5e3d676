/*
 * QoS组合示例 - 演示如何通过XML配置和代码组合不同的原子QoS策略
 * 
 * 本示例展示了：
 * 1. 从XML配置文件加载基础QoS配置
 * 2. 在代码中动态修改和组合QoS策略
 * 3. 针对不同应用场景选择合适的QoS组合
 */

#include "HelloWorldData.hpp"
#include "dds/dds.hpp"
#include <chrono>
#include <iostream>
#include <thread>

using namespace org::eclipse::cyclonedds;

void demonstrate_qos_composition()
{
    std::cout << "=== QoS组合示例 ===" << std::endl;

    // 1. 加载XML配置文件
    std::string config_path =
        "/home/<USER>/dds/cyclonedds-cxx/examples/helloworld/qos_config.xml";
    dds::core::QosProvider qos_provider(config_path);

    // 创建域参与者和发布者
    dds::domain::DomainParticipant participant(0); // 使用域ID 0
    dds::pub::Publisher publisher(participant);
    dds::topic::Topic<HelloWorldData::Msg> topic(participant, "HelloWorld");

    std::cout << "\n--- 场景1: 传感器数据传输 (高频率、低延迟) ---"
              << std::endl;
    {
        // 从XML加载基础配置
        auto sensor_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::BestEffortProfile");

        // 在代码中进一步定制QoS
        sensor_qos << dds::core::policy::LatencyBudget(
            dds::core::Duration(0, 500000));                    // 0.5ms
        sensor_qos << dds::core::policy::TransportPriority(50); // 中等优先级

        std::cout << "传感器数据QoS配置:" << std::endl;
        std::cout << "- 可靠性: BEST_EFFORT (快速传输)" << std::endl;
        std::cout << "- 持久性: VOLATILE (不需要持久化)" << std::endl;
        std::cout << "- 历史: KEEP_LAST(1) (只保留最新数据)" << std::endl;
        std::cout << "- 延迟预算: 0.5ms (极低延迟)" << std::endl;
        std::cout << "- 传输优先级: 50 (中等优先级)" << std::endl;

        dds::pub::DataWriter<HelloWorldData::Msg> sensor_writer(publisher,
                                                                topic,
                                                                sensor_qos);
    }

    std::cout << "\n--- 场景2: 控制命令传输 (可靠、实时) ---" << std::endl;
    {
        // 从XML加载可靠传输配置
        auto control_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::ReliableProfile");

        // 针对控制命令的特殊需求进行定制
        control_qos << dds::core::policy::Deadline(
            dds::core::Duration(0, 100000000)); // 100ms截止时间
        control_qos << dds::core::policy::TransportPriority(100); // 最高优先级
        control_qos << dds::core::policy::History::KeepLast(
            5); // 保留最近5个命令

        std::cout << "控制命令QoS配置:" << std::endl;
        std::cout << "- 可靠性: RELIABLE (确保传输)" << std::endl;
        std::cout << "- 持久性: TRANSIENT_LOCAL (新订阅者可获取)" << std::endl;
        std::cout << "- 历史: KEEP_LAST(5) (保留最近5个命令)" << std::endl;
        std::cout << "- 截止时间: 100ms (严格时间要求)" << std::endl;
        std::cout << "- 传输优先级: 100 (最高优先级)" << std::endl;

        dds::pub::DataWriter<HelloWorldData::Msg> control_writer(publisher,
                                                                 topic,
                                                                 control_qos);
    }

    std::cout << "\n--- 场景3: 配置数据传输 (持久化、独占所有权) ---"
              << std::endl;
    {
        // 从XML加载持久化配置
        auto config_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::PersistentProfile");

        // 针对配置数据的特殊需求
        config_qos << dds::core::policy::Lifespan(
            dds::core::Duration(86400, 0)); // 24小时生命周期
        config_qos << dds::core::policy::OwnershipStrength(200); // 高所有权强度

        std::cout << "配置数据QoS配置:" << std::endl;
        std::cout << "- 可靠性: RELIABLE (确保传输)" << std::endl;
        std::cout << "- 持久性: PERSISTENT (数据持久化)" << std::endl;
        std::cout << "- 历史: KEEP_ALL (保留所有历史)" << std::endl;
        std::cout << "- 生命周期: 24小时 (数据有效期)" << std::endl;
        std::cout << "- 所有权: EXCLUSIVE (独占所有权)" << std::endl;
        std::cout << "- 所有权强度: 200 (高优先级发布者)" << std::endl;

        dds::pub::DataWriter<HelloWorldData::Msg> config_writer(publisher,
                                                                topic,
                                                                config_qos);
    }

    std::cout << "\n--- 场景4: 大数据传输 (高吞吐量) ---" << std::endl;
    {
        // 从XML加载高吞吐量配置
        auto bulk_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::HighThroughputProfile");

        // 针对大数据传输的优化
        bulk_qos << dds::core::policy::ResourceLimits(50000,
                                                      1000,
                                                      500); // 更大的资源限制
        bulk_qos << dds::core::policy::History::KeepLast(
            100); // 保留更多历史数据

        std::cout << "大数据传输QoS配置:" << std::endl;
        std::cout << "- 可靠性: BEST_EFFORT (提高吞吐量)" << std::endl;
        std::cout << "- 持久性: VOLATILE (减少开销)" << std::endl;
        std::cout << "- 历史: KEEP_LAST(100) (批量处理)" << std::endl;
        std::cout << "- 资源限制: 50000样本 (大容量)" << std::endl;
        std::cout << "- 传输优先级: 10 (适中优先级)" << std::endl;

        dds::pub::DataWriter<HelloWorldData::Msg> bulk_writer(publisher,
                                                              topic,
                                                              bulk_qos);
    }

    std::cout << "\n--- 场景5: 自定义QoS组合 ---" << std::endl;
    {
        // 从默认QoS开始，完全自定义组合
        auto custom_qos = publisher.default_datawriter_qos();

        // 组合多个原子QoS策略
        custom_qos
            << dds::core::policy::Reliability::Reliable(
                   dds::core::Duration(2, 0)) // 可靠传输，2秒超时
            << dds::core::policy::Durability::TransientLocal() // 瞬态本地持久性
            << dds::core::policy::History::KeepLast(20) // 保留最近20个样本
            << dds::core::policy::Deadline(
                   dds::core::Duration(1, 0)) // 1秒截止时间
            << dds::core::policy::LatencyBudget(
                   dds::core::Duration(0, 10000000))    // 10ms延迟预算
            << dds::core::policy::TransportPriority(75) // 高优先级
            << dds::core::policy::Lifespan(
                   dds::core::Duration(300, 0)) // 5分钟生命周期
            << dds::core::policy::ResourceLimits(2000, 50, 40); // 资源限制

        std::cout << "自定义QoS组合:" << std::endl;
        std::cout << "- 可靠性: RELIABLE (2秒超时)" << std::endl;
        std::cout << "- 持久性: TRANSIENT_LOCAL" << std::endl;
        std::cout << "- 历史: KEEP_LAST(20)" << std::endl;
        std::cout << "- 截止时间: 1秒" << std::endl;
        std::cout << "- 延迟预算: 10ms" << std::endl;
        std::cout << "- 传输优先级: 75" << std::endl;
        std::cout << "- 生命周期: 5分钟" << std::endl;
        std::cout << "- 资源限制: 2000/50/40" << std::endl;

        dds::pub::DataWriter<HelloWorldData::Msg> custom_writer(publisher,
                                                                topic,
                                                                custom_qos);
    }
}

int main()
{
    try
    {
        demonstrate_qos_composition();
        std::cout << "\n=== QoS组合示例完成 ===" << std::endl;
    }
    catch (const dds::core::Exception &e)
    {
        std::cerr << "异常: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }

    return EXIT_SUCCESS;
}
