# DDS QoS组合指南

## 概述

本指南展示如何使用XML配置文件来设置DataWriter的QoS（服务质量），通过组合多个原子QoS策略来实现各种不同的应用需求。

## 原子QoS策略

DDS提供了多个独立的QoS策略，这些策略可以组合使用：

### 1. 可靠性策略 (Reliability)
- **BEST_EFFORT**: 最佳努力传输，不保证数据到达
- **RELIABLE**: 可靠传输，保证数据到达

### 2. 持久性策略 (Durability)
- **VOLATILE**: 易失性，数据不持久化
- **TRANSIENT_LOCAL**: 瞬态本地，在本地缓存中持久化
- **TRANSIENT**: 瞬态，在服务中持久化
- **PERSISTENT**: 持久化，永久存储

### 3. 历史策略 (History)
- **KEEP_LAST**: 保留最近N个样本
- **KEEP_ALL**: 保留所有样本

### 4. 资源限制 (Resource Limits)
- **max_samples**: 最大样本数
- **max_instances**: 最大实例数
- **max_samples_per_instance**: 每个实例的最大样本数

### 5. 时间相关策略
- **Deadline**: 数据发布/接收的截止时间
- **Latency Budget**: 延迟预算
- **Lifespan**: 数据生命周期
- **Time Based Filter**: 基于时间的过滤

### 6. 所有权策略 (Ownership)
- **SHARED**: 共享所有权
- **EXCLUSIVE**: 独占所有权

## XML配置结构

```xml
<dds xmlns="http://www.omg.org/dds/">
    <qos_library name="ApplicationQoSLibrary">
        <qos_profile name="ProfileName">
            <datawriter_qos>
                <!-- 原子QoS策略组合 -->
                <reliability>...</reliability>
                <durability>...</durability>
                <history>...</history>
                <!-- 更多QoS策略 -->
            </datawriter_qos>
            <datareader_qos>
                <!-- DataReader QoS配置 -->
            </datareader_qos>
        </qos_profile>
    </qos_library>
</dds>
```

## 典型应用场景的QoS组合

### 1. 传感器数据传输
**需求**: 高频率、低延迟、可以容忍数据丢失
```xml
<datawriter_qos>
    <reliability><kind>BEST_EFFORT_RELIABILITY_QOS</kind></reliability>
    <durability><kind>VOLATILE_DURABILITY_QOS</kind></durability>
    <history><kind>KEEP_LAST_HISTORY_QOS</kind><depth>1</depth></history>
    <latency_budget><duration><sec>0</sec><nanosec>1000000</nanosec></duration></latency_budget>
</datawriter_qos>
```

### 2. 控制命令传输
**需求**: 可靠传输、实时性、不能丢失
```xml
<datawriter_qos>
    <reliability><kind>RELIABLE_RELIABILITY_QOS</kind></reliability>
    <durability><kind>TRANSIENT_LOCAL_DURABILITY_QOS</kind></durability>
    <history><kind>KEEP_LAST_HISTORY_QOS</kind><depth>5</depth></history>
    <deadline><period><sec>0</sec><nanosec>100000000</nanosec></period></deadline>
    <transport_priority><value>100</value></transport_priority>
</datawriter_qos>
```

### 3. 配置数据传输
**需求**: 数据持久化、独占所有权、长期有效
```xml
<datawriter_qos>
    <reliability><kind>RELIABLE_RELIABILITY_QOS</kind></reliability>
    <durability><kind>PERSISTENT_DURABILITY_QOS</kind></durability>
    <history><kind>KEEP_ALL_HISTORY_QOS</kind></history>
    <ownership><kind>EXCLUSIVE_OWNERSHIP_QOS</kind></ownership>
    <ownership_strength><value>100</value></ownership_strength>
    <lifespan><duration><sec>3600</sec><nanosec>0</nanosec></duration></lifespan>
</datawriter_qos>
```

### 4. 大数据传输
**需求**: 高吞吐量、大容量缓存
```xml
<datawriter_qos>
    <reliability><kind>BEST_EFFORT_RELIABILITY_QOS</kind></reliability>
    <durability><kind>VOLATILE_DURABILITY_QOS</kind></durability>
    <history><kind>KEEP_LAST_HISTORY_QOS</kind><depth>50</depth></history>
    <resource_limits>
        <max_samples>10000</max_samples>
        <max_instances>1000</max_instances>
        <max_samples_per_instance>100</max_samples_per_instance>
    </resource_limits>
</datawriter_qos>
```

### 5. 实时系统
**需求**: 极低延迟、严格时间要求、高优先级
```xml
<datawriter_qos>
    <reliability><kind>RELIABLE_RELIABILITY_QOS</kind></reliability>
    <durability><kind>VOLATILE_DURABILITY_QOS</kind></durability>
    <history><kind>KEEP_LAST_HISTORY_QOS</kind><depth>5</depth></history>
    <latency_budget><duration><sec>0</sec><nanosec>100000</nanosec></duration></latency_budget>
    <deadline><period><sec>0</sec><nanosec>100000000</nanosec></period></deadline>
    <transport_priority><value>100</value></transport_priority>
</datawriter_qos>
```

## 代码使用示例

### 1. 加载XML配置
```cpp
#include "dds/dds.hpp"

// 创建QoS提供者
dds::core::QosProvider qos_provider("path/to/qos_config.xml");

// 获取特定配置文件的QoS
auto writer_qos = qos_provider.datawriter_qos("ApplicationQoSLibrary::ReliableProfile");
```

### 2. 动态修改QoS
```cpp
// 从XML加载基础配置
auto qos = qos_provider.datawriter_qos("ApplicationQoSLibrary::BestEffortProfile");

// 在代码中进一步定制
qos << dds::core::policy::LatencyBudget(dds::core::Duration(0, 500000))  // 0.5ms
    << dds::core::policy::TransportPriority(50);                         // 中等优先级
```

### 3. 完全自定义QoS组合
```cpp
auto custom_qos = publisher.default_datawriter_qos();

custom_qos << dds::core::policy::Reliability::Reliable(dds::core::Duration(2, 0))
           << dds::core::policy::Durability::TransientLocal()
           << dds::core::policy::History::KeepLast(20)
           << dds::core::policy::Deadline(dds::core::Duration(1, 0));
```

## 最佳实践

1. **模块化设计**: 为不同的应用场景创建独立的QoS配置文件
2. **分层配置**: 使用XML定义基础配置，在代码中进行微调
3. **兼容性考虑**: 确保Publisher和Subscriber的QoS策略兼容
4. **性能优化**: 根据实际需求选择合适的QoS组合，避免过度配置
5. **测试验证**: 在实际环境中测试QoS配置的效果

## QoS兼容性规则

- **Reliability**: Subscriber的可靠性要求不能高于Publisher
- **Durability**: Subscriber的持久性要求不能高于Publisher
- **Deadline**: Publisher的截止时间不能大于Subscriber
- **Ownership**: 必须匹配（都是SHARED或都是EXCLUSIVE）

通过合理组合这些原子QoS策略，可以满足各种复杂的应用需求，实现最优的数据传输性能。
