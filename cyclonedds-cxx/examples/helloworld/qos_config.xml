<?xml version="1.0" encoding="UTF-8"?>
<dds xmlns="http://www.omg.org/dds/"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <!-- QoS 库定义 - 包含多个配置文件 -->
    <qos_library name="ApplicationQoSLibrary">

        <!-- 基础最佳努力配置 - 适用于高频率、低延迟的数据传输 -->
        <qos_profile name="BestEffortProfile">
            <datawriter_qos>
                <!-- 原子QoS 1: 可靠性策略 - 最佳努力传输 -->
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <!-- 原子QoS 2: 持久性策略 - 易失性数据 -->
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <!-- 原子QoS 3: 历史策略 - 保留最新数据 -->
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
                <!-- 原子QoS 4: 延迟预算 - 低延迟要求 -->
                <latency_budget>
                    <duration>
                        <sec>0</sec>
                        <nanosec>1000000</nanosec> <!-- 1ms -->
                    </duration>
                </latency_budget>
            </datawriter_qos>

            <datareader_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
            </datareader_qos>
        </qos_profile>

        <!-- 可靠传输配置 - 适用于重要数据的可靠传输 -->
        <qos_profile name="ReliableProfile">
            <datawriter_qos>
                <!-- 原子QoS 1: 可靠性策略 - 可靠传输 -->
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <!-- 原子QoS 2: 持久性策略 - 瞬态本地持久性 -->
                <durability>
                    <kind>TRANSIENT_LOCAL_DURABILITY_QOS</kind>
                </durability>
                <!-- 原子QoS 3: 历史策略 - 保留多个历史数据 -->
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>100</depth>
                </history>
                <!-- 原子QoS 4: 资源限制 - 控制内存使用 -->
                <resource_limits>
                    <max_samples>1000</max_samples>
                    <max_instances>100</max_instances>
                    <max_samples_per_instance>10</max_samples_per_instance>
                </resource_limits>
                <!-- 原子QoS 5: 截止时间 - 数据时效性要求 -->
                <deadline>
                    <period>
                        <sec>5</sec>
                        <nanosec>0</nanosec>
                    </period>
                </deadline>
            </datawriter_qos>

            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>TRANSIENT_LOCAL_DURABILITY_QOS</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>100</depth>
                </history>
                <deadline>
                    <period>
                        <sec>5</sec>
                        <nanosec>0</nanosec>
                    </period>
                </deadline>
            </datareader_qos>
        </qos_profile>

        <!-- 持久化配置 - 适用于需要数据持久化的场景 -->
        <qos_profile name="PersistentProfile">
            <datawriter_qos>
                <!-- 原子QoS 1: 可靠性策略 - 可靠传输 -->
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <!-- 原子QoS 2: 持久性策略 - 持久化存储 -->
                <durability>
                    <kind>PERSISTENT</kind>
                </durability>
                <!-- 原子QoS 3: 历史策略 - 保留所有历史数据 -->
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <!-- 原子QoS 4: 生命周期 - 数据有效期 -->
                <lifespan>
                    <duration>
                        <sec>3600</sec> <!-- 1小时 -->
                        <nanosec>0</nanosec>
                    </duration>
                </lifespan>
                <!-- 原子QoS 5: 所有权 - 独占所有权 -->
                <ownership>
                    <kind>EXCLUSIVE_OWNERSHIP_QOS</kind>
                </ownership>
                <ownership_strength>
                    <value>100</value>
                </ownership_strength>
            </datawriter_qos>

            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>PERSISTENT</kind>
                </durability>
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <ownership>
                    <kind>EXCLUSIVE_OWNERSHIP_QOS</kind>
                </ownership>
            </datareader_qos>
        </qos_profile>

        <!-- 高吞吐量配置 - 适用于大数据量传输 -->
        <qos_profile name="HighThroughputProfile">
            <datawriter_qos>
                <!-- 原子QoS 1: 可靠性策略 - 最佳努力以提高吞吐量 -->
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <!-- 原子QoS 2: 持久性策略 - 易失性数据 -->
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <!-- 原子QoS 3: 历史策略 - 保留较多数据以提高吞吐量 -->
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>50</depth>
                </history>
                <!-- 原子QoS 4: 资源限制 - 大容量配置 -->
                <resource_limits>
                    <max_samples>10000</max_samples>
                    <max_instances>1000</max_instances>
                    <max_samples_per_instance>100</max_samples_per_instance>
                </resource_limits>
                <!-- 原子QoS 5: 传输优先级 - 高优先级 -->
                <transport_priority>
                    <value>10</value>
                </transport_priority>
            </datawriter_qos>

            <datareader_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>50</depth>
                </history>
                <resource_limits>
                    <max_samples>10000</max_samples>
                    <max_instances>1000</max_instances>
                    <max_samples_per_instance>100</max_samples_per_instance>
                </resource_limits>
            </datareader_qos>
        </qos_profile>

        <!-- 实时配置 - 适用于实时系统 -->
        <qos_profile name="RealTimeProfile">
            <datawriter_qos>
                <!-- 原子QoS 1: 可靠性策略 - 可靠传输 -->
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <!-- 原子QoS 2: 持久性策略 - 易失性数据 -->
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <!-- 原子QoS 3: 历史策略 - 保留最新数据 -->
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>5</depth>
                </history>
                <!-- 原子QoS 4: 延迟预算 - 极低延迟 -->
                <latency_budget>
                    <duration>
                        <sec>0</sec>
                        <nanosec>100000</nanosec> <!-- 0.1ms -->
                    </duration>
                </latency_budget>
                <!-- 原子QoS 5: 截止时间 - 严格时间要求 -->
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </period>
                </deadline>
                <!-- 原子QoS 6: 传输优先级 - 最高优先级 -->
                <transport_priority>
                    <value>100</value>
                </transport_priority>
            </datawriter_qos>

            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>VOLATILE_DURABILITY_QOS</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>5</depth>
                </history>
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </period>
                </deadline>
                <!-- 原子QoS 7: 基于时间的过滤 - 避免重复数据 -->
                <time_based_filter>
                    <minimum_separation>
                        <sec>0</sec>
                        <nanosec>10000000</nanosec> <!-- 10ms -->
                    </minimum_separation>
                </time_based_filter>
            </datareader_qos>
        </qos_profile>

    </qos_library>
</dds>