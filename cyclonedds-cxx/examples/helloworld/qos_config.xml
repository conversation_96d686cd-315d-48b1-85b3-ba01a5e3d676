<?xml version="1.0" encoding="UTF-8"?>
<dds xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles"
     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles fastRTPS_Profiles.xsd">
    
    <!-- QoS 配置文件定义 -->
    <profiles>
        <!-- 可靠传输配置 -->
        <data_writer profile_name="ReliableWriter">
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <durability>
                    <kind>TRANSIENT_LOCAL</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST</kind>
                    <depth>100</depth>
                </history>
                <resource_limits>
                    <max_samples>1000</max_samples>
                    <max_instances>100</max_instances>
                    <max_samples_per_instance>10</max_samples_per_instance>
                </resource_limits>
            </qos>
        </data_writer>
        
        <!-- 高性能配置 -->
        <data_writer profile_name="PerformanceWriter">
            <qos>
                <reliability>
                    <kind>BEST_EFFORT</kind>
                </reliability>
                <durability>
                    <kind>VOLATILE</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST</kind>
                    <depth>1</depth>
                </history>
                <latency_budget>
                    <duration>
                        <sec>0</sec>
                        <nanosec>1000000</nanosec> <!-- 1ms -->
                    </duration>
                </latency_budget>
            </qos>
        </data_writer>
        
        <!-- 数据持久化配置 -->
        <data_writer profile_name="PersistentWriter">
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <durability>
                    <kind>PERSISTENT</kind>
                </durability>
                <history>
                    <kind>KEEP_ALL</kind>
                </history>
                <lifespan>
                    <duration>
                        <sec>3600</sec> <!-- 1小时 -->
                        <nanosec>0</nanosec>
                    </duration>
                </lifespan>
            </qos>
        </data_writer>
        
        <!-- DataReader 配置 -->
        <data_reader profile_name="ReliableReader">
            <qos>
                <reliability>
                    <kind>RELIABLE</kind>
                </reliability>
                <durability>
                    <kind>TRANSIENT_LOCAL</kind>
                </durability>
                <history>
                    <kind>KEEP_LAST</kind>
                    <depth>100</depth>
                </history>
                <deadline>
                    <period>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </period>
                </deadline>
            </qos>
        </data_reader>
    </profiles>
</dds>