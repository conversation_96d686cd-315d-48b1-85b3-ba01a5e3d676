#include "dds/dds.hpp"
#include "HelloWorldData.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <map>
#include <atomic>
#include <iomanip>

class QoSTestFramework {
private:
    dds::domain::DomainParticipant participant;
    dds::topic::Topic<HelloWorldData::Msg> topic;
    dds::pub::Publisher publisher;
    dds::sub::Subscriber subscriber;
    dds::core::QosProvider qos_provider;
    
    std::atomic<int> messages_received{0};
    std::atomic<int> messages_sent{0};
    std::vector<std::string> received_messages;
    std::chrono::steady_clock::time_point test_start_time;
    
public:
    QoSTestFramework() : 
        participant(0),
        topic(participant, "HelloWorldData_Msg"),
        publisher(participant),
        subscriber(participant),
        qos_provider("simple_qos_config.xml") {
        
        std::cout << "=== QoS策略测试框架初始化 ===" << std::endl;
        std::cout << "✓ DomainParticipant创建成功" << std::endl;
        std::cout << "✓ Topic创建成功: " << topic.name() << std::endl;
        std::cout << "✓ Publisher/Subscriber创建成功" << std::endl;
        std::cout << "✓ QoS配置文件加载成功" << std::endl;
    }
    
    // 创建DataWriter
    dds::pub::DataWriter<HelloWorldData::Msg> createWriter(const std::string& qos_profile) {
        try {
            auto writer_qos = qos_provider.datawriter_qos(qos_profile);
            auto writer = dds::pub::DataWriter<HelloWorldData::Msg>(publisher, topic, writer_qos);
            std::cout << "✓ DataWriter创建成功，使用QoS: " << qos_profile << std::endl;
            return writer;
        } catch (const std::exception& e) {
            std::cout << "❌ DataWriter创建失败: " << e.what() << std::endl;
            throw;
        }
    }
    
    // 创建DataReader
    dds::sub::DataReader<HelloWorldData::Msg> createReader(const std::string& qos_profile) {
        try {
            auto reader_qos = qos_provider.datareader_qos(qos_profile);
            auto reader = dds::sub::DataReader<HelloWorldData::Msg>(subscriber, topic, reader_qos);
            std::cout << "✓ DataReader创建成功，使用QoS: " << qos_profile << std::endl;
            return reader;
        } catch (const std::exception& e) {
            std::cout << "❌ DataReader创建失败: " << e.what() << std::endl;
            throw;
        }
    }
    
    // 发送测试消息
    void sendMessages(dds::pub::DataWriter<HelloWorldData::Msg>& writer, 
                     const std::string& test_name, int count = 5) {
        std::cout << "\n--- 发送测试消息 (" << test_name << ") ---" << std::endl;
        
        for (int i = 1; i <= count; ++i) {
            HelloWorldData::Msg msg;
            msg.userID() = i;
            msg.message() = test_name + "_消息_" + std::to_string(i);
            
            try {
                writer.write(msg);
                messages_sent++;
                std::cout << "发送: ID=" << msg.userID() << ", 内容=" << msg.message() << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            } catch (const std::exception& e) {
                std::cout << "❌ 发送失败: " << e.what() << std::endl;
            }
        }
    }
    
    // 接收测试消息
    void receiveMessages(dds::sub::DataReader<HelloWorldData::Msg>& reader, 
                        const std::string& test_name, int timeout_seconds = 3) {
        std::cout << "\n--- 接收测试消息 (" << test_name << ") ---" << std::endl;
        
        auto start_time = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(timeout_seconds);
        
        while (std::chrono::steady_clock::now() - start_time < timeout) {
            auto samples = reader.take();
            
            for (const auto& sample : samples) {
                if (sample.info().valid()) {
                    messages_received++;
                    auto msg = sample.data();
                    std::string received_msg = "ID=" + std::to_string(msg.userID()) + 
                                             ", 内容=" + msg.message();
                    received_messages.push_back(received_msg);
                    std::cout << "接收: " << received_msg << std::endl;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }
    
    // 重置计数器
    void resetCounters() {
        messages_received = 0;
        messages_sent = 0;
        received_messages.clear();
        test_start_time = std::chrono::steady_clock::now();
    }
    
    // 打印测试结果
    void printTestResults(const std::string& test_name) {
        auto elapsed = std::chrono::steady_clock::now() - test_start_time;
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
        
        std::cout << "\n=== " << test_name << " 测试结果 ===" << std::endl;
        std::cout << "发送消息数: " << messages_sent << std::endl;
        std::cout << "接收消息数: " << messages_received << std::endl;
        std::cout << "消息传递率: " << std::fixed << std::setprecision(1) 
                  << (messages_sent > 0 ? (double)messages_received / messages_sent * 100 : 0) 
                  << "%" << std::endl;
        std::cout << "测试耗时: " << elapsed_ms << "ms" << std::endl;
        
        if (messages_received == messages_sent && messages_sent > 0) {
            std::cout << "✅ 测试通过：所有消息成功传递" << std::endl;
        } else if (messages_received > 0) {
            std::cout << "⚠️  部分成功：部分消息传递成功" << std::endl;
        } else {
            std::cout << "❌ 测试失败：没有消息传递成功" << std::endl;
        }
        std::cout << std::string(50, '-') << std::endl;
    }
};

// QoS兼容性测试
void testQoSCompatibility(QoSTestFramework& framework) {
    std::cout << "\n🔍 === QoS兼容性测试 === 🔍" << std::endl;
    
    struct TestCase {
        std::string writer_qos;
        std::string reader_qos;
        std::string test_name;
        bool should_work;
    };
    
    std::vector<TestCase> test_cases = {
        {"TestQoSLibrary::SimpleProfile", "TestQoSLibrary::ReaderSimpleProfile", 
         "相同策略兼容性", true},
        {"TestQoSLibrary::BestEffortProfile", "TestQoSLibrary::ReaderBestEffort", 
         "BestEffort策略兼容性", true},
        {"TestQoSLibrary::SimpleProfile", "TestQoSLibrary::ReaderBestEffort", 
         "不同策略兼容性", true},
        {"TestQoSLibrary::BestEffortProfile", "TestQoSLibrary::ReaderSimpleProfile", 
         "混合策略兼容性", true}
    };
    
    for (const auto& test_case : test_cases) {
        std::cout << "\n📋 测试用例: " << test_case.test_name << std::endl;
        std::cout << "Writer QoS: " << test_case.writer_qos << std::endl;
        std::cout << "Reader QoS: " << test_case.reader_qos << std::endl;
        
        try {
            framework.resetCounters();
            
            auto writer = framework.createWriter(test_case.writer_qos);
            auto reader = framework.createReader(test_case.reader_qos);
            
            // 等待发现
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            
            // 发送和接收消息
            std::thread sender([&]() {
                framework.sendMessages(writer, test_case.test_name, 3);
            });
            
            std::thread receiver([&]() {
                framework.receiveMessages(reader, test_case.test_name, 2);
            });
            
            sender.join();
            receiver.join();
            
            framework.printTestResults(test_case.test_name);
            
        } catch (const std::exception& e) {
            std::cout << "❌ 测试异常: " << e.what() << std::endl;
        }
    }
}

int main() {
    try {
        QoSTestFramework framework;
        
        std::cout << "\n🚀 开始QoS策略验证测试..." << std::endl;
        
        // 执行兼容性测试
        testQoSCompatibility(framework);
        
        std::cout << "\n🎉 === 所有测试完成 === 🎉" << std::endl;
        std::cout << "测试报告已生成，请查看上述结果。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
