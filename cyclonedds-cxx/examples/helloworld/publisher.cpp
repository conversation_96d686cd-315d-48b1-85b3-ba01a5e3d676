/*
 * Copyright(c) 2006 to 2020 ZettaScale Technology and others
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
 * v. 1.0 which is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
 */
#include <chrono>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <thread>
/* Include the C++ DDS API. */
#include "dds/dds.hpp"

/* Include data type and specific traits to be used with the C++ DDS API. */
#include "HelloWorldData.hpp"
#include "OTA_DucData.hpp"
using namespace org::eclipse::cyclonedds;

int main()
{
    try
    {
        std::cout << "=== [Publisher] Create writer." << std::endl;
        std::string config_path =
            "/home/<USER>/dds/cyclonedds-cxx/examples/helloworld/"
            "qos_config.xml";

        // 创建QoS提供者，指定配置文件和库名
        dds::core::QosProvider qos_provider(config_path);
        /* First, a domain participant is needed.
         * Create one on the default domain. */
        dds::domain::DomainParticipant participant(domain::default_id());

        /* A writer also needs a publisher. */
        dds::pub::Publisher publisher(participant);

        dds::topic::Topic<seres::ota_duc_service::OTA_DucDataUnion> topic2(
            participant,
            "OTA_DUC_Data_Union");

        // 演示不同的QoS配置组合

        // 1. 使用最佳努力配置 - 适用于高频率、低延迟数据
        dds::pub::qos::DataWriterQos best_effort_qos =
            qos_provider.datawriter_qos(
                "ApplicationQoSLibrary::BestEffortProfile");

        // 2. 使用可靠传输配置 - 适用于重要数据
        dds::pub::qos::DataWriterQos reliable_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::ReliableProfile");

        // 3. 使用持久化配置 - 适用于需要数据持久化的场景
        dds::pub::qos::DataWriterQos persistent_qos =
            qos_provider.datawriter_qos(
                "ApplicationQoSLibrary::PersistentProfile");

        // 4. 使用高吞吐量配置 - 适用于大数据量传输
        dds::pub::qos::DataWriterQos high_throughput_qos =
            qos_provider.datawriter_qos(
                "ApplicationQoSLibrary::HighThroughputProfile");

        // 5. 使用实时配置 - 适用于实时系统
        dds::pub::qos::DataWriterQos realtime_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::RealTimeProfile");

        // 根据应用需求选择合适的QoS配置
        // 这里使用可靠传输配置作为示例
        dds::pub::DataWriter<seres::ota_duc_service::OTA_DucDataUnion> writer2(
            publisher,
            topic2,
            reliable_qos);

        std::cout << "=== [Publisher] Waiting for subscriber." << std::endl;
        while (writer2.publication_matched_status().current_count() == 0)
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(20));
        }

        seres::ota_duc_service::InventoryInfo info;
        info.ecuName("TestECU");
        info.backupVersion("");
        seres::ota_duc_service::InventoryResult res;
        res.InventoryLists().push_back(info);

        seres::ota_duc_service::OTA_DucDataUnion msg2;
        msg2.uzipPackagesResult();
        msg2.checkUpdateConditionResult();
        writer2->write(msg2);
        std::cout << "=== [Publisher] Waiting for sample to be accepted."
                  << std::endl;
    }
    catch (const dds::core::Exception &e)
    {
        std::cerr << "=== [Publisher] Exception: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }

    std::cout << "=== [Publisher] Done." << std::endl;

    return EXIT_SUCCESS;
}
