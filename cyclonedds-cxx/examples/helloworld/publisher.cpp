/*
 * Copyright(c) 2006 to 2020 ZettaScale Technology and others
 *
 * This program and the accompanying materials are made available under the
 * terms of the Eclipse Public License v. 2.0 which is available at
 * http://www.eclipse.org/legal/epl-2.0, or the Eclipse Distribution License
 * v. 1.0 which is available at
 * http://www.eclipse.org/org/documents/edl-v10.php.
 *
 * SPDX-License-Identifier: EPL-2.0 OR BSD-3-Clause
 */
#include <chrono>
#include <cstdlib>
#include <fstream>
#include <iostream>
#include <thread>
/* Include the C++ DDS API. */
#include "dds/dds.hpp"

/* Include data type and specific traits to be used with the C++ DDS API. */
#include "HelloWorldData.hpp"
#include "OTA_DucData.hpp"
using namespace org::eclipse::cyclonedds;

int main()
{
    try
    {
        std::cout << "=== [Publisher] Create writer." << std::endl;
        std::string config_path =
            "/home/<USER>/dds/cyclonedds-cxx/examples/helloworld/"
            "qos_config.xml";

        dds::core::QosProvider qos_provider(config_path);
        /* First, a domain participant is needed.
         * Create one on the default domain. */
        dds::domain::DomainParticipant participant(domain::default_id());

        /* A writer also needs a publisher. */
        dds::pub::Publisher publisher(participant);

        dds::topic::Topic<seres::ota_duc_service::OTA_DucDataUnion> topic2(
            participant,
            "OTA_DUC_Data_Union");

        dds::pub::qos::DataWriterQos base_qos = qos_provider.datawriter_qos(
            "ApplicationQoSLibrary::BestEffortProfile");

        base_qos->policy(dds::core::policy::Reliability::Reliable());
        dds::pub::DataWriter<seres::ota_duc_service::OTA_DucDataUnion> writer2(
            publisher,
            topic2,
            base_qos);

        std::cout << "=== [Publisher] Waiting for subscriber." << std::endl;
        while (1)
        {
            seres::ota_duc_service::InventoryInfo info;
            info.ecuName("TestECU");
            info.backupVersion("1.1.10");
            seres::ota_duc_service::InventoryResult res;
            res.InventoryLists().push_back(info);

            seres::ota_duc_service::OTA_DucDataUnion msg;
            msg.inventoryResult(res);
            writer2->write(msg);
            std::cout << "=== [Publisher] Pub one msg." << std::endl;
            sleep(1);
        }
    }
    catch (const dds::core::Exception &e)
    {
        std::cerr << "=== [Publisher] Exception: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }

    std::cout << "=== [Publisher] Done." << std::endl;

    return EXIT_SUCCESS;
}
