/*
 * 简单的QoS配置测试程序
 * 用于验证XML配置文件是否正确加载
 */

#include <iostream>
#include <string>
#include <vector>
#include "dds/dds.hpp"

using namespace org::eclipse::cyclonedds;

int main()
{
    try
    {
        std::cout << "=== 简单QoS配置测试 ===" << std::endl;
        
        // 加载XML配置文件
        std::string config_path = "/home/<USER>/dds/cyclonedds-cxx/examples/helloworld/qos_config.xml";
        std::cout << "加载配置文件: " << config_path << std::endl;
        
        dds::core::QosProvider qos_provider(config_path);
        std::cout << "✓ 配置文件加载成功!" << std::endl;
        
        // 测试各个配置文件
        std::vector<std::string> profiles = {
            "ApplicationQoSLibrary::BestEffortProfile",
            "ApplicationQoSLibrary::ReliableProfile", 
            "ApplicationQoSLibrary::PersistentProfile",
            "ApplicationQoSLibrary::HighThroughputProfile",
            "ApplicationQoSLibrary::RealTimeProfile"
        };
        
        std::cout << "\n测试QoS配置文件加载:" << std::endl;
        
        int success_count = 0;
        for (const auto& profile : profiles) {
            try {
                auto qos = qos_provider.datawriter_qos(profile);
                std::cout << "✓ " << profile << std::endl;
                success_count++;
            } catch (const std::exception& e) {
                std::cerr << "✗ " << profile << " - 错误: " << e.what() << std::endl;
            }
        }
        
        std::cout << "\n=== 测试结果 ===" << std::endl;
        std::cout << "成功加载: " << success_count << "/" << profiles.size() << " 个配置文件" << std::endl;
        
        if (success_count == profiles.size()) {
            std::cout << "🎉 所有QoS配置文件测试通过!" << std::endl;
        } else {
            std::cout << "⚠️  部分配置文件加载失败" << std::endl;
        }
        
    }
    catch (const dds::core::Exception& e)
    {
        std::cerr << "❌ DDS异常: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
    catch (const std::exception& e)
    {
        std::cerr << "❌ 异常: " << e.what() << std::endl;
        return EXIT_FAILURE;
    }
    
    return EXIT_SUCCESS;
}
