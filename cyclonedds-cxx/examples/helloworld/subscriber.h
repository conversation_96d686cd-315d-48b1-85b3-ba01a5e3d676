#pragma once

#include "dds/dds.hpp"
#include <atomic>
#include <condition_variable>
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <queue>
#include <string>
#include <thread>
#include <unordered_map>

namespace seres
{
namespace fotamaster
{

template <typename T>
class TopicSubscriber
{
public:
    using Callback = std::function<void(const T &)>;

    TopicSubscriber(dds::domain::DomainParticipant &participant,
                    const std::string &topicName,
                    const Callback &callback,
                    std::shared_ptr<dds::sub::Subscriber> subscriber = nullptr,
                    dds::sub::qos::DataReaderQos reader_qos =
                        dds::sub::qos::DataReaderQos(),
                    const std::string &partition = "")
    {
        try
        {
            // 创建话题
            dds::topic::Topic<T> topic(participant, topicName);

            if (subscriber)
            {
                m_subscriber = subscriber;
            }
            else
            {
                // 创建订阅者并设置分区
                // dds::sub::qos::SubscriberQos sub_qos;
                // if (!partition.empty()) {
                //     sub_qos << dds::core::policy::Partition({partition});
                // }
                m_subscriber = std::make_shared<dds::sub::Subscriber>(
                    participant,
                    dds::sub::qos::SubscriberQos());
            }
            // 创建DataReader

            m_reader = std::make_shared<dds::sub::DataReader<T>>(*m_subscriber,
                                                                 topic,
                                                                 reader_qos);

            // 创建监听器并设置回调
            m_listener = std::make_shared<DataReaderListener>(callback);
            m_reader->listener(m_listener.get(),
                               dds::core::status::StatusMask::data_available());

            printf("Subscribe topic success, Topic_Name: %s",
                   topicName.c_str());
        }
        catch (const dds::core::Exception &e)
        {
            printf("Subscribe topic failed: %s", e.what());
        }
    }

    ~TopicSubscriber()
    {
        if (m_reader)
        {
            m_reader->listener(nullptr, dds::core::status::StatusMask::none());
        }
    }

private:
    class DataReaderListener : public dds::sub::NoOpDataReaderListener<T>
    {
    public:
        explicit DataReaderListener(const Callback &callback)
            : m_callback(callback)
        {
        }

        void on_data_available(dds::sub::DataReader<T> &reader) override
        {
            auto samples = reader.take();
            for (const auto &sample : samples)
            {
                if (sample.info().valid())
                {
                    m_callback(sample.data());
                }
            }
        }

    private:
        Callback m_callback;
    };

    std::shared_ptr<dds::sub::Subscriber> m_subscriber;
    std::shared_ptr<dds::sub::DataReader<T>> m_reader;
    std::shared_ptr<DataReaderListener> m_listener;
};

} // namespace fotamaster
} // namespace seres