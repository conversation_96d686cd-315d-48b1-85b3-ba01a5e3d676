# DDS QoS配置示例

本目录包含了使用XML配置文件设置DataWriter QoS的完整示例，展示如何通过组合多个原子QoS策略来实现各种不同的应用需求。

## 文件说明

### 配置文件
- `qos_config.xml` - 主要的QoS配置文件，包含5种不同的配置文件
- `QoS_Composition_Guide.md` - 详细的QoS组合指南和最佳实践

### 源代码文件
- `publisher.cpp` - 修改后的发布者，展示如何使用XML配置
- `qos_composition_example.cpp` - QoS组合示例程序
- `test_qos_config.cpp` - QoS配置测试程序

### 构建文件
- `Makefile.qos` - 用于编译示例的Makefile

## QoS配置文件概述

`qos_config.xml`包含以下5种预定义的QoS配置：

### 1. BestEffortProfile (最佳努力配置)
**适用场景**: 高频率、低延迟的数据传输（如传感器数据）
- 可靠性: BEST_EFFORT
- 持久性: VOLATILE  
- 历史: KEEP_LAST(1)
- 延迟预算: 1ms

### 2. ReliableProfile (可靠传输配置)
**适用场景**: 重要数据的可靠传输（如控制命令）
- 可靠性: RELIABLE
- 持久性: TRANSIENT_LOCAL
- 历史: KEEP_LAST(100)
- 截止时间: 5秒
- 资源限制: 1000/100/10

### 3. PersistentProfile (持久化配置)
**适用场景**: 需要数据持久化的场景（如配置数据）
- 可靠性: RELIABLE
- 持久性: PERSISTENT
- 历史: KEEP_ALL
- 生命周期: 1小时
- 所有权: EXCLUSIVE (强度100)

### 4. HighThroughputProfile (高吞吐量配置)
**适用场景**: 大数据量传输
- 可靠性: BEST_EFFORT
- 持久性: VOLATILE
- 历史: KEEP_LAST(50)
- 资源限制: 10000/1000/100
- 传输优先级: 10

### 5. RealTimeProfile (实时配置)
**适用场景**: 实时系统
- 可靠性: RELIABLE (100ms超时)
- 持久性: VOLATILE
- 历史: KEEP_LAST(5)
- 延迟预算: 0.1ms
- 截止时间: 100ms
- 传输优先级: 100
- 时间过滤: 10ms

## 使用方法

### 1. 编译示例

```bash
# 设置环境变量
export CYCLONEDX_HOME=/path/to/cyclonedx

# 编译
make -f Makefile.qos all
```

### 2. 测试配置文件

```bash
# 运行配置测试程序
make -f Makefile.qos run-test

# 或直接运行
./test_qos_config
```

### 3. 运行QoS组合示例

```bash
# 运行组合示例
make -f Makefile.qos run

# 或直接运行
./qos_composition_example
```

### 4. 在代码中使用

```cpp
#include "dds/dds.hpp"

// 1. 创建QoS提供者
dds::core::QosProvider qos_provider("qos_config.xml");

// 2. 获取特定配置的QoS
auto reliable_qos = qos_provider.datawriter_qos("ApplicationQoSLibrary::ReliableProfile");

// 3. 创建DataWriter
dds::pub::DataWriter<YourDataType> writer(publisher, topic, reliable_qos);

// 4. 可选：在代码中进一步定制QoS
reliable_qos << dds::core::policy::TransportPriority(50);
```

## 原子QoS策略组合

每个配置文件都是由多个原子QoS策略组合而成：

### 核心策略
- **Reliability**: 可靠性保证
- **Durability**: 数据持久性
- **History**: 历史数据管理

### 时间策略  
- **Deadline**: 数据时效性
- **Latency Budget**: 延迟要求
- **Lifespan**: 数据生命周期

### 资源策略
- **Resource Limits**: 内存和缓存限制
- **Transport Priority**: 传输优先级

### 高级策略
- **Ownership**: 数据所有权
- **Time Based Filter**: 时间过滤

## 最佳实践

1. **选择合适的基础配置**: 根据应用场景选择最接近需求的预定义配置
2. **渐进式定制**: 从XML基础配置开始，在代码中进行微调
3. **兼容性验证**: 确保Publisher和Subscriber的QoS策略兼容
4. **性能测试**: 在实际环境中测试不同QoS组合的性能表现
5. **文档记录**: 记录每种QoS组合的使用场景和预期效果

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 验证XML语法是否正确

2. **QoS不兼容**
   - 检查Publisher和Subscriber的QoS策略是否匹配
   - 参考兼容性规则调整配置

3. **性能不达预期**
   - 分析具体的QoS策略组合
   - 根据实际需求调整参数

### 调试工具

```bash
# 检查XML语法
make -f Makefile.qos check-xml

# 运行配置测试
./test_qos_config
```

## 扩展配置

可以根据具体需求在`qos_config.xml`中添加新的配置文件：

```xml
<qos_profile name="CustomProfile">
    <datawriter_qos>
        <!-- 自定义QoS策略组合 -->
    </datawriter_qos>
</qos_profile>
```

通过这种方式，可以灵活地为不同的应用场景创建专门的QoS配置，实现最优的数据传输性能。
