module seres {
module ota_duc_service {

    /*---------------service and topic name----------------*/
    const int32 DomainParticipant_ID = 1001;

    const string CDC_SERVICE_NAME = "OTA_CDC_Service";
    const string MDC_SERVICE_NAME = "OTA_MDC_Service";
    const string ZCU_SERVICE_NAME = "OTA_ZCU_Service";

    const string CDC_TOPIC_NAME = "OTA_CDC_TOPIC";
    const string MDC_TOPIC_NAME = "OTA_MDC_TOPIC";
    const string ZCU_TOPIC_NAME = "OTA_ZCU_TOPIC";

    enum DUCType{
        CDC,    //座舱域
        MDC,    //智驾域
        ZCU     //其他域
    };

    //待升级部分资产信息列表
    struct SelectedInventoryList{
        sequence<string> inventoryLists;
    };

    // 资产信息
    struct InventoryInfo {
        string partNumber;        // 零部件件号
        string softwareVersion;   // 软件版本号
        string supplierCode;      // 供应商代码
        string ecuName;           // ECU名称
        string serialNumber;      // 序号代码
        string hardwareVersion;   // 硬件版本号
        string ecuBatchNumber;    // ECU批次号
        string bootloaderVersion; // bootloader软件版本号
        string backupVersion;     // 备份区软件版本号
        boolean SeamlessModeSupport;  // 是否支持无感升级
    };
    //资产信息获取结果列表
    struct InventoryResult {
        sequence<InventoryInfo> InventoryLists;// 资产信息列表
    };


    // 下载前检查条件
    struct DownloadRequirement{
        string deviceId;         // 设备ID
        uint64 diskRequirement;  // 磁盘空间要求
    };
    struct DownloadConditionLists{
        sequence<DownloadRequirement> downloadRequirementLists;   // 下载条件列表
    };
    // 下载前检查结果
    enum DownloadConditionResult {
        NOEXCEPTION,        // 无异常
        NetworkERROR,       // 网络异常
        DISK_NOT_ENOUGH,    // 磁盘空间不足
        PARAMS_INVALID      // 参数不合法
    };


    // 升级包下载任务信息
    struct DownloadTaskInfo {
        string taskId;          // 任务ID
        string packageVersion;  // 包版本号
        string packageName;     // 包名称
        string packageUrl;      // 包下载地址
        string packageSize;     // 包大小
        string packageMd5;      // 包MD5
    };
    struct DownloadTaskLists {
        sequence<DownloadTaskInfo> taskLists;   // 下载任务信息列表
    };


    // 下载过程控制
    enum DownloadCtrl {
        PAUSE,
        RESUME,
        CANCEL
    };
    // 下载状态
    enum DownloadStatus{
        DOWNLOADING,        // 正在下载
        DOWNLOAD_SUCCESS,   //下载成功
        DOWNLOAD_FAIL,      //下载失败
        DOWNLOAD_PAUSE,     //下载暂停
        PACKAGE_INCOMPLETE  // 下载包不完整
    };
    // 进度信息
    struct DownloadProgressInfo {
        uint8 progressPercent;  // 0-100
        string packageName;     // 包名称
        uint64 downloadedSize;  // 已下载大小
        uint64 totalSize;       // 总大小
        DownloadStatus status;
    };
    //下载进度
    struct DownloadProgress {
        boolean allFinished;
        sequence<DownloadProgressInfo> progressLists;   //下载进度列表
    };

    //解压结果
    struct UzipPackagesResult{
        boolean successed;  //是否解压成功
        string errorMsg;    //错误信息
    };
    //校验结果
    struct PackagesVerifyResult{
        boolean successed;  //是否校验成功
        string errorMsg;    //错误信息
    };
    

    //检查升级条件
    enum UpdateConditionErrorCode{
        NOERROR,
        ERROR1,//原因待定
        ERROR2
    };
    //检查升级条件结果
    struct CheckUpdateConditionResult{
        boolean passed;
        UpdateConditionErrorCode errorCode;
    };

    //升级模式
    enum UpdateMode {
        SeamlessMode,
        FormalMode
    };
    //待升级设备列表
    struct UpdateDeviceList {
        sequence<InventoryInfo> updateDeviceLists;
    };


    // 设备升级状态
    enum DeviceUpdateStatus {
        IDLE,
        UPDATING,
        SUCCESS,
        FAILURE
    };
    //失败原因
    enum UpdateErrorReason {//具体原因待定
        ERROR_REASON_1,
        ERROR_REASON_2,
        ERROR_REASON_3
    };
    // 升级进度信息
    struct DeviceUpdateProgress {
        uint8 progressPercent;      // 进度百分比 0-100 %
        string deviceName;          // 设备名称
        string deviceId;            // 设备ID
        DeviceUpdateStatus status;        // 设备状态
        UpdateErrorReason errorReason;    // 错误原因
    };
    // 升级进度列表
    struct UpdateProgress {
        boolean allFinished;
        sequence<DeviceUpdateProgress> progressLists;   // 升级进度列表
    };


    struct RollbackComponentList {
        sequence<InventoryInfo> rollbackLists;
    };


    enum OTA_TopicData{
        INVENTORY_RESULT,
        DOWNLOAD_PROGRESS,
        UZIP_PACKAGES_RESULT,
        PACKAGES_VERIFY_RESULT,
        CHECK_UPDATE_CONDITION_RESULT,
        UPDATE_PROGRESS
    };
    union OTA_DucDataUnion switch (OTA_TopicData) {
        case INVENTORY_RESULT:
            InventoryResult inventoryResult;
        case DOWNLOAD_PROGRESS:
            DownloadProgress downloadProgress;
        case UZIP_PACKAGES_RESULT:
            UzipPackagesResult uzipPackagesResult;
        case PACKAGES_VERIFY_RESULT:
            UzipPackagesResult packagesVerifyResult;
        case CHECK_UPDATE_CONDITION_RESULT:
            UzipPackagesResult checkUpdateConditionResult;
        case UPDATE_PROGRESS:
            UpdateProgress updateProgress;
    };

};
};
