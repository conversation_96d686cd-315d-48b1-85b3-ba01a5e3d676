#include "dds/dds.hpp"
#include "HelloWorldData.hpp"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <atomic>
#include <random>

class QoSImpactTester {
private:
    dds::domain::DomainParticipant participant;
    dds::topic::Topic<HelloWorldData::Msg> topic;
    dds::pub::Publisher publisher;
    dds::sub::Subscriber subscriber;
    dds::core::QosProvider qos_provider;
    
    std::atomic<int> total_sent{0};
    std::atomic<int> total_received{0};
    std::atomic<int> late_arrivals{0};
    std::vector<std::chrono::steady_clock::time_point> send_times;
    std::vector<std::chrono::steady_clock::time_point> receive_times;
    
public:
    QoSImpactTester() : 
        participant(0),
        topic(participant, "HelloWorldData_Msg"),
        publisher(participant),
        subscriber(participant),
        qos_provider("simple_qos_config.xml") {
        
        std::cout << "=== QoS策略影响测试器初始化 ===" << std::endl;
    }
    
    // 测试可靠性策略的影响
    void testReliabilityImpact() {
        std::cout << "\n🔬 === 可靠性策略影响测试 === 🔬" << std::endl;
        
        // 测试BEST_EFFORT
        std::cout << "\n📡 测试BEST_EFFORT可靠性策略..." << std::endl;
        testReliabilityScenario("TestQoSLibrary::BestEffortProfile", 
                               "TestQoSLibrary::ReaderBestEffort", 
                               "BEST_EFFORT", true);
        
        // 对比测试：模拟网络不稳定情况
        std::cout << "\n📡 测试网络不稳定情况下的BEST_EFFORT..." << std::endl;
        testReliabilityWithDelay("TestQoSLibrary::BestEffortProfile", 
                                "TestQoSLibrary::ReaderBestEffort", 
                                "BEST_EFFORT_延迟");
    }
    
    // 测试历史策略的影响
    void testHistoryImpact() {
        std::cout << "\n📚 === 历史策略影响测试 === 📚" << std::endl;
        
        // 测试不同深度的历史保留
        testHistoryDepth("TestQoSLibrary::SimpleProfile", 
                        "TestQoSLibrary::ReaderSimpleProfile", 
                        "历史深度_5");
        
        testHistoryDepth("TestQoSLibrary::BestEffortProfile", 
                        "TestQoSLibrary::ReaderBestEffort", 
                        "历史深度_1");
    }
    
    // 测试晚加入读者的行为
    void testLateJoiningReader() {
        std::cout << "\n⏰ === 晚加入读者测试 === ⏰" << std::endl;
        
        try {
            resetCounters();
            
            // 先创建writer并发送消息
            auto writer = createWriter("TestQoSLibrary::BestEffortProfile");
            std::cout << "📤 Writer先发送消息..." << std::endl;
            
            for (int i = 1; i <= 3; ++i) {
                HelloWorldData::Msg msg;
                msg.userID() = i;
                msg.message() = "早期消息_" + std::to_string(i);
                writer.write(msg);
                total_sent++;
                std::cout << "发送早期消息: " << msg.message() << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
            // 延迟后创建reader
            std::cout << "⏳ 等待1秒后创建晚加入的Reader..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(1));
            
            auto reader = createReader("TestQoSLibrary::ReaderBestEffort");
            
            // 再发送一些消息
            std::cout << "📤 Reader加入后继续发送消息..." << std::endl;
            for (int i = 4; i <= 6; ++i) {
                HelloWorldData::Msg msg;
                msg.userID() = i;
                msg.message() = "后期消息_" + std::to_string(i);
                writer.write(msg);
                total_sent++;
                std::cout << "发送后期消息: " << msg.message() << std::endl;
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
            
            // 接收消息
            receiveMessagesWithAnalysis(reader, "晚加入读者", 2);
            
            printDetailedResults("晚加入读者测试");
            
        } catch (const std::exception& e) {
            std::cout << "❌ 晚加入读者测试异常: " << e.what() << std::endl;
        }
    }
    
    // 性能基准测试
    void performanceBenchmark() {
        std::cout << "\n⚡ === 性能基准测试 === ⚡" << std::endl;
        
        struct BenchmarkCase {
            std::string writer_qos;
            std::string reader_qos;
            std::string name;
            int message_count;
        };
        
        std::vector<BenchmarkCase> cases = {
            {"TestQoSLibrary::SimpleProfile", "TestQoSLibrary::ReaderSimpleProfile", 
             "基础配置", 10},
            {"TestQoSLibrary::BestEffortProfile", "TestQoSLibrary::ReaderBestEffort", 
             "BestEffort配置", 10}
        };
        
        for (const auto& test_case : cases) {
            std::cout << "\n🏃 基准测试: " << test_case.name << std::endl;
            
            try {
                resetCounters();
                auto start_time = std::chrono::high_resolution_clock::now();
                
                auto writer = createWriter(test_case.writer_qos);
                auto reader = createReader(test_case.reader_qos);
                
                // 等待发现
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
                
                // 并发发送和接收
                std::thread sender([&]() {
                    for (int i = 1; i <= test_case.message_count; ++i) {
                        HelloWorldData::Msg msg;
                        msg.userID() = i;
                        msg.message() = "基准测试_" + std::to_string(i);
                        
                        auto send_time = std::chrono::high_resolution_clock::now();
                        writer.write(msg);
                        send_times.push_back(std::chrono::steady_clock::now());
                        total_sent++;
                        
                        std::this_thread::sleep_for(std::chrono::milliseconds(10));
                    }
                });
                
                std::thread receiver([&]() {
                    receiveMessagesWithTiming(reader, test_case.name, 3);
                });
                
                sender.join();
                receiver.join();
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                    end_time - start_time).count();
                
                printPerformanceResults(test_case.name, duration);
                
            } catch (const std::exception& e) {
                std::cout << "❌ 基准测试异常: " << e.what() << std::endl;
            }
        }
    }
    
private:
    dds::pub::DataWriter<HelloWorldData::Msg> createWriter(const std::string& qos_profile) {
        auto writer_qos = qos_provider.datawriter_qos(qos_profile);
        return dds::pub::DataWriter<HelloWorldData::Msg>(publisher, topic, writer_qos);
    }
    
    dds::sub::DataReader<HelloWorldData::Msg> createReader(const std::string& qos_profile) {
        auto reader_qos = qos_provider.datareader_qos(qos_profile);
        return dds::sub::DataReader<HelloWorldData::Msg>(subscriber, topic, reader_qos);
    }
    
    void testReliabilityScenario(const std::string& writer_qos, const std::string& reader_qos, 
                               const std::string& test_name, bool expect_all_messages) {
        try {
            resetCounters();
            
            auto writer = createWriter(writer_qos);
            auto reader = createReader(reader_qos);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
            
            // 快速发送多条消息
            for (int i = 1; i <= 5; ++i) {
                HelloWorldData::Msg msg;
                msg.userID() = i;
                msg.message() = test_name + "_快速消息_" + std::to_string(i);
                writer.write(msg);
                total_sent++;
                std::this_thread::sleep_for(std::chrono::milliseconds(20)); // 快速发送
            }
            
            receiveMessagesWithAnalysis(reader, test_name, 2);
            printDetailedResults(test_name);
            
        } catch (const std::exception& e) {
            std::cout << "❌ 可靠性测试异常: " << e.what() << std::endl;
        }
    }
    
    void testReliabilityWithDelay(const std::string& writer_qos, const std::string& reader_qos, 
                                const std::string& test_name) {
        try {
            resetCounters();
            
            auto writer = createWriter(writer_qos);
            auto reader = createReader(reader_qos);
            
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
            
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> delay_dist(10, 100);
            
            // 模拟网络延迟的不规律发送
            for (int i = 1; i <= 5; ++i) {
                HelloWorldData::Msg msg;
                msg.userID() = i;
                msg.message() = test_name + "_延迟消息_" + std::to_string(i);
                writer.write(msg);
                total_sent++;
                
                int delay = delay_dist(gen);
                std::this_thread::sleep_for(std::chrono::milliseconds(delay));
            }
            
            receiveMessagesWithAnalysis(reader, test_name, 3);
            printDetailedResults(test_name);
            
        } catch (const std::exception& e) {
            std::cout << "❌ 延迟测试异常: " << e.what() << std::endl;
        }
    }
    
    void testHistoryDepth(const std::string& writer_qos, const std::string& reader_qos, 
                         const std::string& test_name) {
        try {
            resetCounters();
            
            auto writer = createWriter(writer_qos);
            
            // 先发送消息，再创建reader测试历史保留
            std::cout << "📤 先发送历史消息..." << std::endl;
            for (int i = 1; i <= 8; ++i) {
                HelloWorldData::Msg msg;
                msg.userID() = i;
                msg.message() = test_name + "_历史消息_" + std::to_string(i);
                writer.write(msg);
                total_sent++;
                std::this_thread::sleep_for(std::chrono::milliseconds(50));
            }
            
            std::cout << "📥 创建Reader并检查历史消息..." << std::endl;
            auto reader = createReader(reader_qos);
            
            receiveMessagesWithAnalysis(reader, test_name, 2);
            printDetailedResults(test_name + " (历史深度测试)");
            
        } catch (const std::exception& e) {
            std::cout << "❌ 历史深度测试异常: " << e.what() << std::endl;
        }
    }
    
    void receiveMessagesWithAnalysis(dds::sub::DataReader<HelloWorldData::Msg>& reader, 
                                   const std::string& test_name, int timeout_seconds) {
        auto start_time = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(timeout_seconds);
        
        while (std::chrono::steady_clock::now() - start_time < timeout) {
            auto samples = reader.take();
            
            for (const auto& sample : samples) {
                if (sample.info().valid()) {
                    total_received++;
                    receive_times.push_back(std::chrono::steady_clock::now());
                    auto msg = sample.data();
                    std::cout << "📨 接收: ID=" << msg.userID() << ", " << msg.message() << std::endl;
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(50));
        }
    }
    
    void receiveMessagesWithTiming(dds::sub::DataReader<HelloWorldData::Msg>& reader, 
                                 const std::string& test_name, int timeout_seconds) {
        auto start_time = std::chrono::steady_clock::now();
        auto timeout = std::chrono::seconds(timeout_seconds);
        
        while (std::chrono::steady_clock::now() - start_time < timeout) {
            auto samples = reader.take();
            
            for (const auto& sample : samples) {
                if (sample.info().valid()) {
                    total_received++;
                    receive_times.push_back(std::chrono::steady_clock::now());
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
    
    void resetCounters() {
        total_sent = 0;
        total_received = 0;
        late_arrivals = 0;
        send_times.clear();
        receive_times.clear();
    }
    
    void printDetailedResults(const std::string& test_name) {
        std::cout << "\n📊 === " << test_name << " 详细结果 === 📊" << std::endl;
        std::cout << "发送消息总数: " << total_sent << std::endl;
        std::cout << "接收消息总数: " << total_received << std::endl;
        std::cout << "消息丢失数: " << (total_sent - total_received) << std::endl;
        std::cout << "传递成功率: " << std::fixed << std::setprecision(1) 
                  << (total_sent > 0 ? (double)total_received / total_sent * 100 : 0) 
                  << "%" << std::endl;
        
        if (total_received == total_sent && total_sent > 0) {
            std::cout << "✅ 完美传递：所有消息都成功接收" << std::endl;
        } else if (total_received > 0) {
            std::cout << "⚠️  部分丢失：" << (total_sent - total_received) << " 条消息丢失" << std::endl;
        } else {
            std::cout << "❌ 完全失败：没有消息被接收" << std::endl;
        }
        std::cout << std::string(60, '-') << std::endl;
    }
    
    void printPerformanceResults(const std::string& test_name, long duration_ms) {
        std::cout << "\n⚡ === " << test_name << " 性能结果 === ⚡" << std::endl;
        std::cout << "总耗时: " << duration_ms << "ms" << std::endl;
        std::cout << "平均延迟: " << (total_received > 0 ? duration_ms / total_received : 0) << "ms/消息" << std::endl;
        std::cout << "吞吐量: " << (duration_ms > 0 ? (total_received * 1000.0 / duration_ms) : 0) << " 消息/秒" << std::endl;
        std::cout << std::string(60, '-') << std::endl;
    }
};

int main() {
    try {
        QoSImpactTester tester;
        
        std::cout << "\n🔬 开始QoS策略影响分析测试..." << std::endl;
        
        // 执行各种影响测试
        tester.testReliabilityImpact();
        tester.testHistoryImpact();
        tester.testLateJoiningReader();
        tester.performanceBenchmark();
        
        std::cout << "\n🎯 === 所有QoS影响测试完成 === 🎯" << std::endl;
        std::cout << "通过以上测试可以观察到不同QoS策略对消息传递的具体影响。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
