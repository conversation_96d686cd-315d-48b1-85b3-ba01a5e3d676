cmake_minimum_required(VERSION 3.16)

if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "x86" CACHE STRING "Choose the build type: x86, aarch64")
endif()
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "x86" "aarch64")

if(CMAKE_BUILD_TYPE STREQUAL "x86")
    set(CMAKE_C_COMPILER "gcc")
    set(CMAKE_CXX_COMPILER "g++")
elseif(CMAKE_BUILD_TYPE STREQUAL "aarch64")
    set(LINARO_GCC_ROOT /usr/bin)
    set(CMAKE_C_COMPILER "${LINARO_GCC_ROOT}/aarch64-linux-gnu-gcc")
    set(CMAKE_CXX_COMPILER "${LINARO_GCC_ROOT}/aarch64-linux-gnu-g++")
else()
    message(FATAL_ERROR "Unknown build type: ${CMAKE_BUILD_TYPE}")
endif()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS "-std=c++17 ${CMAKE_CXX_FLAGS}")

project(Domain_Update_Controller LANGUAGES C CXX)

if(NOT TARGET CycloneDDS-CXX::ddscxx)
    find_package(CycloneDDS-CXX REQUIRED)
endif()

# global include
include_directories(.
    ddscom/
    common/
    domainupdate/
    duclog/
    inventory/
    package/
)

# set source codes in .cpp
file(GLOB_RECURSE duc_src_cpp
    "ActivityManager.cpp"
    "TaskController.cpp"
    "Activity.cpp"
    "ServiceManager.cpp"
    "DUCMain.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/ddscom/DucDdsService.cpp"
)

message(STATUS "RPC_PREFIX_PATH=${RPC_PREFIX_PATH}")
message(STATUS "DOIP_PREFIX_PATH=${DOIP_PREFIX_PATH}")
# cicd dependency
set(TARGET_INCLUDE_DIR
    ${RPC_PREFIX_PATH}/include/rpcddscxx
    ${CMAKE_CURRENT_SOURCE_DIR}/ddscom/idl/target/include
    # ${DOIP_PREFIX_PATH}/include
)
set(TARGET_LINK_DIR
    ${RPC_PREFIX_PATH}/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/ddscom/idl/target/lib
    # ${DOIP_PREFIX_PATH}/lib
)

# main
set(MAIN_TARGET domain_update)
add_executable(${MAIN_TARGET} ${duc_src_cpp})

add_subdirectory(ipc)
add_subdirectory(inventory)
add_subdirectory(domainupdate)

target_include_directories(${MAIN_TARGET} PRIVATE ${TARGET_INCLUDE_DIR})
target_link_directories(${MAIN_TARGET} PRIVATE ${TARGET_LINK_DIR})
target_link_libraries(${MAIN_TARGET} PRIVATE 
    CycloneDDS-CXX::ddscxx
    otaidle
    rpcddscxx
    pthread
    # doipffi
)

if (BUILDIN_LIBDOIP)
    message(STATUS "build doip")
    find_library(LIB_DOIP
       NAMES doipffi
       PATHS ${DOIP_PREFIX_PATH}/lib
       NO_DEFAULT_PATH
       REQUIRED
   )
   target_compile_definitions(${MAIN_TARGET} PRIVATE _LIBDOIP_INCLUDE)
   target_link_libraries(${MAIN_TARGET} PRIVATE ${LIB_DOIP})
endif ()

set_property(TARGET ${MAIN_TARGET} PROPERTY CXX_STANDARD ${cyclonedds_cpp_std_to_use})

if(BUILD_UNITTEST)
    message(STATUS "build unittest")
    add_subdirectory(unittest)
endif()

install(TARGETS ${MAIN_TARGET}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
