#pragma once

#include <vector>
#include <queue>
#include <atomic>
#include <future>
#include <functional>
#include <condition_variable>
#include <mutex>

namespace ota {
namespace duc {

class TaskController
{
public:
    explicit TaskController(unsigned short num = 1);
    ~TaskController();

    unsigned int maxTaskCapacity();

    template <class F, class... Args>
    auto addTask(F&& f, Args&&... args) -> std::future<decltype(f(args...))> {
        using RetType = decltype(f(args...));

        auto task = std::make_shared<std::packaged_task<RetType()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...));

        std::future<RetType> future = task->get_future();
        {
            std::lock_guard<std::mutex> lock{_mtx};
            _tasks.emplace([task]() { (*task)();} );
        }

        _task_cv.notify_one();
        return future;
    }

private:
    using Task = std::function<void()>;
    using TaskThread = std::thread;

    std::vector<TaskThread> _task_pool;
    std::queue<Task> _tasks;
    std::atomic<bool> _stop;
    std::mutex _mtx;
    std::condition_variable _task_cv;

    void taskLoop();
};

}
}
