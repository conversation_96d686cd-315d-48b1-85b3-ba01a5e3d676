#pragma once

#include <string>
#include <unordered_map>
#include <mutex>

#include "ServiceManager.h"
#include "Activity.h"

namespace ota {
namespace duc {

class ActivityManager : public IService
{
public:
    explicit ActivityManager() = default;
    ~ActivityManager() = default;

    static const std::string name() { return "ota.duc.ActivityManager"; };

    bool hasActivty(std::string &id);
    Activity &getActivty(std::string &id);
    bool setActivty(std::string &id, Activity &activity);

private:
    std::mutex _mtx;
    std::unordered_map<std::string, Activity&> _activity_map;
};

REGISTER_DEFAULT_SERVICE(ActivityManager);

}
}
