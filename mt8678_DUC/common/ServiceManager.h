#pragma once

#include <string>
#include <memory>
#include <unordered_map>
#include <mutex>

namespace ota {
namespace duc {

class IService {
public:
    virtual ~IService() = default;
    static const std::string name() { return "ota.duc.IService"; };

    // disallow copy and assign
    IService(const IService &) = delete;
    IService &operator=(const IService &) = delete;

protected:
    IService() = default;
};

class ServiceManager {
public:
    static ServiceManager &instance() {
        static ServiceManager instance;
        return instance;
    }

    ServiceManager(const ServiceManager &) = delete;
    ServiceManager &operator=(const ServiceManager &) = delete;

    using service_ptr = std::shared_ptr<ota::duc::IService>;
    bool addService(const std::string &name, const service_ptr &service);
    service_ptr getService(const std::string &name);

    template <typename ServiceType>
    bool addService(const std::string &name) {
        static_assert(std::is_base_of<ota::duc::IService, ServiceType>::value,
                "type is not derived from ota::duc::IService");
        auto servic = std::make_shared<ServiceType>();
        return this->addService(name, std::move(servic));
    }

    template <typename ServiceType>
    bool addDefaultService() {
        const std::string default_name =
            std::string{"_default_"} + std::string{typeid(ServiceType).name()};
        return this->addService<ServiceType>(default_name);
    }

    template <typename ServiceType>
    std::shared_ptr<ServiceType> getService(const std::string &name) {
        static_assert(std::is_base_of<ota::duc::IService, ServiceType>::value,
                "type is not derived from ota::duc::IService");
        return std::static_pointer_cast<ServiceType>(this->getService(name));
    }

    template <typename ServiceType>
    std::shared_ptr<ServiceType> getDefaultService() {
        const std::string default_name =
            std::string{"_default_"} + std::string{typeid(ServiceType).name()};
        return this->getService<ServiceType>(default_name);
    }

private:
    ServiceManager() = default;
    ~ServiceManager();

    std::mutex _mtx;
    std::unordered_map<std::string, service_ptr> _service_table;
};

#define __REGISTER_DEFAULT_SERVICE__(Type, UniqueID)                  \
    namespace                                                         \
    {                                                                 \
        struct TemporaryType##UniqueID                                \
        {                                                             \
            TemporaryType##UniqueID()                                 \
            {                                                         \
                using namespace ota::duc;                             \
                ServiceManager::instance().addDefaultService<Type>(); \
            }                                                         \
        };                                                            \
        static TemporaryType##UniqueID default_serivce_##UniqueID;    \
    }

#define REGISTER_DEFAULT_SERVICE_INTER(Type, UniqueID) \
    __REGISTER_DEFAULT_SERVICE__(Type, UniqueID)

#define REGISTER_DEFAULT_SERVICE(Type) \
    REGISTER_DEFAULT_SERVICE_INTER(Type, __COUNTER__)

}
}
