#include "TaskController.h"

namespace ota {
namespace duc {

TaskController::TaskController(unsigned short num)
{
    _stop = false;
    // add limitation to task number
    if (num > maxTaskCapacity()) {
        num = maxTaskCapacity();
    }
    _task_pool.reserve(num);
    for (; num > 0; --num) {
        TaskThread tt = TaskThread(&TaskController::taskLoop, this);
        _task_pool.emplace_back(std::move(tt));
    }
}

TaskController::~TaskController()
{
    _stop = true;
    _task_cv.notify_all();
    for (TaskThread& task : _task_pool) {
        if (task.joinable())
            task.join();
    }
    _task_pool.clear();

    if (!_tasks.empty()) {
        std::queue<Task>().swap(_tasks);
    }
}

unsigned int TaskController::maxTaskCapacity()
{
    return std::thread::hardware_concurrency();
}

void TaskController::taskLoop()
{
    do {
        std::unique_lock<std::mutex> lock{_mtx};
        _task_cv.wait(lock, [this] { return _stop || !_tasks.empty(); });
        if (_stop) return;

        if (!_tasks.empty()) {
            Task task = std::move(_tasks.front());
            _tasks.pop();
            lock.unlock();
            task();
        }
    } while (!_stop);
};

}
}
