#pragma once

#include <string>
#include <memory>

namespace ota {
namespace duc {

class IDucService
{
public:
    IDucService() = default;
    IDucService(const std::string &name) : _name(name) {};
    virtual ~IDucService() = default;
    virtual std::string getName() { return _name; }
    virtual void run() = 0;
protected:
    std::string _name;
};

std::unique_ptr<IDucService> createDucService();

}
}
