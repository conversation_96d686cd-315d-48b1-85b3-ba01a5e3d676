#include <chrono>
#include "Activity.h"
#include "DUCLogger.h"

namespace ota {
namespace duc {

Activity::~Activity()
{
    _stop.store(true);
    if (mThread.joinable())
        mThread.join();
}

bool Activity::start()
{
    if (mThread.joinable()) {
        mThread.join();
    }
    _stop.store(false);
    _count.store(0);
    mThread = std::thread([&] {
        do {
            duclogger::debug("count: " + std::to_string(_count.load()));
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        } while (_count++ < 10 & !_stop.load());
    });
    duclogger::debug("Activity::start: " + std::to_string(_count.load()));
    return true;
}

bool Activity::pause()
{
    _stop.store(true);
    mThread.join();
    duclogger::debug("Activity::pause: " + std::to_string(_count.load()));
    return true;
}

bool Activity::resume()
{
    _stop.store(false);
    mThread = std::thread([&] {
        do {
            duclogger::debug("count: " + std::to_string(_count.load()));
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        } while (_count++ < 10 & !_stop.load());
    });
    duclogger::debug("Activity::resume: " + std::to_string(_count.load()));
    return true;
}

bool Activity::stop()
{
    _stop.store(true);
    mThread.join();
    _count.store(0);
    duclogger::debug("Activity::stop: " + std::to_string(_count.load()));
    return true;
}

}
}
