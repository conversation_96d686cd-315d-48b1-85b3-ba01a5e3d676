#include "ServiceManager.h"

namespace ota {
namespace duc {

ServiceManager::~ServiceManager() {
    _service_table.clear();
}

bool ServiceManager::addService(const std::string &name, const std::shared_ptr<ota::duc::IService> &service)
{
    std::lock_guard<std::mutex> lock(_mtx);
    auto ret = _service_table.emplace(name, service);
    return ret.second;
}

std::shared_ptr<ota::duc::IService> ServiceManager::getService(const std::string &name)
{
    std::lock_guard<std::mutex> lock(_mtx);
    return _service_table.at(name);
}

}
}
