#pragma once

#include <atomic>
#include <thread>

namespace ota {
namespace duc {

class Activity {
public:
    explicit Activity() = default;
    ~Activity();

    virtual bool start();
    virtual bool pause();
    virtual bool resume();
    virtual bool stop();

    bool isRun() {
        return mThread.joinable();
    };

protected:
    std::thread mThread;

private:
    std::atomic_int _count{0};
    std::atomic_bool _stop{false};
};

}
}
