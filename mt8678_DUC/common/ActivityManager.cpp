#include "ActivityManager.h"

namespace ota {
namespace duc {

bool ActivityManager::hasActivty(std::string &id) {
    std::lock_guard<std::mutex> lock(_mtx);
    auto found = _activity_map.find(id);
    return found != _activity_map.end();
}

Activity &ActivityManager::getActivty(std::string &id) {
    std::lock_guard<std::mutex> lock(_mtx);
    return _activity_map.at(id);
}

bool ActivityManager::setActivty(std::string &id, Activity &activity) {
    std::lock_guard<std::mutex> lock(_mtx);
    auto retPair = _activity_map.emplace(id, activity);
    return retPair.second;
}

}
}