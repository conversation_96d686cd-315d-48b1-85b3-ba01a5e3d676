cmake_minimum_required(VERSION 3.16)
project(DucUnitTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS "-std=c++17 ${CMAKE_CXX_FLAGS}")

### UNIT TEST
set(UNIT_TEST unit_test)

set(UNIT_TEST_SRC
    TestMain.cpp
)

add_executable(${UNIT_TEST} ${UNIT_TEST_SRC})

target_include_directories(${UNIT_TEST} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}/../ddscom/idl/target/include
    ${RPC_PREFIX_PATH}/include/rpcddscxx
)

target_link_directories(${UNIT_TEST} PRIVATE 
    ${RPC_PREFIX_PATH}/lib
    ${CMAKE_CURRENT_SOURCE_DIR}/../ddscom/idl/target/lib
)
target_link_libraries(${UNIT_TEST} PRIVATE otaidle CycloneDDS-CXX::ddscxx  rpcddscxx)

