#include <iostream>
#include <csignal>
#include <thread>
#include <chrono>
#include "dds/dds.hpp"
#include "dds/dds.h"
#include "OTA_DucInterface_gen_client.hpp"
#include "OTA_DucData.hpp"

int main(int argc, char *argv[])
{
    std::cout << "DUC Unit Test" << std::endl;
    
    dds::domain::DomainParticipant participant(seres::ota_duc_service::DomainParticipant_ID);
    dds::rpc::ClientParams param(participant);
    param.serviceName(seres::ota_duc_service::ZCU_SERVICE_NAME);
    dds::pub::qos::DataWriterQos dataWriterQos;
    dataWriterQos.policy(dds::core::policy::Liveliness(dds::core::policy::LivelinessKind::Type::MANUAL_BY_PARTICIPANT,dds::core::Duration::from_secs(2)));
    dds::sub::qos::DataReaderQos dataReaderQos;
    dataReaderQos.policy(dds::core::policy::Liveliness(dds::core::policy::LivelinessKind::Type::MANUAL_BY_PARTICIPANT,dds::core::Duration::from_secs(2)));
    dataReaderQos.policy(dds::core::policy::Reliability(dds::core::policy::ReliabilityKind::Type::RELIABLE,dds::core::Duration::from_secs(5)));
    param.dataReaderQos(dataReaderQos);
    param.dataWriterQos(dataWriterQos);

    seres::ota_duc_service::DucServiceInterfaceClient client(param);
    client.wait_for_service();

    std::vector<std::string> deviceList;
    deviceList.push_back("test_Device1");
    seres::ota_duc_service::UpdateDeviceList udl(deviceList);

    std::cout << "startUpdate" << std::endl;
    client.startUpdate(seres::ota_duc_service::UpdateMode::FormalMode, udl);
    
    return 0;
}
