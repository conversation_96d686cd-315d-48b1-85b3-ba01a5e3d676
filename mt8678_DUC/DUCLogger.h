#pragma once

#include <iostream>
#include <string>

namespace duclogger {

namespace level {
enum LogLevel {
    debug = 1,
    info = 2,
    warn = 3,
    error = 4,
    off = 5
};
}

class ILogger {
public:
    virtual ~ILogger() = default;
    virtual void log(level::LogLevel lvl, const std::string& msg) = 0;
};

class Logger : public ILogger {
public:
    static Logger &instance() {
        static Logger instance;
        return instance;
    }

    Logger(const Logger &) = delete;
    Logger &operator=(const Logger &) = delete;

    void log(level::LogLevel lvl, const std::string& msg) override {
        switch (lvl) {
            case level::debug:
                std::cout << "[D] " << msg << std::endl;
                break;
            case level::info:
                std::cout << "[I] " << msg << std::endl;
                break;
            case level::warn:
                std::cerr << "[W] " << msg << std::endl;
                break;
            case level::error:
                std::cerr << "[E] " << msg << std::endl;
                break;
            default:
                // log nothing;
                break;
        }
    }

private:
    Logger() = default;
    ~Logger() = default;
};

inline void debug(const std::string &msg)
{
    Logger::instance().log(level::debug, msg);
}

inline void info(const std::string &msg)
{
    Logger::instance().log(level::info, msg);
}

inline void warn(const std::string &msg)
{
    Logger::instance().log(level::warn, msg);
}

inline void error(const std::string &msg)
{
    Logger::instance().log(level::error, msg);
}

}
