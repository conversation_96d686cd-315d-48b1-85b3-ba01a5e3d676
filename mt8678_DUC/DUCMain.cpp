#include <csignal>
#include <thread>
#include <chrono>
#include <memory>
#include "DUCLogger.h"
#include "ServiceManager.h"
#include "DucService.h"

using namespace ota::duc;

volatile sig_atomic_t stop = 0;
void signalHandler(int signum)
{
    switch (signum) {
        case SIGTERM:
        case SIGKILL:
        case SIGINT:
            stop = 1;
            break;
    }
}

int main()
{
    duclogger::info("Domain Update Control Launched");

    std::unique_ptr<IDucService> ducService = createDucService();
    duclogger::info("run DUC service: " + ducService->getName());
    ducService->run();

    duclogger::info("Domain Update Control Exit");
    return 0;
}
