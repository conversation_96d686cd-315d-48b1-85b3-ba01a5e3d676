#!/bin/bash

set -e

shell_path="$(realpath "$(dirname "${BASH_SOURCE[0]}")")"
duc_idl_home=$shell_path/ddscom/idl
cicd_top_path=$shell_path/../../SERES_DDS/cicd
dds_output_path=$cicd_top_path/output
doip_output_path=$cicd_top_path/output_doip

idlc_bin=$dds_output_path/x86/bin

jobs=1
yocto_flag=false
help_flag=false
clean_flag=false
x86_flag=false
install_prefix=$shell_path/output
build_doip=""
build_unittest=""

# 解析命令行参数
options=$(getopt -o j:xyhcdto: -l jobs:,x86,yocto,help,clean,doip,test,output: -- "$@")
if [ $? != 0 ]; then
    echo_build "Error: Invalid option"
    show_help
    exit 1
fi

eval set -- "$options"

while true; do
    case "$1" in
        -j | --jobs)
            jobs=$2
            shift 2
            ;;
        -y | --yocto)
            yocto_flag=true
            shift
            ;;
        -x | --x86)
            x86_flag=true
            shift
            ;;
        -h | --help)
            help_flag=true
            shift
            ;;
        -o | --output)
            install_prefix=$2
            shift 2
            ;;
        -c | --clean)
            clean_flag=true
            shift
            ;;
        -d | --doip)
            build_doip="ON"
            shift
            ;;
        -t | --test)
            build_unittest="ON"
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            help_flag=true
            ;;
    esac
done

# 显示帮助信息函数
function show_helps() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -j, --jobs <jobs>       Number of jobs to run in parallel (default: 1)"
    echo "  -y, --yocto             Enable Yocto build mode"
    echo "  -h, --help              Display this help message"
    echo "  -x, --x86               Enable x86 build mode"
    echo "  -c, --clean             Clean project"
    echo "  -d, --doip              Build doip"
    echo "  -t, --test              Build unittest"
    echo "  -o, --output            Install path"
    echo "  -h, --help              Show this help message and exit."
}

function echo_build() {
    echo "[BUILD] $1"
}

function show_parameters() {
    echo -e "\e[32mBuilder parameters:\e[0m"
    echo "-- Number of jobs     : [$jobs]"
    echo "-- Yocto build mode   : [$yocto_flag]"
    echo "-- x86 build mode     : [$x86_flag]"
    if [ "$yocto_flag" = true ]; then
        install_prefix=$install_prefix/yocto
        doip_output_path=$doip_output_path/yocto
        dds_output_path=$dds_output_path/yocto
        echo "-- install path       : $install_prefix"
    elif [ "$x86_flag" = true ]; then
        install_prefix=$install_prefix/x86
        doip_output_path=$doip_output_path/x86
        dds_output_path=$dds_output_path/x86
        echo "-- install path       : $install_prefix"
    fi
}

function build_duc_idle(){
    echo -e "\e[32m-----build idle------\e[0m"
    cd $duc_idl_home
    
    if [[ -d "build" ]];then
        rm -rf "build"
    fi
    if [[ -d "target" ]];then
        rm -rf "target"
    fi

    mkdir -p build target

    cd build
    cmake -DCMAKE_PREFIX_PATH=$dds_output_path \
          -DCMAKE_BUILD_TYPE=$1 \
          -DRPC_TARGET_PATH=$dds_output_path \
          -DIDLC_BIN_PATH=$idlc_bin \
          ..
    make && make install
}

function build_duc(){
    echo -e "\e[32m-----build duc------\e[0m"
    cd $shell_path

    # 删除已有的构建目录
    if [ -d "build" ]; then
        rm -r "build"
    fi

    # 创建新的构建目录
    mkdir -p build && cd build

    # 运行 CMake 并指定构建类型
    cmake -DCMAKE_PREFIX_PATH=$dds_output_path \
          -DRPC_PREFIX_PATH=$dds_output_path \
          -DDOIP_PREFIX_PATH=$doip_output_path \
          -DCMAKE_INSTALL_PREFIX=$install_prefix \
          -DCMAKE_BUILD_TYPE=$1 \
          -DBUILDIN_LIBDOIP=$build_doip \
          -DBUILD_UNITTEST=$build_unittest \
          ..
    # 检查 CMake 配置是否成功
    if [ $? -ne 0 ]; then
        echo_build "Error: CMake configuration failed."
        exit 1
    fi

    make && make install
}

function clean_project() {
    rm -rf $install_prefix
}


# --------逻辑区---------------
if [ "$help_flag" = true ]; then
    show_helps
    exit 0
fi

show_parameters

clean_project

#build ilde
if [ "$yocto_flag" = true ]; then
    echo -e "\e[32mEnabling Yocto build mode\e[0m"
    build_duc_idle "aarch64" 
elif [ "$x86_flag" = true ]; then
    echo -e "\e[32mEnabling X86 build mode\e[0m"
    build_duc_idle "x86"
fi

#build duc
if [ "$yocto_flag" = true ]; then
    echo -e "\e[32mEnabling Yocto build mode\e[0m"
    build_duc "aarch64" 
elif [ "$x86_flag" = true ]; then
    echo -e "\e[32mEnabling X86 build mode\e[0m"
    build_duc "x86"
fi

