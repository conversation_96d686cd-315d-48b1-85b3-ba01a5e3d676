#pragma once

#include <vector>
#include <mutex>
#include "DomainBase.hpp"
#include "EcuDevice.hpp"

class ZcuDomain : public DomainBase
{
public:
    ZcuDomain(/* args */);
    ~ZcuDomain();

    int collectInventory(std::vector<std::string> device_id) override;
    void stopCollect() override;
    int init() override;

private:
    std::vector<EcuDevice> device_list_;
    void* channel_ = nullptr;

    std::mutex collect_mutex_;
    bool stop_collect_;
};


