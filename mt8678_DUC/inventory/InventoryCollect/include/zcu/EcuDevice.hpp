#pragma

#include <string>
#include "InventoryStorage.hpp"

class EcuDevice {
public:
    explicit EcuDevice(void* channel, std::string id, std::string ip, uint16_t ecu_addr);
    ~EcuDevice();

    int connect();
    int disconnect();
    int read(unsigned char* buffer, size_t buffer_len, size_t* bytes_read);
    int write(const unsigned char* data, size_t data_len);

    std::string getDeviceId();

    int getInventory(LocalInventoryInfo& device_info);

private:
    void* channel_;
    std::string devices_id_;
    std::string ip_;
    uint16_t ecu_addr_;
};