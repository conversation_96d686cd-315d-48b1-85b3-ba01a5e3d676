#pragma once

class DomainBase{
public:
    virtual ~DomainBase() = default;

    // virtual int connect() = 0;
    // virtual int disconnect() = 0;
    // virtual int read(unsigned char* buffer, size_t buffer_len, size_t* bytes_read) = 0;
    // virtual int write(const unsigned char* data, size_t data_len) = 0;
    virtual int collectInventory(std::vector<std::string> device_id) = 0;
    virtual void stopCollect() = 0;
    virtual int init() = 0;
};