#pragma once

#include <memory>
#include <vector>
#include <thread>
#include <mutex>
#include <functional>
#include "DomainBase.hpp"
#include "DeviceFactory.hpp"

class InventoryCollect {
public:
    InventoryCollect();
    ~InventoryCollect();

    void initialize(DeviceType device_type);
    void collectInventory(std::vector<std::string> device_id, std::function<void(std::vector<std::string>)> callback);
    void stopCollect();
private:
    bool isCollecting();

private:
    std::unique_ptr<DomainBase> domain_;

    /* 采集线程管理 */
    std::mutex collect_mutex_;
    std::thread thread_;
    bool stop_collect_;
};