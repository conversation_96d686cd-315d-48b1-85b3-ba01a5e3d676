#include "EcuDevice.hpp"
#include "DoipClient.hpp"
#include <iostream>
#include <vector>

EcuDevice::EcuDevice(void* channel, std::string id, std::string ip, uint16_t ecu_addr)
    : channel_(channel),
      devices_id_(id),
      ip_(ip),
      ecu_addr_(ecu_addr) {
}

EcuDevice::~EcuDevice() {

}

int EcuDevice::connect() {
    return DoipClient::syncConnect(channel_, ip_.c_str(), 13400);
}

int EcuDevice::disconnect() {
    return DoipClient::syncDisconnect(channel_, ip_.c_str());
}

int EcuDevice::read(unsigned char* buffer, size_t buffer_len, size_t* bytes_read) {
    return DoipClient::syncRead(channel_, ip_.c_str(), buffer, buffer_len, bytes_read);
}

int EcuDevice::write(const unsigned char* data, size_t data_len) {
    return DoipClient::syncWrite(channel_, ip_.c_str(), ecu_addr_, data, data_len);
}

std::string EcuDevice::getDeviceId(){
    return devices_id_;
}

int EcuDevice::getInventory(LocalInventoryInfo& device_info) {
    if (connect() != 0){
        // log
        return -1;
    }

    std::vector<uint8_t> cmd;
    std::vector<uint8_t> resp;
    size_t bytes_read = 0;

    /* part number */
    cmd = {0x22, 01};
    write(cmd.data(), cmd.size());
    read(resp.data(), resp.size(), &bytes_read);
    resp.resize(bytes_read);
    device_info.part_number = std::string(reinterpret_cast<const char*>(resp.data()), resp.size());

    /* software version */
    cmd = {0x22, 02};
    resp.clear();
    bytes_read = 0;
    write(cmd.data(), cmd.size());
    read(resp.data(), resp.size(), &bytes_read);
    resp.resize(bytes_read);
    device_info.software_version  = std::string(reinterpret_cast<const char*>(resp.data()), resp.size());

    // 其余资产条目获取.....

    disconnect();

    return 0;
}