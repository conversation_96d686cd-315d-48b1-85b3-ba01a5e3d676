#include "ZcuDomain.hpp"
#include "config.hpp"
#include "DoipClient.hpp"

ZcuDomain::ZcuDomain() : stop_collect_(false)
{
}

ZcuDomain::~ZcuDomain()
{
    device_list_.clear();
    if(channel_){
        DoipClient::destroyOTAChannel(channel_);
        channel_ = nullptr;
    }
}

int ZcuDomain::init(){
    void* channel_ = DoipClient::createOTAChannel(0x00e0, 13400);
    for(uint32_t i=0; i<ID_MAX; i++){
        device_list_.emplace_back(EcuDevice(channel_, ecu_device_info[i].id, ecu_device_info[i].ip, ecu_device_info[i].ecu_addr));
    }

    return 0;
}

int ZcuDomain::collectInventory(std::vector<std::string> id_list){
    if(id_list.empty()){
        return -1;
    }

    for(auto id : id_list){
        for(auto device : device_list_){
            {
                std::unique_lock<std::mutex> lock(collect_mutex_);
                if(stop_collect_) return -1;
            }

            if(id == device.getDeviceId()){
                LocalInventoryInfo device_info;
                if(device.getInventory(device_info) == 0){
                    InventoryStorage::storage(id, std::move(device_info));
                }

                break;
            }
        }
    }

    return 0;
}

void ZcuDomain::stopCollect(){
    std::unique_lock<std::mutex> lock(collect_mutex_);

    stop_collect_ = true;
}