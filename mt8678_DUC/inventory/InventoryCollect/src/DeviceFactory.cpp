#include "DeviceFactory.hpp"


DeviceFactory::DeviceFactory(/* args */)
{
}

DeviceFactory::~DeviceFactory()
{
}

std::unique_ptr<DomainBase> DeviceFactory::createDeviceManager(DeviceType type){
    switch (type)
    {
        case DeviceType::ZCU:
            return std::make_unique<ZcuDomain>();
        case DeviceType::CDC:
            // return std::make_unique<CdcDomain>();
        default:
            throw std::runtime_error("Unsupported device");
    }
}