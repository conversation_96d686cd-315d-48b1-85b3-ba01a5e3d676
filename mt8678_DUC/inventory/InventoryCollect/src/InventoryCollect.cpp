#include "InventoryCollect.hpp"

InventoryCollect::InventoryCollect() : stop_collect_(false){

}

InventoryCollect::~InventoryCollect(){
    stopCollect();
    if (thread_.joinable()) {
        thread_.join();
    }
}


void InventoryCollect::initialize(DeviceType device_type) {
    domain_ = DeviceFactory::createDeviceManager(device_type);
    domain_->init();
}

void InventoryCollect::collectInventory(std::vector<std::string> device_id, std::function<void(std::vector<std::string>)> callback) {
    if(isCollecting()){
        return ;
    }

    /* 单线程实现 */
    thread_ = std::thread([this, device_id, callback](){
        if (domain_) {
            if(domain_->collectInventory(device_id) == 0){
                if(callback)callback(device_id);
            }
        }
    });
}


bool InventoryCollect::isCollecting() {
    return thread_.joinable();
}

void InventoryCollect::stopCollect(){
    if(isCollecting()){
        domain_->stopCollect();

        if (thread_.joinable()) {
            thread_.join();
        }
    }
}
