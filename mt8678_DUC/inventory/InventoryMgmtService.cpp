#include "InventoryMgmtService.h"
#include "DeviceManager.hpp"

namespace ota {
namespace duc {


void InventoryMgmtService::init(){
    entity_ = std::make_unique<InventoryCollect>();
#ifdef DUC_ZCU
    DeviceType domain_type = DeviceType::ZCU;
#else
    #ifdef DUC_CDC
        DeviceType domain_type = DeviceType::CDC;
    #else
        DeviceType domain_type = DeviceType::MDC;
    #endif
#endif 
    DeviceManager::instance().init(domain_type);
    entity_->initialize(domain_type);
}

bool InventoryMgmtService::collect(std::vector<std::string> device_list, std::function<void(std::vector<std::string>)> callback){
    if(false == DeviceManager::instance().validDevices(device_list)) return false;
    entity_->collectInventory(device_list, callback);

    return true;
}

void InventoryMgmtService::stopCollect(){
    entity_->stopCollect();
}

bool InventoryMgmtService::readInventory(std::vector<LocalInventoryInfo>& container){
    return InventoryStorage::take(container);
}

bool InventoryMgmtService::readInventory(std::vector<std::string> device_list, std::vector<LocalInventoryInfo>& container){
    return InventoryStorage::take(device_list, container);
}

}
}