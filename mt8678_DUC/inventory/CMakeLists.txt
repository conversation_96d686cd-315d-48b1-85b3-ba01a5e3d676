cmake_minimum_required(VERSION 3.16)
# set(MODULE_NAME InventoryService)

set(INCLUDE_DIR
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/common
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryCollect/include
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryCollect/include/zcu
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryCollect/include/cdc
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryStorage/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../ipc/doip
)

set(SRC_DIR 
    ${CMAKE_CURRENT_SOURCE_DIR}/
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryCollect/src
    ${CMAKE_CURRENT_SOURCE_DIR}/InventoryStorage/src
)

set(CPP_SOURCES)
foreach(dir ${SRC_DIR})
    file(GLOB_RECURSE dir_sources "${dir}/*.cpp")
    list(APPEND CPP_SOURCES ${dir_sources})
endforeach()

if(NOT TARGET ${MAIN_TARGET})
    message(FATAL_ERROR "Main target ${MAIN_TARGET} not defined")
endif()

target_include_directories(${MAIN_TARGET} PRIVATE ${INCLUDE_DIR})
target_sources(${MAIN_TARGET} PRIVATE ${CPP_SOURCES})
