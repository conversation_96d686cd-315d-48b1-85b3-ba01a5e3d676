#pragma

#include <string>
#include <unordered_map>
#include <mutex>
#include <vector>
#include "unity.hpp"

class InventoryStorage{
public:
    /* clean database */
    static void clean();

    static bool contains(std::string device_id);

    /* storage device inventory into database */
    static bool storage(std::string device_id, LocalInventoryInfo&& info);

    /* read device inventory from database */
    static bool take(const std::vector<std::string> device_list, std::vector<LocalInventoryInfo>& container);
    static bool take(std::vector<LocalInventoryInfo>& container);

    /* read single item*/
    static bool get_part_number(std::string devices_id, std::string& data);
    static bool get_sw_version(std::string devices_id, std::string& data);
    static bool get_hw_version(std::string devices_id, std::string& data);
    static bool get_supplier_code(std::string devices_id, std::string& data);
    static bool get_ecu_name(std::string devices_id, std::string& data);
    static bool get_sn(std::string devices_id, std::string& data);
    static bool get_ecu_batch_number(std::string devices_id, std::string& data);
    static bool get_bootloader_version(std::string devices_id, std::string& data);
    static bool get_backup_version(std::string devices_id, std::string& data);
    static bool get_upgrade_mode(std::string devices_id, bool& mode);
private:
    static std::unordered_map<std::string, LocalInventoryInfo> database_;
    static std::mutex mutex_;
};