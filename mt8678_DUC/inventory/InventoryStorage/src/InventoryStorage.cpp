#include "InventoryStorage.hpp"

std::unordered_map<std::string, LocalInventoryInfo> InventoryStorage::database_;
std::mutex InventoryStorage::mutex_;

void InventoryStorage::clean() {
    std::lock_guard<std::mutex> lock(mutex_);
    database_.clear();
}

bool InventoryStorage::storage(std::string device_id, LocalInventoryInfo&& info) {
    std::lock_guard<std::mutex> lock(mutex_);
    database_.insert_or_assign(device_id, std::move(info));
    return true;
}

bool InventoryStorage::take(const std::vector<std::string> device_list, std::vector<LocalInventoryInfo>& container) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (database_.empty()) {
        return false;
    }

    for(auto device : device_list){
        auto it = database_.find(device);
        if(it != database_.end()){
            container.emplace_back(it->second);
        }else{
            return false;
        }
    }

    return true;
}

bool InventoryStorage::take(std::vector<LocalInventoryInfo>& container){
    std::lock_guard<std::mutex> lock(mutex_);

    if (database_.empty()) {
        return false;
    }

    container.reserve(container.size() + database_.size());
    
    for(auto it = database_.begin(); it != database_.end();){
        auto node = database_.extract(it++);
        container.emplace_back(std::move(node.mapped()));
    }

    return true;
}

bool InventoryStorage::contains(std::string device_id) {
    std::lock_guard<std::mutex> lock(mutex_);
    return database_.find(device_id) != database_.end();
}

bool InventoryStorage::get_part_number(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.part_number;
        return true;
    }
    return false;
}

bool InventoryStorage::get_sw_version(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.software_version;
        return true;
    }
    return false;
}

bool InventoryStorage::get_hw_version(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.hardware_version;
        return true;
    }
    return false;
}

bool InventoryStorage::get_supplier_code(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.supplier_code;
        return true;
    }
    return false;
}

bool InventoryStorage::get_ecu_name(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.ecu_name;
        return true;
    }
    return false;
}

bool InventoryStorage::get_sn(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.serial_number;
        return true;
    }
    return false;
}

bool InventoryStorage::get_ecu_batch_number(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.ecu_batch_number;
        return true;
    }
    return false;
}

bool InventoryStorage::get_bootloader_version(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.bootloader_version;
        return true;
    }
    return false;
}

bool InventoryStorage::get_backup_version(std::string device_id, std::string& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.backup_version;
        return true;
    }
    return false;
}

bool InventoryStorage::get_upgrade_mode(std::string device_id, bool& data) {
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = database_.find(device_id);
    if (it != database_.end()) {
        data = it->second.seamless_mode;
        return true;
    }
    return false;
}