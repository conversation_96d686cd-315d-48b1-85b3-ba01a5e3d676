#pragma once
#include <vector>
#include <string>
#include <unordered_set>
#include <mutex>
#include "unity.hpp"

namespace ota {
namespace duc {

class DeviceManager {
public:
    static DeviceManager& instance() {
        static DeviceManager instance;
        return instance;
    }

    DeviceManager(const DeviceManager&) = delete;
    DeviceManager& operator=(const DeviceManager&) = delete;

    struct VehicleDeviceInfo {
        DeviceType type;
        std::unordered_set<std::string> device_ids;
    };

    bool init(DeviceType type) {
        static const std::vector<VehicleDeviceInfo> device_info = {
            {DeviceType::CDC, {"0", "1", "2"}}, 
            {DeviceType::ZCU, {"50", "51", "52"}},
            {DeviceType::MDC, {"3", "4"}}    
        };

        std::lock_guard<std::mutex> lock(mutex_); 
        
        for (const auto& info : device_info) {
            if (info.type == type) {
                device_list_ = info.device_ids;
                break;
            }
        }

        if (device_list_.empty()) {
            return false;
        }

        return true;
    }

    bool validDevices(const std::vector<std::string>& id_list) const {
        if (id_list.empty()) return false;

        std::lock_guard<std::mutex> lock(mutex_);
        for (const auto& id : id_list) {
            if (device_list_.find(id) == device_list_.end()) {
                return false;
            }
        }
        return true;
    }

private:
    DeviceManager() = default;
    ~DeviceManager() = default;

    std::unordered_set<std::string> device_list_;
    mutable std::mutex mutex_;
};

}
} 