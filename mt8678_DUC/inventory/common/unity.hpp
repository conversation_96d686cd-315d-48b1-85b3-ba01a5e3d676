#pragma once

#include <string>

struct LocalInventoryInfo
{
    std::string part_number;
    std::string software_version;
    std::string supplier_code;
    std::string ecu_name;
    std::string serial_number;
    std::string hardware_version;
    std::string ecu_batch_number;
    std::string bootloader_version;
    std::string backup_version;
    bool seamless_mode;
};

enum class DeviceType : uint8_t {
    ZCU,
    CDC,
    MDC,
};