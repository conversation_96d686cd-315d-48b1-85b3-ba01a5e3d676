#pragma once

#include <vector>
#include "ServiceManager.h"
#include "InventoryCollect.hpp"


namespace ota {
namespace duc {

class InventoryMgmtService : public IService
{
public:
    explicit InventoryMgmtService() = default;
    ~InventoryMgmtService() = default;

    static const std::string name() { return "InventoryMgmtService"; };

    void init();
    bool collect(std::vector<std::string> device_list, std::function<void(std::vector<std::string>)> callback);
    void stopCollect();
    bool readInventory(std::vector<LocalInventoryInfo>& container);
    bool readInventory(std::vector<std::string> device_list, std::vector<LocalInventoryInfo>& container);
private:
    std::unique_ptr<InventoryCollect> entity_;

};

REGISTER_DEFAULT_SERVICE(InventoryMgmtService);
}
}
