module seres {
module ota_duc_service {

    /*---------------service and topic name----------------*/
    const int32 DomainParticipant_ID = 1;

    const string CDC_SERVICE_NAME = "OTA_CDC_Service";
    const string MDC_SERVICE_NAME = "OTA_MDC_Service";
    const string ZCU_SERVICE_NAME = "OTA_ZCU_Service";

    const string CDC_TOPIC_NAME = "OTA_CDC_TOPIC";
    const string MDC_TOPIC_NAME = "OTA_MDC_TOPIC";
    const string ZCU_TOPIC_NAME = "OTA_ZCU_TOPIC";

    enum Status {
        kStatusInProgress,  // 进行中
        kStatusSuccess,     // 成功
        kStatusFailed,      // 失败，具体原因参见ErrorReasonCode
        @value(0xFF)kStatusNR
    };

    enum ErrorReasonCode {
        kReasonNone,
        kReasonParamErr,                        // 参数错误
        kReasonOperationNotSupport,             // 操作不支持
        kReasonSysErr,                          // 系统错误
        kReasonDecryptFailed,                   // 解密失败
        kReasonDiskSpaceNotEnough,              // 磁盘空间不够
        kReasonNetworkInaccessible,             // 网络不可达
        @value(51)kReasonGetInventoryFailed,    // 获取Inventory失败
        kReasonStartDownloadNotifyFailed,       // 通知启动下载失败
        kReasonPauseDownloadFailed,             // 暂停下载失败
        kReasonSysBusyInGetInventory,           // 获取资产信息中，系统忙碌
        kReasonCancelDownloadFailed,            // 取消下载失败
        kReasonSignatureVerifyFailed,           // 验签失败
        kReasonIntegrityVerifyFailed,           // 完整性验证失败
        kReasonPreUpgradeCheckNotifyFailed,     // 通知升级前检查失败
        kReasonCertVerifyFailed,                // 证书验证失败
        kReasonEnterDomainUpgradeModeFailed,    // 进入域的升级模式失败
        kReasonExitUpgradeModeFailed,           // 退出升级模式失败
        kReasonStartUpgradeModeNotifyFailed,    // 通知启动升级模式失败
        kReasonStartInstallNotifyFailed,        // 通知启动安装失败
        kReasonUnzipInstallPackageFailed,       // 解压安装包失败
        kReasonDeploymentPackageFailed,         // 部署安装包失败
        kReasonStartActiveNotifyFailed,         // 通知启动激活失败
        kReasonUpgradeFailedBecauseStartupAnomaly, //升级失败：启动异常
        kReasonUpgradeFailedBecauseVersionVerifyAnomaly, //升级失败：版本校验异常
        kReasonStartRollbackNotifyFailed,       // 通知启动回滚失败
        kReasonUnzipRollbackPackageFailed,      // 解压回滚包失败
        kReasonUpgradePackageNotAvailable,      // 升级包不在位
        kReasonUpgradePackageErr,               // 升级包错误
        kReasonRollbackPackageNotAvailable,     // 回滚包不在位
        kReasonRollbackPackageErr,              // 回滚包错误
        kReasonBaseVersionSubpackageErr,        // 差分包基本版本错误
        kReasonDomainPreUpgradeCheckFailed,     // 域升级前检查失败
        kReasonDiffFailedBecauseWriteFileFailed, // 差分失败，写文件失败
        kReasonDiskSpaceNotEnoughInUpgradeOrRollback, // 升级或回滚中出现磁盘空间不足
        kReasonDiffFailedBecauseSysErr,         // 差分失败，系统错误
        kReasonFlashFailedBecauseInformedStop,  // 被通知停止，刷写失败
        kReasonCallUAAnomaly,                   // 调用UA网络异常

        // TODO add other reason here...

        @value(1024)kReasonNR
    };

    // DUC 共有状态描述
    struct CommonStatus {
        Status status;
        ErrorReasonCode reason;
    };

    enum DUCType{
        CDC,    //座舱域
        MDC,    //智驾域
        ZCU     //其他域
    };

    //待升级部分资产信息列表
    struct SelectedInventoryList{
        sequence<string> inventoryLists;
    };

    // 资产信息
    struct InventoryInfo {
        string partNumber;        // 零部件件号
        string softwareVersion;   // 软件版本号
        string supplierCode;      // 供应商代码
        string ecuName;           // ECU名称
        string serialNumber;      // 序号代码
        string hardwareVersion;   // 硬件版本号
        string ecuBatchNumber;    // ECU批次号
        string bootloaderVersion; // bootloader软件版本号
        string backupVersion;     // 备份区软件版本号
        boolean SeamlessModeSupport;  // 是否支持无感升级
    };

    //资产信息获取结果列表
    struct InventoryResult {
        sequence<InventoryInfo> InventoryLists;// 资产信息列表
    };

    // 下载前检查条件
    struct DownloadRequirement{
        string deviceId;         // 设备ID
        uint64 diskRequirement;  // 磁盘空间要求
    };

    struct DownloadConditionLists{
        sequence<DownloadRequirement> downloadRequirementLists;   // 下载条件列表
    };

    // 升级包下载任务信息
    struct DownloadTaskInfo {
        string taskId;          // 任务ID
        string packageVersion;  // 包版本号
        string packageName;     // 包名称
        string packageUrl;      // 包下载地址
        string packageSize;     // 包大小
        string packageMd5;      // 包MD5
    };
    struct DownloadTaskLists {
        sequence<DownloadTaskInfo> taskLists;   // 下载任务信息列表
    };

    // 下载过程控制
    enum DownloadCtrl {
        PAUSE,
        RESUME,
        CANCEL
    };

    // 进度信息
    struct DownloadProgressInfo {
        uint8 progressPercent;  // 0-100
        string packageName;     // 包名称
        uint64 downloadedSize;  // 已下载大小
        uint64 totalSize;       // 总大小
        CommonStatus status;
    };

    //下载进度
    struct DownloadProgress {
        boolean allFinished;
        sequence<DownloadProgressInfo> progressLists;   //下载进度列表
    };

    //升级模式
    enum UpdateMode {
        SeamlessMode,
        FormalMode
    };

    //待升级设备列表
    struct UpdateDeviceList {
        sequence<string> updateDeviceLists;
    };

    // 升级进度信息
    struct DeviceUpdateProgress {
        uint8 progressPercent;      // 进度百分比 0-100 %
        string deviceName;          // 设备名称
        string deviceId;            // 设备ID
        CommonStatus status;        // 设备状态
    };

    // 升级进度列表
    struct UpdateProgress {
        boolean allFinished;
        sequence<DeviceUpdateProgress> progressLists;   // 升级进度列表
    };

    struct RollbackComponentList {
        sequence<string> rollbackLists;
    };

    enum OTA_TopicData{
        INVENTORY_RESULT,
        DOWNLOAD_PROGRESS,
        UZIP_PACKAGES_RESULT,
        PACKAGES_VERIFY_RESULT,
        CHECK_UPDATE_CONDITION_RESULT,
        UPDATE_PROGRESS,
        ROLLBACK_PROGRESS
    };

    @final
    union OTA_DucDataUnion switch (OTA_TopicData) {
        case INVENTORY_RESULT:
            InventoryResult inventoryResult;
        case DOWNLOAD_PROGRESS:
            DownloadProgress downloadProgress;
        case UZIP_PACKAGES_RESULT:
            CommonStatus uzipPackagesResult;
        case PACKAGES_VERIFY_RESULT:
            CommonStatus packagesVerifyResult;
        case CHECK_UPDATE_CONDITION_RESULT:
            CommonStatus checkUpdateConditionResult;
        case UPDATE_PROGRESS:
            UpdateProgress updateProgress;
        case ROLLBACK_PROGRESS:
            UpdateProgress rollbackProgress;
    };

};
};
