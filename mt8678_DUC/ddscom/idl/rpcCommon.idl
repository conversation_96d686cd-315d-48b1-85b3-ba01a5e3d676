module dds { module rpc {

    typedef octet GuidPrefix_t[12];

    struct EntityId_t {
        octet entityKey[3];
        octet entityKind;
    };

    struct GUID_t {
        GuidPrefix_t guidPrefix;
        EntityId_t entityId;
    };
    struct SequenceNumber_t {
        long high;
        unsigned long low;
    };

    struct SampleIdentity {
        GUID_t writer_guid;
        SequenceNumber_t sequence_number;
    };

    struct RequestHeader {
        SampleIdentity requestId;
    };

    struct ReplyHeader {
        SampleIdentity relatedRequestId;
    };

};};
