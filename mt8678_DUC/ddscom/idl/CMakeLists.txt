cmake_minimum_required(VERSION 3.16)

if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "x86" CACHE STRING "Choose the build type: x86, aarch64")
endif()
set_property(CACHE CMAKE_BUILD_TYPE PROPERTY STRINGS "x86" "aarch64")

if(CMAKE_BUILD_TYPE STREQUAL "x86")
    set(CMAKE_C_COMPILER "gcc")
    set(CMAKE_CXX_COMPILER "g++")
elseif(CMAKE_BUILD_TYPE STREQUAL "aarch64")
    set(LINARO_GCC_ROOT /usr/bin)
    set(CMAKE_C_COMPILER "${LINARO_GCC_ROOT}/aarch64-linux-gnu-gcc")
    set(CMAKE_CXX_COMPILER "${LINARO_GCC_ROOT}/aarch64-linux-gnu-g++")
else()
    message(FATAL_ERROR "Unknown build type: ${CMAKE_BUILD_TYPE}")
endif()

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)
set(CMAKE_CXX_FLAGS "-std=c++17 ${CMAKE_CXX_FLAGS}")

project(OtaIdle LANGUAGES C CXX)

if(NOT TARGET CycloneDDS-CXX::ddscxx)
    find_package(CycloneDDS-CXX REQUIRED)
endif()

find_program(IDLC_EXECUTABLE "idlc"
             PATHS ${IDLC_BIN_PATH}
             NO_CMAKE_FIND_ROOT_PATH REQUIRED
)

set(DDSCOM_IDL_GEN_DIR ${CMAKE_SOURCE_DIR}/target/generate)
set(DDSCOM_IDL_INC_DIR ${CMAKE_SOURCE_DIR}/target/include)
set(DDSCOM_IDL_LIB_DIR ${CMAKE_SOURCE_DIR}/target/lib)

file(MAKE_DIRECTORY "${DDSCOM_IDL_GEN_DIR}")
file(MAKE_DIRECTORY "${DDSCOM_IDL_INC_DIR}")
file(MAKE_DIRECTORY "${DDSCOM_IDL_LIB_DIR}")

file(GLOB IDL_FILES "${CMAKE_SOURCE_DIR}/*.idl")

set(ddscom_idl_cpp
    ${DDSCOM_IDL_GEN_DIR}/rpcCommon.cpp
    ${DDSCOM_IDL_GEN_DIR}/OTA_DucData.cpp
    ${DDSCOM_IDL_GEN_DIR}/OTA_DucInterface.cpp
    ${DDSCOM_IDL_GEN_DIR}/OTA_DucInterface_gen_server.cpp
    ${DDSCOM_IDL_GEN_DIR}/OTA_DucInterface_gen_client.cpp
)

add_custom_command(
    OUTPUT ${ddscom_idl_cpp}
    WORKING_DIRECTORY ${DDSCOM_IDL_GEN_DIR}
    COMMAND 
        "${IDLC_EXECUTABLE}" -l cxx  "${CMAKE_SOURCE_DIR}/rpcCommon.idl" &&
        "${IDLC_EXECUTABLE}" -l cxx  "${CMAKE_SOURCE_DIR}/OTA_DucData.idl" &&
        "${IDLC_EXECUTABLE}" -l cxx  "${CMAKE_SOURCE_DIR}/OTA_DucInterface.idl" &&
        "${IDLC_EXECUTABLE}" -l cxx -r client  "${CMAKE_SOURCE_DIR}/OTA_DucInterface.idl" &&
        "${IDLC_EXECUTABLE}" -l cxx -r server  "${CMAKE_SOURCE_DIR}/OTA_DucInterface.idl"
    DEPENDS ${IDL_FILES}
    COMMENT "Generating DDS IDL Interface Code"
    VERBATIM
)

add_library(otaidle STATIC ${ddscom_idl_cpp})
target_include_directories(otaidle PUBLIC
    ${DDSCOM_IDL_GEN_DIR}
    ${RPC_TARGET_PATH}/include/rpcddscxx
)
target_link_directories(otaidle PUBLIC ${RPC_TARGET_PATH}/lib)
target_link_libraries(otaidle PUBLIC CycloneDDS-CXX::ddscxx rpcddscxx)

install(TARGETS otaidle
    LIBRARY DESTINATION "${DDSCOM_IDL_LIB_DIR}"  
    ARCHIVE DESTINATION "${DDSCOM_IDL_LIB_DIR}"
)

install(DIRECTORY "${DDSCOM_IDL_GEN_DIR}/"
    DESTINATION "${DDSCOM_IDL_INC_DIR}"
    FILES_MATCHING PATTERN "*.hpp"
)