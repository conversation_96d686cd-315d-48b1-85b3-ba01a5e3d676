#pragma once

#include "dds/dds.hpp"

namespace ota {
namespace duc {

template <typename DataType>
class TopicPublisher
{
public:
    TopicPublisher(uint32_t domain_id, const std::string& topic_name) :
        TopicPublisher(dds::domain::DomainParticipant(domain_id), topic_name) {}

    TopicPublisher(const dds::domain::DomainParticipant& participant,
                   const std::string& topic_name) :
        _publisher(participant),
        _topic(participant, topic_name),
        _writer(_publisher, _topic) {}

    void send(const DataType& message) {
        _writer.write(message);
    }

private:
    dds::pub::Publisher _publisher;
    dds::topic::Topic<DataType> _topic;
    dds::pub::DataWriter<DataType> _writer;
};

}
}
