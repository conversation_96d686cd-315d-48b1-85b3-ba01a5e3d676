#pragma once

#include <memory>
#include "OTA_DucData.hpp"
#include "TopicPublisher.h"

namespace ota {
namespace duc {

namespace duc_dds_svc = ::seres::ota_duc_service;

class IDucDdsServiceCallback
{
public:
    virtual void onInventoryResult(duc_dds_svc::InventoryResult &result) = 0;
    virtual void onDownloadProgress(duc_dds_svc::DownloadProgress &progress) = 0;
    virtual void onUzipPackagesResult(duc_dds_svc::CommonStatus &result) = 0;
    virtual void onPackagesVerifyResult(duc_dds_svc::CommonStatus &result) = 0;
    virtual void onCheckUpdateConditionResult(duc_dds_svc::CommonStatus &result) = 0;
    virtual void onUpdateProgress(duc_dds_svc::UpdateProgress &progress) = 0;
};

class DucDdsServiceCallback : public IDucDdsServiceCallback
{
public:
    explicit DucDdsServiceCallback(std::unique_ptr<TopicPublisher<duc_dds_svc::OTA_DucDataUnion>> &publisher) {
        _publisher = std::move(publisher);
    }

    void onInventoryResult(duc_dds_svc::InventoryResult &result) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.inventoryResult(std::move(result));
        _publisher->send(data_union);
    }

    void onDownloadProgress(duc_dds_svc::DownloadProgress &progress) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.downloadProgress(std::move(progress));
        _publisher->send(data_union);
    }

    void onUzipPackagesResult(duc_dds_svc::CommonStatus &result) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.uzipPackagesResult(std::move(result));
        _publisher->send(data_union);
    }

    void onPackagesVerifyResult(duc_dds_svc::CommonStatus &result) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.packagesVerifyResult(std::move(result));
        _publisher->send(data_union);
    }

    void onCheckUpdateConditionResult(duc_dds_svc::CommonStatus &result) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.checkUpdateConditionResult(std::move(result));
        _publisher->send(data_union);
    }

    void onUpdateProgress(duc_dds_svc::UpdateProgress &progress) override {
        duc_dds_svc::OTA_DucDataUnion data_union;
        data_union.updateProgress(std::move(progress));
        _publisher->send(data_union);
    }

private:
    std::unique_ptr<TopicPublisher<duc_dds_svc::OTA_DucDataUnion>> _publisher;
};

}
}
