#include <memory>

#include "DUCLogger.h"

#include "DucDdsService.h"
#include "ServiceManager.h"
#include "InventoryMgmtService.h"
#include "PackageDownloadService.h"
#include "PackageMgmtService.h"
#include "DomainUpdateService.h"
#include "DomainLogService.h"

#include "DeviceManager.hpp"

namespace ota {
namespace duc {

DucDdsService::DucDdsService() {}

duc_dds_svc::ReturnCode DucDdsService::inventoryCollection(const duc_dds_svc::SelectedInventoryList &inventory_list)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<InventoryMgmtService> service =
        ServiceManager::instance().getDefaultService<InventoryMgmtService>();

    std::vector<std::string> device_list;
    device_list = inventory_list.inventoryLists();

    bool ret = service->collect(device_list, [this](const std::vector<std::string>& device_list){
        this->publishInventoryData(device_list);
    });

    if(false == ret) {
        /* invaild devices */
        return duc_dds_svc::ReturnCode::ERROR;
    }

    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::stopInventoryCollection()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<InventoryMgmtService> service =
        ServiceManager::instance().getDefaultService<InventoryMgmtService>();
    // TODO
    service->stopCollect();

    return duc_dds_svc::ReturnCode::REFUSED;
}

/* 暂时保留 */
duc_dds_svc::ReturnCode DucDdsService::getInventoryResult(duc_dds_svc::InventoryResult &inventory_list)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<InventoryMgmtService> service =
        ServiceManager::instance().getDefaultService<InventoryMgmtService>();

    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::checkDownloadCondition(const duc_dds_svc::DownloadConditionLists &conditions, seres::ota_duc_service::CommonStatus &condition_result)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageDownloadService> service =
        ServiceManager::instance().getDefaultService<PackageDownloadService>();
    // TODO
    condition_result.status(duc_dds_svc::Status::kStatusNR);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::startDownload(const duc_dds_svc::DownloadTaskLists &task_list)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageDownloadService> service =
        ServiceManager::instance().getDefaultService<PackageDownloadService>();
    // TODO
    std::thread([this]{
            const uint64_t total_size = 10240;
            uint64_t download_size = 0;
            duc_dds_svc::DownloadProgress download_progress;
            duc_dds_svc::DownloadProgressInfo progress_info;

            progress_info.packageName("test_package");
            progress_info.downloadedSize(0);
            progress_info.totalSize(total_size);

            download_progress.allFinished(false);
            download_progress.progressLists().push_back(progress_info);
            do {
                std::this_thread::sleep_for(std::chrono::seconds(1));

                download_size += 1024;
                progress_info.downloadedSize(download_size);
                uint32_t percent = download_size * 100 / total_size;
                progress_info.progressPercent(percent);

                if (_callback != nullptr)
                    _callback->onDownloadProgress(download_progress);
                duclogger::debug("publish download progress: " + std::to_string(percent));
            } while(download_size < total_size);

            if (download_size == total_size) {
                progress_info.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusSuccess,
                                                               duc_dds_svc::ErrorReasonCode::kReasonNone});
                download_progress.allFinished(true);
            } else {
                progress_info.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusFailed,
                                                               duc_dds_svc::ErrorReasonCode::kReasonStartDownloadNotifyFailed});
            }

            if (_callback != nullptr)
                _callback->onDownloadProgress(download_progress);
        }).detach();
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::downloadCtrl(const duc_dds_svc::DownloadCtrl &download_command)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageDownloadService> service =
        ServiceManager::instance().getDefaultService<PackageDownloadService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}

duc_dds_svc::ReturnCode DucDdsService::getDownloadProgress(duc_dds_svc::DownloadProgress &download_progress)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageDownloadService> service =
        ServiceManager::instance().getDefaultService<PackageDownloadService>();
    // TODO
    duc_dds_svc::DownloadProgressInfo progress_info;
    progress_info.progressPercent(50);
    progress_info.packageName("test_package");
    progress_info.downloadedSize(500);
    progress_info.totalSize(1000);
    progress_info.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusSuccess,
                                                   duc_dds_svc::ErrorReasonCode::kReasonNone});
    download_progress.allFinished(false);
    download_progress.progressLists().push_back(progress_info);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::uzipPackages()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageMgmtService> service =
        ServiceManager::instance().getDefaultService<PackageMgmtService>();
    // TODO
    std::thread([this]{
        std::this_thread::sleep_for(std::chrono::seconds(2));
        duc_dds_svc::CommonStatus uzip_Result;
        uzip_Result.status(duc_dds_svc::Status::kStatusSuccess);
        uzip_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);

        if (_callback != nullptr)
            _callback->onUzipPackagesResult(uzip_Result);
    }).detach();
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::getuzipPackagesResult(seres::ota_duc_service::CommonStatus &uzip_Result)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageMgmtService> service =
        ServiceManager::instance().getDefaultService<PackageMgmtService>();
    // TODO
    uzip_Result.status(duc_dds_svc::Status::kStatusSuccess);
    uzip_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::startPackagesVerify()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageMgmtService> service =
        ServiceManager::instance().getDefaultService<PackageMgmtService>();
    // TODO
    std::thread([this]{
        duc_dds_svc::CommonStatus verify_Result;
        verify_Result.status(duc_dds_svc::Status::kStatusSuccess);
        verify_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);

        if (_callback != nullptr)
            _callback->onPackagesVerifyResult(verify_Result);
    }).detach();
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::getPackagesVerifyResult(seres::ota_duc_service::CommonStatus &verify_Result)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<PackageMgmtService> service =
        ServiceManager::instance().getDefaultService<PackageMgmtService>();
    // TODO
    verify_Result.status(duc_dds_svc::Status::kStatusSuccess);
    verify_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::checkUpdateCondition()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    std::thread([this]{
        std::this_thread::sleep_for(std::chrono::seconds(2));
        duc_dds_svc::CommonStatus checkcondition_Result;
        checkcondition_Result.status(duc_dds_svc::Status::kStatusSuccess);
        checkcondition_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);

        if (_callback != nullptr)
            _callback->onCheckUpdateConditionResult(checkcondition_Result);
    }).detach();
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::getCheckUpdateConditionResult(seres::ota_duc_service::CommonStatus &checkcondition_Result)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    checkcondition_Result.status(duc_dds_svc::Status::kStatusSuccess);
    checkcondition_Result.reason(duc_dds_svc::ErrorReasonCode::kReasonNone);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::startUpdate(const duc_dds_svc::UpdateMode &mode, const duc_dds_svc::UpdateDeviceList &update_list)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    std::thread([this, service]{
        int p = 0;
        uint64_t update_count = 0;

        service->zcuUpgrade();

        do{
            std::this_thread::sleep_for(std::chrono::seconds(1));
            p += 20;
            update_count += 1024;
            duc_dds_svc::UpdateProgress update_progress;
            duc_dds_svc::DeviceUpdateProgress progress;
            progress.progressPercent(p);
            progress.deviceName("TestDevice");
            progress.deviceId("DEV001");
            progress.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusInProgress,
                                                      duc_dds_svc::ErrorReasonCode::kReasonNone});
            update_progress.allFinished(p == 100);
            update_progress.progressLists().push_back(progress);

            if (_callback != nullptr)
                _callback->onUpdateProgress(update_progress);
            duclogger::debug("publish update progress: " + std::to_string(p));
        }while(p < 100);
    }).detach();
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::resumeUpdate()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}

duc_dds_svc::ReturnCode DucDdsService::pauseUpdate()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}

duc_dds_svc::ReturnCode DucDdsService::getUpdateProgress(duc_dds_svc::UpdateProgress &update_progress)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    duc_dds_svc::DeviceUpdateProgress progress;
    progress.progressPercent(75);
    progress.deviceName("TestDevice");
    progress.deviceId("DEV001");
    progress.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusInProgress,
                                              duc_dds_svc::ErrorReasonCode::kReasonNone});

    update_progress.allFinished(false);
    update_progress.progressLists().push_back(progress);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::activate()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}

duc_dds_svc::ReturnCode DucDdsService::rollback(const duc_dds_svc::RollbackComponentList &component_list)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}

duc_dds_svc::ReturnCode DucDdsService::getRollbackProgress(duc_dds_svc::UpdateProgress &update_progress)
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainUpdateService> service =
        ServiceManager::instance().getDefaultService<DomainUpdateService>();
    // TODO
    duc_dds_svc::DeviceUpdateProgress progress;
    progress.progressPercent(25);
    progress.deviceName("TestDevice");
    progress.deviceId("DEV001");
    progress.status(duc_dds_svc::CommonStatus{duc_dds_svc::Status::kStatusInProgress,
                                              duc_dds_svc::ErrorReasonCode::kReasonNone});
    update_progress.allFinished(false);
    update_progress.progressLists().push_back(progress);
    return duc_dds_svc::ReturnCode::OK;
}

duc_dds_svc::ReturnCode DucDdsService::uploadLog()
{
    duclogger::debug(__FUNCTION__);
    std::shared_ptr<DomainLogService> service =
        ServiceManager::instance().getDefaultService<DomainLogService>();
    // TODO
    return duc_dds_svc::ReturnCode::REFUSED;
}


void DucDdsService::publishInventoryData(const std::vector<std::string>& device_list){
    std::shared_ptr<InventoryMgmtService> service =
        ServiceManager::instance().getDefaultService<InventoryMgmtService>();

    duc_dds_svc::InventoryResult inventory_result;
    std::vector<LocalInventoryInfo> container;
    service->readInventory(device_list, container);

    for(auto it : container){
        duc_dds_svc::InventoryInfo info;

        info.partNumber(it.part_number);
        info.softwareVersion(it.software_version);
        info.supplierCode(it.supplier_code);
        info.ecuName(it.ecu_name);
        info.serialNumber(it.serial_number);
        info.hardwareVersion(it.hardware_version);
        info.ecuBatchNumber(it.ecu_batch_number);
        info.bootloaderVersion(it.bootloader_version);
        info.backupVersion(it.backup_version);
        info.SeamlessModeSupport(it.seamless_mode);

        inventory_result.InventoryLists().emplace_back(std::move(info));
    }

    if (_callback != nullptr)
        _callback->onInventoryResult(inventory_result);
}

}
}
