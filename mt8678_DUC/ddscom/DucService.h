#pragma once

#include "DucDdsService.h"
#include "IDucService.h"
#include "DucType.h"
#include "DUCLogger.h"

namespace ota {
namespace duc {

struct DucServiceConfig
{
    DucType duc_type;
    uint32_t domain_id;
    std::string service_name;
    std::string topic_name;
};


class DucService : public IDucService
{
public:
    DucService(const DucType type) {
        _config = LookupConfig(type);
        _name = _config.service_name;
    };

    static DucServiceConfig LookupConfig(DucType type) {
        const static DucServiceConfig duc_configs[] = {
            {DucType::CDC, duc_dds_svc::DomainParticipant_ID, duc_dds_svc::CDC_SERVICE_NAME, duc_dds_svc::CDC_TOPIC_NAME},
            {DucType::MDC, duc_dds_svc::DomainParticipant_ID, duc_dds_svc::MDC_SERVICE_NAME, duc_dds_svc::MDC_TOPIC_NAME},
            {DucType::ZCU, duc_dds_svc::DomainParticipant_ID, duc_dds_svc::ZCU_SERVICE_NAME, duc_dds_svc::ZCU_TOPIC_NAME},
        };
        for (auto &config : duc_configs) {
            if (config.duc_type == type) return config;
        }
        return {DucType::UNK};
    }

    void run() override {
        try {

            dds::domain::DomainParticipant participant(_config.domain_id);
            duclogger::info(IDucService::getName() + " : create dp success");
            dds::rpc::Server server(dds::rpc::ServerParam(4));

            dds::rpc::ServiceParams param(participant);
            param.serviceName(_config.service_name);

            dds::pub::qos::DataWriterQos dataWriterQos;
            dataWriterQos.policy(dds::core::policy::Liveliness(dds::core::policy::LivelinessKind::Type::MANUAL_BY_TOPIC,dds::core::Duration::from_secs(2)));
            dds::sub::qos::DataReaderQos dataReaderQos;
            dataReaderQos.policy(dds::core::policy::Liveliness(dds::core::policy::LivelinessKind::Type::MANUAL_BY_PARTICIPANT,dds::core::Duration::from_secs(2)));
            dataReaderQos.policy(dds::core::policy::Reliability(dds::core::policy::ReliabilityKind::Type::RELIABLE,dds::core::Duration::from_secs(5)));
            param.dataReaderQos(dataReaderQos);
            param.dataWriterQos(dataWriterQos);

            std::shared_ptr<DucDdsService> impl = std::make_shared<DucDdsService>();
            auto publisher = std::make_unique<TopicPublisher<duc_dds_svc::OTA_DucDataUnion>>
                (participant, _config.topic_name);
            std::unique_ptr<IDucDdsServiceCallback> callback = std::make_unique<DucDdsServiceCallback>(publisher);
            impl->setCallback(callback);

            seres::ota_duc_service::DucServiceInterfaceService service(impl, server, param);
            server.run();
        } catch (const dds::core::Exception& e) {
            duclogger::error(IDucService::getName() + "exception: " + std::string(e.what()));
        }
    };

private:
    DucServiceConfig _config;
};

std::unique_ptr<IDucService> createDucService() {
    return std::make_unique<DucService>(DucType::CDC);
}

}
}
