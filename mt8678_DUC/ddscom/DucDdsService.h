#pragma once

#include "OTA_DucInterface_gen_server.hpp"
#include "DucDdsServiceCallback.h"
#include "TopicPublisher.h"

namespace ota {
namespace duc {

namespace duc_dds_svc = seres::ota_duc_service;

class DucDdsService : public duc_dds_svc::Interface_DucServiceInterface_BASE
{
public:
    explicit DucDdsService();

    void setCallback(std::unique_ptr<IDucDdsServiceCallback> &cb) {
        _callback = std::move(cb);
    }

    duc_dds_svc::ReturnCode inventoryCollection(const duc_dds_svc::SelectedInventoryList &inventory_list) override;
    duc_dds_svc::ReturnCode stopInventoryCollection() override;
    duc_dds_svc::ReturnCode getInventoryResult(duc_dds_svc::InventoryResult &inventory_list) override;
    duc_dds_svc::ReturnCode checkDownloadCondition(const seres::ota_duc_service::DownloadConditionLists &conditions, seres::ota_duc_service::CommonStatus &condition_result) override;
    duc_dds_svc::ReturnCode startDownload(const duc_dds_svc::DownloadTaskLists &task_list) override;
    duc_dds_svc::ReturnCode downloadCtrl(const duc_dds_svc::DownloadCtrl &download_command) override;
    duc_dds_svc::ReturnCode getDownloadProgress(duc_dds_svc::DownloadProgress &download_progress) override;
    duc_dds_svc::ReturnCode uzipPackages() override;
    duc_dds_svc::ReturnCode getuzipPackagesResult(seres::ota_duc_service::CommonStatus &uzip_Result) override;
    duc_dds_svc::ReturnCode startPackagesVerify() override;
    duc_dds_svc::ReturnCode getPackagesVerifyResult(seres::ota_duc_service::CommonStatus &verify_Result) override;
    duc_dds_svc::ReturnCode checkUpdateCondition() override;
    duc_dds_svc::ReturnCode getCheckUpdateConditionResult(seres::ota_duc_service::CommonStatus &checkcondition_Result) override;
    duc_dds_svc::ReturnCode startUpdate(const duc_dds_svc::UpdateMode &mode, const duc_dds_svc::UpdateDeviceList &update_list) override;
    duc_dds_svc::ReturnCode resumeUpdate() override;
    duc_dds_svc::ReturnCode pauseUpdate() override;
    duc_dds_svc::ReturnCode getUpdateProgress(duc_dds_svc::UpdateProgress &update_progress) override;
    duc_dds_svc::ReturnCode activate() override;
    duc_dds_svc::ReturnCode rollback(const duc_dds_svc::RollbackComponentList &component_list) override;
    duc_dds_svc::ReturnCode getRollbackProgress(duc_dds_svc::UpdateProgress &update_progress) override;
    duc_dds_svc::ReturnCode uploadLog() override;

    void publishInventoryData(const std::vector<std::string>& device_list);

private:
    std::unique_ptr<IDucDdsServiceCallback> _callback;
};

}
}
