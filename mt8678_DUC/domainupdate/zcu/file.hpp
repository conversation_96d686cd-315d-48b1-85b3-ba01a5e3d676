#pragma once
#include <iostream>
#include <fstream>
#include <vector>
#include <cstdint>
#include <iomanip>
#include <sstream>

class HexFileParser {
public:
    static bool parseHexFile(const std::string& filePath, uint32_t& fw_addr, std::vector<unsigned char>& data) {
        std::ifstream hexFile(filePath);
        if (!hexFile.is_open()) {
            std::cout << "Error: Unable to open file: " << filePath << std::endl;
            return false;
        }

        /* 解析第一行: 刷写地址 */
        std::string line;
        if(!std::getline(hexFile, line)) {
            std::cout << "Error: parse file failed " << std::endl;
            return false;
        }
        
        fw_addr = parseFirstLine(line);
        if (fw_addr < 0) {
            std::cout << "Error: fw_addr < 0 " << std::endl;
            return false;
        }

        /* 解析固件数据 */
        while (std::getline(hexFile, line)) {
            if (line.empty() || line[0] != ':') {
                continue;
            }
            
            size_t pos = 1;
            uint8_t length = hexToByte(line[pos], line[pos + 1]);
            pos += 2;
            
            uint16_t address = hexToByte(line[pos], line[pos + 1]) << 8;
            pos += 2;
            address |= hexToByte(line[pos], line[pos + 1]);
            pos += 2;
            
            uint8_t type = hexToByte(line[pos], line[pos + 1]);
            pos += 2;
            
            if (type == 0x04) {
                uint16_t extAddr = hexToByte(line[pos], line[pos + 1]) << 8;
                pos += 2;
                extAddr |= hexToByte(line[pos], line[pos + 1]);
                pos += 2;
            } else if (type == 0x00) {
                for (uint8_t i = 0; i < length; ++i) {
                    uint8_t byte = hexToByte(line[pos], line[pos + 1]);
                    data.emplace_back(byte); 
                    pos += 2;
                }
            } else if (type == 0x01) {
                return true;
            } else if (type == 0x05) {
                // 处理起始线性地址记录（如果需要）
            }
            
            /* 跳过校验和字段 */
            pos += 2;
        }
        return true;
    }

    static void printHexVector(const std::vector<unsigned char>& data) {
    for (size_t i = 0; i < data.size(); i += 16) {
        // 计算当前行范围
        size_t end = std::min(i + 16, data.size());
        size_t mid = std::min(i + 8, end);
        
        // 生成前8字节部分
        std::stringstream part1;
        for (size_t j = i; j < mid; ++j) {
            part1 << std::hex << std::setw(2) << std::setfill('0') 
                 << static_cast<int>(data[j]) << " ";
        }
        
        // 生成后8字节部分
        std::stringstream part2;
        bool hasSecondPart = (mid < end);
        if (hasSecondPart) {
            for (size_t j = mid; j < end; ++j) {
                part2 << std::hex << std::setw(2) << std::setfill('0') 
                     << static_cast<int>(data[j]) << " ";
            }
        }
        
        // 拼接最终字符串
        std::string finalStr = part1.str();
        if (!finalStr.empty() && finalStr.back() == ' ') {
            finalStr.pop_back();
        }
        if (hasSecondPart) {
            std::string p2 = part2.str();
            if (!p2.empty() && p2.back() == ' ') {
                p2.pop_back();
            }
            finalStr += " - " + p2;
        }
        std::cout << finalStr << std::endl;
    }
}
private:
    static uint8_t hexToByte(char high, char low) {
        uint8_t byte = 0;
        if (high >= '0' && high <= '9')       byte += (high - '0') << 4;
        else if (high >= 'A' && high <= 'F') byte += (high - 'A' + 10) << 4;
        
        if (low >= '0' && low <= '9')        byte += (low - '0');
        else if (low >= 'A' && low <= 'F')   byte += (low - 'A' + 10);
        
        return byte;
    }

    static int32_t parseFirstLine(const std::string& line) {
        if (line.empty() || line[0] != ':') {
            std::cout << "Error: Invalid first line in HEX file." << std::endl;
            return -1;
        }

        // std::cout << "Raw Record: " << line << std::endl;
        size_t pos = 1;
        
        uint8_t length = hexToByte(line[pos], line[pos + 1]);
        pos += 2;
        
        uint16_t address = hexToByte(line[pos], line[pos + 1]) << 8;
        pos += 2;
        address |= hexToByte(line[pos], line[pos + 1]);
        pos += 2;
        
        uint8_t type = hexToByte(line[pos], line[pos + 1]);
        pos += 2;
        
        // std::cout << "Length: 0x" << std::hex << static_cast<int>(length) << std::endl;
        // std::cout << "Address: 0x" << std::hex << address << std::endl;
        // std::cout << "Type: 0x" << std::hex << static_cast<int>(type) << std::endl;
        
        uint16_t dataField = 0;
        if (length == 2) {
            dataField = hexToByte(line[pos], line[pos + 1]) << 8;
            pos += 2;
            dataField |= hexToByte(line[pos], line[pos + 1]);
            pos += 2;
        } else {
            std::cout << "Error: Invalid data field length for extended linear address record." << std::endl;
            return -1;
        }
        
        uint32_t baseAddress = static_cast<uint32_t>(dataField) << 16;
        uint8_t recordChecksum = hexToByte(line[pos], line[pos + 1]);
        
        uint8_t checksum = length + (address >> 8) + (address & 0xFF) + type;
        checksum += (dataField >> 8) + (dataField & 0xFF);
        checksum = (-checksum) & 0xFF;
        
        // std::cout << "Data Field: 0x" << std::hex << dataField << std::endl;
        // std::cout << "Base Address: 0x" << std::hex << baseAddress << std::endl;
        // std::cout << "Record Checksum: 0x" << std::hex << static_cast<int>(recordChecksum) << std::endl;
        // std::cout << "Calculated Checksum: 0x" << std::hex << static_cast<int>(checksum) << std::endl;
        
        if (checksum != recordChecksum) {
            std::cout << "Checksum is invalid!" << std::endl;
            return -1;
        }
        
        std::cout << "Checksum is valid." << std::endl;
        return static_cast<int32_t>(baseAddress);
    }
};