#pragma once

#include "ZcuUpgrade.hpp"

extern "C" void service_10h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService10h(ta, data, len);
}

extern "C" void service_11h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService11h(ta, data, len);
}

extern "C" void service_14h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService14h(ta, data, len);
}

extern "C" void service_19h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService19h(ta, data, len);
}

extern "C" void service_22h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService22h(ta, data, len);
}

extern "C" void service_23h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService23h(ta, data, len);
}

extern "C" void service_27h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService27h(ta, data, len);
}

extern "C" void service_28h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService28h(ta, data, len);
}

extern "C" void service_2Ah_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService2Ah(ta, data, len);
}

extern "C" void service_2Ch_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService2Ch(ta, data, len);
}

extern "C" void service_2Eh_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService2Eh(ta, data, len);
}

extern "C" void service_2Fh_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService2Fh(ta, data, len);
}

extern "C" void service_31h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService31h(ta, data, len);
}

extern "C" void service_34h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService34h(ta, data, len);
}

extern "C" void service_36h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService36h(ta, data, len);
}

extern "C" void service_37h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService37h(ta, data, len);
}

extern "C" void service_38h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService38h(ta, data, len);
}

extern "C" void service_3Dh_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService3Dh(ta, data, len);
}

extern "C" void service_3Eh_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService3Eh(ta, data, len);
}

extern "C" void service_85h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService85h(ta, data, len);
}

extern "C" void service_29h_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService29h(ta, data, len);
}

extern "C" void service_7fh_wrapper(uint16_t ta, const uint8_t* data, 
                                   size_t len, void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleService7fh(ta, data, len);
}

// extern "C" void timer_keep_alive_wrapper(void* ctx) {
//     auto* handler = static_cast<ZcuUpgrade*>(ctx);
//     handler->handle_timer_keep_alive();
// }

extern "C" void timer_nrc78_wrapper(void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleTimerNrc78Timeout();
}

extern "C" void timer_resp_wrapper(void* ctx) {
    auto* handler = static_cast<ZcuUpgrade*>(ctx);
    handler->handleTimerRespTimeout();
}
