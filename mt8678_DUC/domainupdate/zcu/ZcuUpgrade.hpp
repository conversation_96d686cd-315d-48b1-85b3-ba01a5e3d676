#pragma once

#ifdef __cplusplus
extern "C" {
#endif
#include "doip_c.h"
#ifdef __cplusplus
}
#endif

#include <vector>
#include <functional>
#include <mutex>
#include <condition_variable>
#include <shared_mutex>
#include <fstream>
#include <iostream>
#include <thread>
#include <queue>

enum class EcuUpgradeStatus {
    UpgradeStatus_Init = 0,
    UpgradeStatus_Failed,
    UpgradeStatus_Success,

    /* 预编程 */
    Pre_DefaultSession,
    Pre_ReadDidData,
    Pre_ExtendedSession,
    Pre_CheckFlashCondition,
    Pre_DisableDTC,
    Pre_DisableRxTx,
    /* 主编程 */
    Master_ProgrammingSession,
    Master_SecurityAccess,
    Master_WriteDigest,
    /* 刷写flash driver */
    Master_Flash_RequestDownload,
    Master_Flash_DataTransfer,
    Master_Flash_ExitTransfer,
    Master_Flash_SecuritySignCheck,
    /* 刷写app */
    Master_App_EraseFlash,
    Master_App_RequestDownload,
    Master_App_DataTransfer,
    Master_App_ExitTransfer,
    Master_App_SecuritySignCheck,

    Master_DisableAutoSwitchParttion,
    Master_CompatibilityCheck,
    /* 后编程 */
    Later_EnableRxTx,
    Later_RebootEcu,
    Later_Reconnected,
    Later_ClearEcuErrorCode,
    Later_EnableDTC,
    Later_DefaultSession,
    Later_ReadEcuInfo,   
};

class ZcuUpgrade {
public:
    // 服务处理器构造函数
    ZcuUpgrade(std::string ip, uint16_t ecu_ta);
    ~ZcuUpgrade();

    void init(void* channel, FFIServerCallback* callbacks);
    
    void submitWriteTask(std::function<void()> task);
    void waitUpgradeFinish();
    void waitUpgradeFinish(std::chrono::seconds timeout);
    void updateUpgradeStatus(EcuUpgradeStatus status);
    EcuUpgradeStatus getUpgradeStatus();

    bool transferFinish();
    bool transferData(std::vector<unsigned char>& fw);

    // Diagnostic Session Control
    void handleService10h(uint16_t ta, const uint8_t* data, size_t len);
    // ECU Reset
    void handleService11h(uint16_t ta, const uint8_t* data, size_t len);
    // Clear Diagnostic Information
    void handleService14h(uint16_t ta, const uint8_t* data, size_t len);
    // Read DTC Information
    void handleService19h(uint16_t ta, const uint8_t* data, size_t len);
    // Read Data By Identifier
    void handleService22h(uint16_t ta, const uint8_t* data, size_t len);
    // Read Memory By Address
    void handleService23h(uint16_t ta, const uint8_t* data, size_t len);
    // Security Access
    void handleService27h(uint16_t ta, const uint8_t* data, size_t len);
    // Communication Control
    void handleService28h(uint16_t ta, const uint8_t* data, size_t len);
    // Check Programming Dependencies
    void handleService2Ah(uint16_t ta, const uint8_t* data, size_t len);
    // Dynamically Define Data Identifier
    void handleService2Ch(uint16_t ta, const uint8_t* data, size_t len);
    // Write Data By Identifier
    void handleService2Eh(uint16_t ta, const uint8_t* data, size_t len);
    // Input Output Control By Identifier
    void handleService2Fh(uint16_t ta, const uint8_t* data, size_t len);
    // Routine Control
    void handleService31h(uint16_t ta, const uint8_t* data, size_t len);
    // Request Download
    void handleService34h(uint16_t ta, const uint8_t* data, size_t len);
    // Transfer Data
    void handleService36h(uint16_t ta, const uint8_t* data, size_t len);
    // Request Transfer Exit
    void handleService37h(uint16_t ta, const uint8_t* data, size_t len);
    // Request File Transfer
    void handleService38h(uint16_t ta, const uint8_t* data, size_t len);
    // Write Memory By Address
    void handleService3Dh(uint16_t ta, const uint8_t* data, size_t len);
    // Tester Present
    void handleService3Eh(uint16_t ta, const uint8_t* data, size_t len);
    // Control DTC Settings
    void handleService85h(uint16_t ta, const uint8_t* data, size_t len);
    // Authentication
    void handleService29h(uint16_t ta, const uint8_t* data, size_t len); 
    // Negative resp
    void handleService7fh(uint16_t ta, const uint8_t* data, size_t len);

    void handleTimerKeepAlive();
    void handleTimerNrc78Timeout();
    void handleTimerRespTimeout();
private:
    std::mutex mtx_;
    std::shared_mutex us_mtx_;
    const char* ip_;
    const uint16_t TA_;
    const uint16_t func_addr_ = 0x006f;
    void* channel_;
    EcuUpgradeStatus current_status_;

    size_t total_size_;
    size_t block_size_;
    size_t total_blocks_;
    uint16_t current_block_;
    uint8_t block_scnt_; //Block sequence counter
    size_t offset_;
    size_t length_;

    uint32_t flash_addr_;
    std::vector<uint8_t> flash_fw_;
    uint32_t app_addr_;
    std::vector<uint8_t> app_fw_;

    // 线程安全队列：存储待发送的写入任务
    std::queue<std::function<void()>> write_task_queue_;
    std::mutex queue_mutex_;       // 保护队列的互斥锁
    std::condition_variable write_cv_;  // 条件变量，用于通知任务到达
    std::mutex upgrade_mutex_;
    std::condition_variable upgrade_cv_;
    bool running_ = true;               // 控制处理线程的运行状态

    std::thread queue_handle_;

    void process();
};