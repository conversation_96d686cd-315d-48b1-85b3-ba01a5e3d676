#include "ZcuUpgrade.hpp"
#include "file.hpp"
#include "DoipClient.hpp"
#include <iostream>
#include <unistd.h>
#include <future>
#include <chrono>

ZcuUpgrade::ZcuUpgrade(std::string ip, uint16_t ecu_ta):ip_(ip.c_str()), TA_(ecu_ta){
    queue_handle_ = std::thread([this]() {
            process();
        });
}

ZcuUpgrade::~ZcuUpgrade()
{
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        running_ = false;
    }
    write_cv_.notify_one(); // 唤醒处理线程以退出
    if (queue_handle_.joinable()) {
        queue_handle_.join();
    }
}

void ZcuUpgrade::init(void* channel, FFIServerCallback* callbacks){
    channel_ = channel;
    if (DoipClient::asyncConnect(channel_, ip_, callbacks, this) != 0) {
        std::cout << "connect error!" << std::endl;
        return;
    }

    /* 解析固件 */
    if(HexFileParser::parseHexFile("./flashdriver.hex", flash_addr_, flash_fw_) == false)
    {
        std::cout << "固件flash解析失败" << std::endl;
        return;
    }

    // HexFileParser::printHexVector(flash_fw_);

    if(HexFileParser::parseHexFile("./jm30_zcu.hex", app_addr_, app_fw_) == false)
    {
        std::cout << "固件app解析失败" << std::endl;
        return;
    }

    // sleep(10);
    submitWriteTask([this](){
        std::vector<uint8_t> request;
        request = {0x10, 0x01};
        if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
            std::cout << "10 01发送失败!" << std::endl;
        }
        updateUpgradeStatus(EcuUpgradeStatus::Pre_DefaultSession);
    });
}

void ZcuUpgrade::process(){
    std::unique_lock<std::mutex> lock(queue_mutex_);
    while (running_) {
        write_cv_.wait(lock, [this]() {
            return !write_task_queue_.empty() || !running_;
        });

        if (!running_) break;
        while (!write_task_queue_.empty()) {
            auto task = write_task_queue_.front();
            write_task_queue_.pop();
            lock.unlock();
            task();
            lock.lock();
        }
    }
}

void ZcuUpgrade::submitWriteTask(std::function<void()> task) {
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        write_task_queue_.push(std::move(task));
    }
    write_cv_.notify_one();
}

void ZcuUpgrade::updateUpgradeStatus(EcuUpgradeStatus status){
    {
        std::unique_lock<std::shared_mutex> lock(us_mtx_);
        current_status_ = status;
    }

    if (status == EcuUpgradeStatus::UpgradeStatus_Success) {
        std::lock_guard<std::mutex> lock(upgrade_mutex_);
        upgrade_cv_.notify_all();
    }
}

EcuUpgradeStatus ZcuUpgrade::getUpgradeStatus(){
    std::shared_lock<std::shared_mutex> lock(us_mtx_);
    return current_status_;
}

void ZcuUpgrade::waitUpgradeFinish() {
    std::unique_lock<std::mutex> lock(upgrade_mutex_);
    upgrade_cv_.wait(lock, [this]() {
        std::shared_lock<std::shared_mutex> slock(us_mtx_);
        return current_status_ == EcuUpgradeStatus::UpgradeStatus_Success
            || current_status_ == EcuUpgradeStatus::UpgradeStatus_Failed;
    });
}

void ZcuUpgrade::waitUpgradeFinish(std::chrono::seconds timeout){
    std::unique_lock<std::mutex> lock(upgrade_mutex_);
    upgrade_cv_.wait_for(lock, timeout, [this]() {
        std::shared_lock<std::shared_mutex> slock(us_mtx_);
        return current_status_ == EcuUpgradeStatus::UpgradeStatus_Success
                || current_status_ == EcuUpgradeStatus::UpgradeStatus_Failed;
    });
}

void ZcuUpgrade::handleService10h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x10+0x40 && data[1] == 0x01)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Pre_DefaultSession)
        {
            std::cout << "发送10 83" << std::endl;
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x10, 0x83};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_ExtendedSession);

                request = {0x31, 0x01, 0x02, 0x03};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_CheckFlashCondition);
            });
        }
        
    }
    else if(data[0] == 0x10+0x40 && data[1] == 0x02)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_ProgrammingSession)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x27, 0x09};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_SecurityAccess);
            });
        }
    }
    else if(data[0] == 0x10+0x40 && data[1] == 0x03)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Pre_ExtendedSession)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x31, 0x01, 0x02, 0x03};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_CheckFlashCondition);
            });
        }
    }
}

// ECU Reset
void ZcuUpgrade::handleService11h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);
    /* 与ecu重新建立连接 */
    if(data[0] == 0x11+0x40 && data[1] == 0x01){
        if(getUpgradeStatus() == EcuUpgradeStatus::Later_RebootEcu){
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x14, 0xFF, 0xFF, 0xFF};
                while(DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0)
                {
                    sleep(1);
                    std::cout << "发送失败!" << std::endl;
                }
                    
                updateUpgradeStatus(EcuUpgradeStatus::Later_ClearEcuErrorCode);
            });
        }
    }
}
// Clear Diagnostic Information
void ZcuUpgrade::handleService14h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);
    if(data[0] == 0x14+0x40){
        if(getUpgradeStatus() == EcuUpgradeStatus::Later_ClearEcuErrorCode){
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x85, 0x81};
                if (DoipClient::asyncWrite(channel_, ip_, func_addr_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Later_EnableDTC);

                request = {0x10, 0x01};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Later_DefaultSession);

                /* upgrade finish */
                updateUpgradeStatus(EcuUpgradeStatus::UpgradeStatus_Success);
            });
        }
    }
}
// Read DTC Information
void ZcuUpgrade::handleService19h(uint16_t ta, const uint8_t* data, size_t len) {}
// Read Data By Identifier
void ZcuUpgrade::handleService22h(uint16_t ta, const uint8_t* data, size_t len) {}
// Read Memory By Address
void ZcuUpgrade::handleService23h(uint16_t ta, const uint8_t* data, size_t len) {}
// Security Access
void ZcuUpgrade::handleService27h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x27+0x40 && data[1] == 0x09)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_SecurityAccess)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x27, 0x0a, 0x01, 0x02, 0x03, 0x04};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
            });
        }
    }
    else if(data[0] == 0x27+0x40 && data[1] == 0x0a)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_SecurityAccess)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x2e, 0xf1, 0x84,
                    0x00, 0x00, 0x00, 0x00, 0x00,
                    0x00, 0x00, 0x00, 0x00, 0x01,};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_WriteDigest);
            });
        }
    }
}
// Communication Control
void ZcuUpgrade::handleService28h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x28+0x40 && data[1] == 0x03)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Pre_DisableRxTx)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x10, 0x02};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_ProgrammingSession);
            });
        }
    }
    else if(data[0] == 0x28+0x40 && data[1] == 0x00)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Later_EnableRxTx)
        {
            /* ecu reboot */
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x11, 0x01};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(), 1) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Later_RebootEcu);
            });
        }
    }
}
// Check Programming Dependencies
void ZcuUpgrade::handleService2Ah(uint16_t ta, const uint8_t* data, size_t len) {}
// Dynamically Define Data Identifier
void ZcuUpgrade::handleService2Ch(uint16_t ta, const uint8_t* data, size_t len) {}
// Write Data By Identifier
void ZcuUpgrade::handleService2Eh(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x2e + 0x40 && data[1] == 0xf1 && data[2] == 0x84)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_WriteDigest)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x34, 0x00, 0x44,
                            0x70, 0x10, 0x00, 0x00,
                            0x00, 0x01, 0x00, 0x00};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_Flash_RequestDownload);
            });
        }
    }
}
// Input Output Control By Identifier
void ZcuUpgrade::handleService2Fh(uint16_t ta, const uint8_t* data, size_t len) {}
// Routine Control
void ZcuUpgrade::handleService31h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    /* falsh condition check - resp */
    if(data[0] == 0x31+0x40 && data[1] == 0x01 && data[2] == 0x02 && data[3] == 0x03)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Pre_CheckFlashCondition)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x85, 0x82};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_DisableDTC);

                request = {0x28, 0x83, 0x03};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_DisableRxTx);

                request = {0x10, 0x02};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_ProgrammingSession);
            });
        }
    }
    /* security sign - resp */
    else if(data[0] == 0x31+0x40 && data[1] == 0x01 && data[2] == 0xdd && data[3] == 0x02)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_Flash_SecuritySignCheck)
        {
            /* 擦除flash */
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x31, 0x01, 0xff, 0x00,
                                0x80, 0x00 ,0x00, 0x00,
                                0x00, 0xa0, 0x00, 0x00};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_App_EraseFlash);
            });
        }
        else if(getUpgradeStatus() == EcuUpgradeStatus::Master_App_SecuritySignCheck)
        {
            /* 兼容性检测 */
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x31, 0x01, 0xff, 0x01};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_CompatibilityCheck);
            });
        }
    }
    /* erase flash - resp */
    else if(data[0] == 0x31+0x40 && data[1] == 0x01 && data[2] == 0xff && data[3] == 0x00)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_App_EraseFlash)
        {
            /* request download app fw */
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x34, 0x00, 0x44, 
                            0x80, 0x00 ,0x00, 0x00,
                            0x00, 0xa0, 0x00, 0x00};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Master_App_RequestDownload);
            });
        }
    }
    /* 兼容性检测 - resp */
    else if(data[0] == 0x31+0x40 && data[1] == 0x01 && data[2] == 0xff && data[3] == 0x01)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_CompatibilityCheck)
        {
            /* 后编程 */
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x28, 0x80, 0x03};
                if (DoipClient::asyncWrite(channel_, ip_, func_addr_, request.data(), request.size(), 0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Later_EnableRxTx);

                request = {0x11, 0x01};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),1) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Later_RebootEcu);
            });
        }
    }
}
// Request Download
void ZcuUpgrade::handleService34h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x34+0x40 && data[1] == 0x20)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_Flash_RequestDownload)
        {
            total_size_ = flash_fw_.size();
            block_size_ = (static_cast<uint16_t>(data[2]) << 8) | data[3];
            block_size_ -= 2;
            total_blocks_ = (total_size_ + block_size_ - 1) / block_size_;
            block_scnt_ = 1;
            current_block_ = 0;

            submitWriteTask([this](){
                transferData(flash_fw_);
                updateUpgradeStatus(EcuUpgradeStatus::Master_Flash_DataTransfer);
            });
        }
        else if(getUpgradeStatus() == EcuUpgradeStatus::Master_App_RequestDownload)
        {
            total_size_ = app_fw_.size();
            block_size_ = (static_cast<uint16_t>(data[2]) << 8) | data[3];
            block_size_ -= 2;
            total_blocks_ = (total_size_ + block_size_ - 1) / block_size_;
            block_scnt_ = 1;
            current_block_ = 0;

            submitWriteTask([this](){
                transferData(app_fw_);
                updateUpgradeStatus(EcuUpgradeStatus::Master_App_DataTransfer);
            });
        }
    }
}
bool ZcuUpgrade::transferFinish(){
    return current_block_ < total_blocks_ ? false : true;
}

bool ZcuUpgrade::transferData(std::vector<unsigned char>& fw){
    offset_ = current_block_ * block_size_;
    length_ = std::min(block_size_, total_size_ - offset_);

    std::vector<uint8_t> request;
    request.assign(fw.begin() + offset_, fw.begin() + offset_ + length_);
    request.insert(request.begin(), block_scnt_);
    request.insert(request.begin(), 0x36);

    if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
        std::cout << "发送失败!" << std::endl;
        return false;
    }

    return true;
}

// Transfer Data
void ZcuUpgrade::handleService36h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x36+0x40)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_Flash_DataTransfer){
            if(block_scnt_ == data[1]){
                block_scnt_++;
                current_block_++;

                if(transferFinish()){
                    submitWriteTask([this](){
                        std::vector<uint8_t> request;
                        request = {0x37};
                        if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                            std::cout << "发送失败!" << std::endl;
                        }

                        updateUpgradeStatus(EcuUpgradeStatus::Master_Flash_ExitTransfer);
                    });
                }
                else{
                    submitWriteTask([this](){
                        transferData(flash_fw_);
                    });
                }
            }
        }
        else if(getUpgradeStatus() == EcuUpgradeStatus::Master_App_DataTransfer)
        {
            if(block_scnt_ == data[1]){
                block_scnt_++;
                current_block_++;

                if(transferFinish()){
                    submitWriteTask([this](){
                        std::vector<uint8_t> request;
                        request = {0x37};
                        if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                            std::cout << "发送失败!" << std::endl;
                        }

                        updateUpgradeStatus(EcuUpgradeStatus::Master_App_ExitTransfer);
                    });
                }
                else{
                    submitWriteTask([this](){
                        transferData(app_fw_);
                    });
                }
            }
        }
    }
}
// Request Transfer Exit
void ZcuUpgrade::handleService37h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x37+0x40)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Master_Flash_ExitTransfer){  
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x31, 0x01, 0xdd, 0x02,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                std::cout << "发送失败!" << std::endl;
                }

                updateUpgradeStatus(EcuUpgradeStatus::Master_Flash_SecuritySignCheck);
            });
            
        }else if(getUpgradeStatus() == EcuUpgradeStatus::Master_App_ExitTransfer){
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x31, 0x01, 0xdd, 0x02,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                            0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                            0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                            0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                            0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                std::cout << "发送失败!" << std::endl;
            }

            updateUpgradeStatus(EcuUpgradeStatus::Master_App_SecuritySignCheck);
            }); 
        }
    }
}
// Request File Transfer
void ZcuUpgrade::handleService38h(uint16_t ta, const uint8_t* data, size_t len) {}
// Write Memory By Address
void ZcuUpgrade::handleService3Dh(uint16_t ta, const uint8_t* data, size_t len) {}
// Tester Present
void ZcuUpgrade::handleService3Eh(uint16_t ta, const uint8_t* data, size_t len) {}
// Control DTC Settings
void ZcuUpgrade::handleService85h(uint16_t ta, const uint8_t* data, size_t len) {
    std::lock_guard<std::mutex> lock(mtx_);

    if(data[0] == 0x85+0x40 && data[1] == 0x02)
    {
        if(getUpgradeStatus() == EcuUpgradeStatus::Pre_DisableDTC)
        {
            submitWriteTask([this](){
                std::vector<uint8_t> request;
                request = {0x28, 0x03, 0x03};
                if (DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(),0) != 0) {
                    std::cout << "发送失败!" << std::endl;
                }
                updateUpgradeStatus(EcuUpgradeStatus::Pre_DisableRxTx);
            });
        }
    }
}
// Authentication
void ZcuUpgrade::handleService29h(uint16_t ta, const uint8_t* data, size_t len) {}

void ZcuUpgrade::handleService7fh(uint16_t ta, const uint8_t* data, size_t len) {
    std::cout << "negative response :7f" << std::endl;
}

void ZcuUpgrade::handleTimerKeepAlive(){
    std::lock_guard<std::mutex> lock(mtx_);
    submitWriteTask([this](){
        std::vector<uint8_t> request;
        request = {0x3e, 0x80};
        uint8_t ret = DoipClient::asyncWrite(channel_, ip_, TA_, request.data(), request.size(), 2);

        if (ret != 0) {
            std::cout << "发送失败: ret = " << ret << std::endl;
        }
    });
    std::cout << "send 3e service" << std::endl;
}
void ZcuUpgrade::handleTimerNrc78Timeout(){
    std::cout << "ECU pending timeout" << std::endl;
}
void ZcuUpgrade::handleTimerRespTimeout(){
    std::cout << "service resp timeout" << std::endl;
}




