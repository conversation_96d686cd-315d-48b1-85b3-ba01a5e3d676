#include "DomainUpdateService.h"
#include "ZcuUpgrade.hpp"
#include "ffiWrapper.hpp"
#include "DoipClient.hpp"
#include "DUCLogger.h"
#include <thread>

namespace ota::duc {

void DomainUpdateService::zcuUpgrade(){
    duclogger::info(DomainUpdateService::name() + "start zcu upgrade");
    ZcuUpgrade service("************", 0x0064);
    FFIServerCallback callbacks{
        .service_10h_callback = service_10h_wrapper,
        .service_11h_callback = service_11h_wrapper,
        .service_14h_callback = service_14h_wrapper,
        .service_19h_callback = service_19h_wrapper,
        .service_22h_callback = service_22h_wrapper,
        .service_23h_callback = service_23h_wrapper,
        .service_27h_callback = service_27h_wrapper,
        .service_28h_callback = service_28h_wrapper,
        .service_2Ah_callback = service_2Ah_wrapper,
        .service_2Ch_callback = service_2Ch_wrapper,
        .service_2Eh_callback = service_2Eh_wrapper,
        .service_2Fh_callback = service_2Fh_wrapper,
        .service_31h_callback = service_31h_wrapper,
        .service_34h_callback = service_34h_wrapper,
        .service_36h_callback = service_36h_wrapper,
        .service_37h_callback = service_37h_wrapper,
        .service_38h_callback = service_38h_wrapper,
        .service_3Dh_callback = service_3Dh_wrapper,
        .service_3Eh_callback = service_3Eh_wrapper,
        .service_85h_callback = service_85h_wrapper,
        .service_29h_callback = service_29h_wrapper,
        .service_7fh_callback = service_7fh_wrapper,
        // .timer_kepp_alive_callback = timer_keep_alive_wrapper,
        .timer_nrc78_callback = timer_nrc78_wrapper,
        .timer_resp_callback = timer_resp_wrapper,
    };

    void* channel = DoipClient::createOTAChannel(0x0E00, 13400);
    DoipClient::init(channel);
    service.init(channel, &callbacks);
    service.waitUpgradeFinish();
}

}