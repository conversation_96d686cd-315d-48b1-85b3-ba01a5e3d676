#pragma once

#ifdef __cplusplus
extern "C" {
#endif

#include "doip_c.h"

#ifdef __cplusplus
}
#endif

class DoipClient {
public:    
    // 禁止拷贝和赋值
    DoipClient(const DoipClient&) = delete;
    void operator=(const DoipClient&) = delete;

    static int32_t init(void* handle);
    static void* createOTAChannel(uint16_t sa, uint16_t port);
    static void destroyOTAChannel(void* handle);
    static int32_t asyncConnect(void* handle,
                        const char* ip,
                        const FFIServerCallback* c_callback,
                        void* handler_ptr);
    static int32_t asyncWrite(void* handle,
                      const char* ip,
                      uint16_t ta,
                      const unsigned char* data,
                      size_t data_len,
                      uint8_t property);
    static int32_t syncConnect(void* handle, const char* ip, uint16_t port);
    static int32_t syncDisconnect(void* handle, const char* ip);
    static int32_t syncWrite(void* handle,
                     const char* ip,
                     uint16_t ta,
                     const unsigned char* data,
                     size_t data_len);
    static int32_t syncRead(void* handle,
                    const char* ip,
                    unsigned char* buffer,
                    size_t buffer_len,
                    size_t* bytes_read);

private:
    DoipClient() = default;
    ~DoipClient() = default;
};