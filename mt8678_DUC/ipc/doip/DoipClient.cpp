#include "DoipClient.hpp"

#ifdef _LIBDOIP_INCLUDE

int32_t DoipClient::init(void* handle) {
    return doip_init(handle);
}

void* DoipClient::createOTAChannel(uint16_t sa, uint16_t port) {
    return doip_create_ota_channel(sa, port);
}

void DoipClient::destroyOTAChannel(void* handle) {
    doip_destroy_ota_channel(handle);
}

int32_t DoipClient::asyncConnect(void* handle,
                                const char* ip,
                                const FFIServerCallback* c_callback,
                                void* handler_ptr) {
    return doip_connect_async(handle, ip, c_callback, handler_ptr);
}

int32_t DoipClient::asyncWrite(void* handle,
                              const char* ip,
                              uint16_t ta,
                              const unsigned char* data,
                              size_t data_len,
                              uint8_t property) {
    return doip_write_async(handle, ip, ta, data, data_len, property);
}

int32_t DoipClient::syncConnect(void* handle, const char* ip, uint16_t port) {
    return doip_sync_connect(handle, ip, port);
}

int32_t DoipClient::syncDisconnect(void* handle, const char* ip) {
    return doip_sync_disconnect(handle, ip);
}

int32_t DoipClient::syncWrite(void* handle,
                             const char* ip,
                             uint16_t ta,
                             const unsigned char* data,
                             size_t data_len) {
    return doip_sync_write(handle, ip, ta, data, data_len);
}

int32_t DoipClient::syncRead(void* handle,
                            const char* ip,
                            unsigned char* buffer,
                            size_t buffer_len,
                            size_t* bytes_read) {
    return doip_sync_read(handle, ip, buffer, buffer_len, bytes_read);
}

#else

int32_t DoipClient::init(void* handle) { return 0; }

void* DoipClient::createOTAChannel(uint16_t sa, uint16_t port) { return nullptr; }

void DoipClient::destroyOTAChannel(void* handle) {}

int32_t DoipClient::asyncConnect(void* handle,
                                const char* ip,
                                const FFIServerCallback* c_callback,
                                void* handler_ptr) { return 0; }

int32_t DoipClient::asyncWrite(void* handle,
                              const char* ip,
                              uint16_t ta,
                              const unsigned char* data,
                              size_t data_len,
                              uint8_t property) { return 0; }

int32_t DoipClient::syncConnect(void* handle, const char* ip, uint16_t port) { return 0; }

int32_t DoipClient::syncDisconnect(void* handle, const char* ip) { return 0; }

int32_t DoipClient::syncWrite(void* handle,
                             const char* ip,
                             uint16_t ta,
                             const unsigned char* data,
                             size_t data_len) { return 0; }

int32_t DoipClient::syncRead(void* handle,
                            const char* ip,
                            unsigned char* buffer,
                            size_t buffer_len,
                            size_t* bytes_read) { return 0; }

#endif