use std::env;
use std::path::Path;

fn main() {
    let target = env::var("TARGET").unwrap();
    let profile = env::var("PROFILE").unwrap();
    

    let doip_lib_path = format!(
        "../doip/target/{}/{}", 
        target, 
        if profile == "release" { "release" } else { "debug" }
    );

    let lib_file = format!("{}/libdoip.rlib", doip_lib_path);
    assert!(
        Path::new(&lib_file).exists(),
        "\x1b[31merror: {} does not exist\x1b[0m",
        lib_file
    );

    println!("cargo:rustc-link-search=native={}", doip_lib_path);
}