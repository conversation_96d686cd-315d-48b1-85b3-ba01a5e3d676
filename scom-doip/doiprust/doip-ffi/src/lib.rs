use std::ffi::CStr;
use std::os::raw::{c_char, c_void, c_uchar, c_ushort};
use std::sync::{Arc, Mutex};
use std::slice;
use libc::size_t;
use tokio::runtime::{self, Runtime};
use std::sync::OnceLock;
use doip::{
    DoipFactory,
    util::{OtaDataInfo, ServerCallback, DiagnosticMessage, UdsCallback, TimerCallback},
    error_code::{DoipError, ErrorCode},
    doip_src::doip_impl::ota_channel::OtaChannelImpl
};

static GLOBAL_RT: OnceLock<tokio::runtime::Runtime> = OnceLock::new();

/* rust错误码转C++ */
pub fn to_c_error_code(e: &DoipError) -> i32 {
    match e.code {
        ErrorCode::InvalidIpError => 1001,
        ErrorCode::RoutingActivationError => 1002,
        ErrorCode::ConnectClosedError => 1003,
        ErrorCode::WriteHashmapError => 1004,
        ErrorCode::AsyncConnectError => 1005,
        ErrorCode::SendMsgError => 1006,
        _ => -1,
    }
}

struct OpaqueHandle {
    channel: Arc<OtaChannelImpl>,
    // rt: Runtime, //异步调用需要tokio运行时，否则无法运行
}

fn init_global_rt() -> &'static Runtime {
    GLOBAL_RT.get_or_init(|| {
        runtime::Builder::new_multi_thread()
            .enable_all()
            .build()
            .expect("Failed to create Tokio runtime")
    })
}

#[repr(transparent)]
struct SyncSendPtr(*mut c_void);

// 手动实现Send和Sync，需确保指针的线程安全性
unsafe impl Send for SyncSendPtr {}
unsafe impl Sync for SyncSendPtr {}

#[repr(C)]
pub struct FFIServerCallback {
    service_10h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_11h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_14h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_19h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_22h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_23h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_27h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_28h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_2Ah_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_2Ch_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_2Eh_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_2Fh_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_31h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_34h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_36h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_37h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_38h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_3Dh_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_3Eh_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_85h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_29h_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,
    service_7fh_callback: Option<extern "C" fn(c_ushort, *const c_uchar, size_t, *mut c_void)>,

    timer_nrc78_callback: Option<extern "C" fn(*mut c_void)>,
    timer_resp_callback: Option<extern "C" fn(*mut c_void)>,
    // ... 其他服务字段
}

/* ffi接口 */
#[no_mangle]
pub extern "C" fn doip_init(
    handle: *mut c_void,
) -> i32 {
    if handle.is_null(){
        return -1; // EINVAL
    }

    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    handle.channel.init();
    0
}


#[no_mangle]
pub extern "C" fn doip_create_ota_channel(sa: u16, port: u16) -> *mut c_void {
    let rt = init_global_rt();
    let channel = rt.block_on(async {
        DoipFactory::create_ota_channel(sa, port)
    });

    let handle = OpaqueHandle { channel};
    Box::into_raw(Box::new(handle)) as *mut c_void
}

#[no_mangle]
pub extern "C" fn doip_destroy_ota_channel(handle: *mut c_void) {
    if !handle.is_null() {
        unsafe { Box::from_raw(handle as *mut OpaqueHandle) };
    }
}

#[no_mangle]
pub extern "C" fn doip_connect_async(
    handle: *mut c_void, 
    ip: *const c_char, 
    c_callback: *const FFIServerCallback,
    handler_ptr: *mut c_void
) -> i32 {
    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() }; 
    let cb = unsafe { &*c_callback };
    let mut server_cb = ServerCallback::default();
    
    // 宏转换所有服务回调
    macro_rules! convert_server_callback {
        ($service:ident) => {
            if let Some(cb_func) = cb.$service {
                let handler_send = Arc::new(SyncSendPtr(handler_ptr));
                server_cb.$service = Some(Box::new(move |ta: u16, data: Vec<u8>| {
                    // 通过参数传递上下文指针
                    let handler = handler_send.clone();
                    unsafe {
                        cb_func(
                            ta as c_ushort,
                            data.as_ptr() as *const c_uchar,
                            data.len() as size_t,
                            (*Arc::as_ptr(&handler)).0 // 直接从参数获取
                        )
                    }
                }) as UdsCallback);
            }
        };
    }

    convert_server_callback!(service_10h_callback);
    convert_server_callback!(service_11h_callback);
    convert_server_callback!(service_14h_callback);
    convert_server_callback!(service_19h_callback);
    convert_server_callback!(service_22h_callback);
    convert_server_callback!(service_23h_callback);
    convert_server_callback!(service_27h_callback);
    convert_server_callback!(service_28h_callback);
    convert_server_callback!(service_2Ah_callback);
    convert_server_callback!(service_2Ch_callback);
    convert_server_callback!(service_2Eh_callback);
    convert_server_callback!(service_2Fh_callback);
    convert_server_callback!(service_31h_callback);
    convert_server_callback!(service_34h_callback);
    convert_server_callback!(service_36h_callback);
    convert_server_callback!(service_37h_callback);
    convert_server_callback!(service_38h_callback);
    convert_server_callback!(service_3Dh_callback);
    convert_server_callback!(service_3Eh_callback);
    convert_server_callback!(service_85h_callback);
    convert_server_callback!(service_29h_callback);

    macro_rules! convert_timer_callback {
        ($service:ident) => {
            if let Some(cb_func) = cb.$service {
                let handler_send = Arc::new(SyncSendPtr(handler_ptr));
                server_cb.$service = Some(Arc::new(move || {
                    // 通过参数传递上下文指针
                    let handler = handler_send.clone();
                    unsafe {
                        cb_func(
                            (*Arc::as_ptr(&handler)).0 // 直接从参数获取
                        )
                    }
                }) as TimerCallback);
            }
        };
    }

    convert_timer_callback!(timer_nrc78_callback);
    convert_timer_callback!(timer_resp_callback);

    init_global_rt().block_on(async {
        match handle.channel.async_connect(ip_str.to_owned(), server_cb).await {
            Ok(()) => 0,
            Err(e) => to_c_error_code(&e),
        }
    })
}

#[no_mangle]
pub extern "C" fn doip_write_async(
    handle: *mut c_void,
    ip: *const c_char,
    ta: u16,
    data: *const c_uchar,
    data_len: size_t,
    property: u8
) -> i32 {
    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() };
    let data_slice = unsafe { slice::from_raw_parts(data, data_len) };
    
    let info = OtaDataInfo {
        ip: ip_str.to_owned(),
        ta: ta as u16,
        uds_req: data_slice.to_vec(),
        mask: property,
    };
    
    let rt = init_global_rt();
    rt.block_on(async {
        match handle.channel.write_async(info).await {
            Ok(()) => 0,
            Err(e) => to_c_error_code(&e),
        }
    })
}

#[no_mangle]
pub extern "C" fn doip_sync_connect(
    handle: *mut c_void,
    ip: *const c_char,
    port: u16
) -> i32 {
    if handle.is_null() || ip.is_null() {
        return -1;
    }
    
    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() };

    match handle.channel.sync_connect(ip_str.to_owned(), port) {
        Ok(()) => 0,
        Err(e) => to_c_error_code(&e),
    }
}

// 同步断开接口
#[no_mangle]
pub extern "C" fn doip_sync_disconnect(
    handle: *mut c_void,
    ip: *const c_char
) -> i32 {
    if handle.is_null() || ip.is_null() {
        return -1;
    }

    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() };

    match handle.channel.sync_disconnect(ip_str.to_owned()) {
        Ok(()) => 0,
        Err(e) => to_c_error_code(&e),
    }
}

// 同步写入接口
#[no_mangle]
pub extern "C" fn doip_sync_write(
    handle: *mut c_void,
    ip: *const c_char,
    ta: u16,
    data: *const c_uchar,
    data_len: size_t
) -> i32 {
    if handle.is_null() || ip.is_null() || data.is_null() {
        return -1;
    }

    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() };

    let data_slice = unsafe { slice::from_raw_parts(data, data_len) };

    match handle.channel.sync_write(ip_str.to_owned(), ta, data_slice.to_vec()) {
        Ok(()) => 0,
        Err(e) => to_c_error_code(&e),
    }
}

// 同步读取接口
#[no_mangle]
pub extern "C" fn doip_sync_read(
    handle: *mut c_void,
    ip: *const c_char,
    buffer: *mut c_uchar,
    buffer_len: size_t,
    bytes_read: *mut size_t
) -> i32 {
    if handle.is_null() || ip.is_null() || buffer.is_null() || bytes_read.is_null() {
        return -1;
    }

    let handle = unsafe { &*(handle as *const OpaqueHandle) };
    let ip_str = unsafe { CStr::from_ptr(ip).to_str().unwrap() };

    let mut data_buf = DiagnosticMessage::default();
    
    match handle.channel.sync_read(ip_str.to_owned(), &mut data_buf) {
        Ok(n) => {
            // 确保不超过缓冲区长度
            let copy_len = n.min(buffer_len);
            unsafe {
                std::ptr::copy_nonoverlapping(data_buf.data.as_ptr(), buffer, copy_len);
                *bytes_read = copy_len;
            }
            0
        },
        Err(e) => {
            unsafe { *bytes_read = 0; }
            println!("read error: {:?}", e);
            to_c_error_code(&e)
        }
    }
}