/* AUTO-GENERATED by cbindgen */
/* cbindgen --config cbindgen.toml --output include/doip_c.h */
#pragma once
#include <stdint.h>
#include <stdbool.h>


#ifndef DOIP_C_H
#define DOIP_C_H

#include <stdarg.h>
#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>

typedef struct FFIServerCallback {
  void (*service_10h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_11h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_14h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_19h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_22h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_23h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_27h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_28h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_2Ah_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_2Ch_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_2Eh_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_2Fh_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_31h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_34h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_36h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_37h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_38h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_3Dh_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_3Eh_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_85h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_29h_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*service_7fh_callback)(unsigned short, const unsigned char*, size_t, void*);
  void (*timer_nrc78_callback)(void*);
  void (*timer_resp_callback)(void*);
} FFIServerCallback;

int32_t doip_init(void *handle);

void *doip_create_ota_channel(uint16_t sa, uint16_t port);

void doip_destroy_ota_channel(void *handle);

int32_t doip_connect_async(void *handle,
                           const char *ip,
                           const struct FFIServerCallback *c_callback,
                           void *handler_ptr);

int32_t doip_write_async(void *handle,
                         const char *ip,
                         uint16_t ta,
                         const unsigned char *data,
                         size_t data_len,
                         uint8_t property);

int32_t doip_sync_connect(void *handle, const char *ip, uint16_t port);

int32_t doip_sync_disconnect(void *handle, const char *ip);

int32_t doip_sync_write(void *handle,
                        const char *ip,
                        uint16_t ta,
                        const unsigned char *data,
                        size_t data_len);

int32_t doip_sync_read(void *handle,
                       const char *ip,
                       unsigned char *buffer,
                       size_t buffer_len,
                       size_t *bytes_read);

#endif  /* DOIP_C_H */
