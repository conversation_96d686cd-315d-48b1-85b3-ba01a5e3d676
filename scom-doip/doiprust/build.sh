#!/bin/bash
set -e


doip_home=./doip
doip_ffi_home=./doip-ffi
shell_path=`pwd`
install_prefix=`pwd`/install
target=()
toolchain=()


case "$1" in
    x86)
        toolchain="x86_64-unknown-linux-gnu"
        target="target/$toolchain/release"
        install_prefix+="/x86"
        ;;
    aarch64)
        toolchain="aarch64-unknown-linux-gnu"
        target="target/$toolchain/release"
        install_prefix+="/aarch64"
        ;;
    *)
        echo "Invalid platform: $1"
        exit 1
esac

# if [[ ! -d "$install_prefix" ]]; then
#     mkdir -p "$install_prefix"
# fi
if [[ ! -d "$install_prefix/include" ]];then
    mkdir -p "$install_prefix/include"
fi
if [[ ! -d "$install_prefix/lib" ]];then
    mkdir -p "$install_prefix/lib"
fi


build_doip(){
    cd $shell_path
    cd $doip_home

    #默认编译release版本
    cargo clean
    cargo build --target $1 --release
}

build_doip_ffi(){
    cd $shell_path
    cd $doip_ffi_home

    #默认编译release版本
    cargo clean
    cargo build --target $1 --release
}

clean_project(){
    rm -f $install_prefix/include/*
    rm -f $install_prefix/lib/*
}

install(){
    cd $shell_path
    cp $doip_ffi_home/$target/libdoipffi.so $install_prefix/lib
    cp $doip_ffi_home/include/* $install_prefix/include
}

clean_project
build_doip $toolchain
build_doip_ffi $toolchain
install
