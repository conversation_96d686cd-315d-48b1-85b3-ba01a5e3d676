use doip::{DoipFactory, util::OtaDataInfo};
use std::time::Duration;
use doip::log::LogLevel;
use doip::log;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 配置参数
    const SA: u16 = 0x0E00;
    const TA: u16 = 0x0064;
    const PORT: u16 = 13400;
    const SERVER_IP: &str = "127.0.0.1";

    let _ = log::init(LogLevel::Info);

    // 创建通道实例
    let ota_channel = DoipFactory::create_ota_channel(SA, PORT);

    // 正确注册回调（满足 Send + Sync）
    // ota_channel.register_callback(Box::new(|data: Vec<u8>| {
    //     println!("\n=== 收到诊断响应 ===");
    //     println!("原始数据 ({}字节):", data.len());
    //     println!("HEX: {:02X?}", data);
    // }) as Box<dyn Fn(Vec<u8>) + Send + Sync>);

    // 连接服务端
    // println!("[1/3] 连接到服务端...");
    // ota_channel.async_connect(SERVER_IP.to_string())
    //     .await
    //     .expect("连接失败");
    // println!("✅ 连接成功");

    // 发送诊断请求
    // println!("[2/3] 发送请求...");
    // let req = OtaDataInfo {
    //     ip: SERVER_IP.to_string(),
    //     ta: TA,
    //     uds_req: vec![0x10, 0x01], // 示例：读取数据
    //     uds_resp: Vec::new()
    // };
    // ota_channel.write_async(req).await?;
    // println!("✅ 请求已发送");

    // 保持运行接收回调
    // println!("[3/3] 等待响应（15秒）...");
    // tokio::time::sleep(Duration::from_secs(15)).await;

    Ok(())
}