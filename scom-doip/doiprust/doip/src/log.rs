use std::sync::atomic::{AtomicUsize, Ordering};
use chrono::{Local, Timelike, Utc};

// 定义日志等级枚举（包含 Off 级别）
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
pub enum LogLevel {
    Off = 0,
    Error = 1,
    Warn = 2,
    Info = 3,
    Debug = 4,
}

// ANSI 颜色代码
pub const RED: &str = "\x1b[31m";
pub const YELLOW: &str = "\x1b[33m";
pub const GREEN: &str = "\x1b[32m";
pub const CYAN: &str = "\x1b[36m";
pub const RESET: &str = "\x1b[0m";

// 全局日志等级（默认关闭）
static LOG_LEVEL: AtomicUsize = AtomicUsize::new(LogLevel::Info as usize);

/// 初始化日志系统
pub fn init(level: LogLevel) -> Result<(), String> {
    let val = level as usize;
    if val > LogLevel::Debug as usize {
        return Err("Invalid log level".into());
    }
    LOG_LEVEL.store(val, Ordering::Relaxed);
    Ok(())
}

pub fn format_timestamp() -> String {
    let now = Utc::now();
    format!(
        "{:02}-{:02}-{:02}:{:03}",
        now.hour(),
        now.minute(),
        now.second(),
        now.nanosecond() / 1_000_000 // 转换为毫秒
    )
}

/// 获取当前日志等级
pub fn get_log_level() -> Option<LogLevel> {
    match LOG_LEVEL.load(Ordering::Relaxed) {
        0 => Some(LogLevel::Off),
        1 => Some(LogLevel::Error),
        2 => Some(LogLevel::Warn),
        3 => Some(LogLevel::Info),
        4 => Some(LogLevel::Debug),
        _ => None,
    }
}

// 私有方法：检查是否应该记录
pub fn should_log(level: LogLevel) -> bool {
    let current = LOG_LEVEL.load(Ordering::Relaxed);
    match current {
        0 => false,
        _ => level as usize <= current
    }
}

// 私有方法：带颜色输出
pub fn colored_print(color: &str, message: &str) {
    println!("{}{}{}", color, message, RESET);
}

pub fn bytes_to_hex_multiline<T: AsRef<[u8]>>(data: T) -> String {
    let bytes = data.as_ref();
    let mut output = String::with_capacity(bytes.len() * 5);
    
    for chunk in bytes.chunks(16) {
        let split_point = 8.min(chunk.len());
        let (first_half, second_half) = chunk.split_at(split_point);

        let hex_first = first_half.iter()
            .map(|b| format!("{:02X}", b))
            .collect::<Vec<_>>()
            .join(" ");

        let hex_second = second_half.iter()
            .map(|b| format!("{:02X}", b))
            .collect::<Vec<_>>()
            .join(" ");

        // 仅当两部分都有内容时才添加分隔符
        let line = if !hex_second.is_empty() {
            format!("{} - {}", hex_first, hex_second)
        } else {
            hex_first
        };

        output.push_str(&line);
        output.push('\n');
    }
    
    // 移除最后的多余换行
    if !bytes.is_empty() {
        output.pop(); 
    }
    
    output
}

#[macro_export]
macro_rules! filename {
    () => {{
        const FILE_PATH: &str = file!();
        FILE_PATH.split('/').last().unwrap_or(FILE_PATH)
    }};
}

// 日志宏实现
#[macro_export]
macro_rules! log_e {
    ($($arg:tt)*) => {{
        if $crate::log::should_log($crate::log::LogLevel::Error) {
            let timestamp = $crate::log::format_timestamp();
            let message = format!("[{}]-[{}:{}] : {}", timestamp, $crate::filename!(), line!(), format!($($arg)*));
            $crate::log::colored_print($crate::log::RED, &message);
        }
    }};
}

#[macro_export]
macro_rules! log_w {
    ($($arg:tt)*) => {{
        if $crate::log::should_log($crate::log::LogLevel::Warn) {
            let timestamp = $crate::log::format_timestamp();
            let message = format!("[{}]-[{}:{}] : {}", timestamp, $crate::filename!(), line!(), format!($($arg)*));
            $crate::log::colored_print($crate::log::YELLOW, &message);
        }
    }};
}

#[macro_export]
macro_rules! log_i {
    ($($arg:tt)*) => {{
        if $crate::log::should_log($crate::log::LogLevel::Info) {
            let timestamp = $crate::log::format_timestamp();
            let message = format!("[{}]-[{}:{}] : {}", timestamp, $crate::filename!(), line!(), format!($($arg)*));
            $crate::log::colored_print($crate::log::GREEN, &message);
        }
    }};
}

#[macro_export]
macro_rules! log_d {
    ($($arg:tt)*) => {{
        if $crate::log::should_log($crate::log::LogLevel::Debug) {
            let timestamp = $crate::log::format_timestamp();
            let message = format!("[{}]-[{}:{}] : {}", timestamp, $crate::filename!(), line!(), format!($($arg)*));
            $crate::log::colored_print($crate::log::CYAN, &message);
        }
    }};
}

#[macro_export]
macro_rules! log_hex {
    ($level:ident, $label:expr, $data:expr) => {{
        if $crate::log::should_log($crate::log::LogLevel::$level) {
            use $crate::log::{bytes_to_hex_multiline, colored_print};
            let hex_str = bytes_to_hex_multiline($data);
            let color = $crate::color_for_level!($level);
            let timestamp = $crate::log::format_timestamp();

            // 构建前缀字符串
            let prefix = format!(
                "[{}]-[{}:{}] {}:",    // 注意这里保留冒号
                timestamp,
                $crate::filename!(),
                line!(),
                $label
            );
            let prefix_len = prefix.chars().count();  // 使用字符计数确保对齐准确
            
            let message = if hex_str.is_empty() {
                format!("{} <EMPTY>", prefix)
            } else {
                // 分割并过滤空行
                let hex_lines: Vec<&str> = hex_str.lines()
                    .filter(|s| !s.trim().is_empty())
                    .collect();
                
                if hex_lines.is_empty() {
                    prefix // 理论上不会出现这种情况
                } else {
                    // 构造首行
                    let mut formatted = format!("{} {}", prefix, hex_lines[0]);
                    
                    // 生成对齐空格字符串
                    let indent = " ".repeat(prefix_len + 1);  // +1 用于冒号后的空格
                    
                    // 处理后续行
                    for line in hex_lines.iter().skip(1) {
                        formatted.push_str(&format!("\n{}{}", indent, line));
                    }
                    
                    // 处理原始字符串最后的换行
                    if hex_str.ends_with('\n') {
                        formatted.push('\n');
                    }
                    
                    formatted
                }
            };

            // 带颜色输出完整消息块
            colored_print(color, &message);
        }
    }};
}

// 颜色匹配宏
#[macro_export]
macro_rules! color_for_level {
    (Error) => { $crate::log::RED };
    (Warn) => { $crate::log::YELLOW };
    (Info) => { $crate::log::GREEN };
    (Debug) => { $crate::log::CYAN };
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::sync::atomic::Ordering;

    // 测试辅助函数：重置日志状态
    fn reset_logger() {
        LOG_LEVEL.store(LogLevel::Off as usize, Ordering::Relaxed);
    }

    #[test]
    fn test_default_level_is_off() {
        reset_logger();
        assert_eq!(get_log_level(), Some(LogLevel::Off));
        log_e!("Should not print");
        log_w!("Should not print");
    }

    #[test]
    fn test_level_filtering() {
        reset_logger();
        
        // 测试 Info 级别
        init(LogLevel::Info).unwrap();
        log_e!("Visible");    // 应显示
        log_w!("Visible");    // 应显示
        log_i!("Visible");    // 应显示
        log_d!("Invisible");  // 不应显示
        
        // 验证切换为 Debug
        init(LogLevel::Debug).unwrap();
        log_d!("Now visible"); // 应显示
    }

    #[test]
    fn test_off_level() {
        reset_logger();
        
        // 明确设置 Off 级别
        init(LogLevel::Off).unwrap();
        log_e!("Invisible");
        log_w!("Invisible");
        assert_eq!(LOG_LEVEL.load(Ordering::Relaxed), 0);
    }

    #[test]
    fn test_hex_logging() {
        init(LogLevel::Debug).unwrap();
        let data = vec![0u8; 19];
        
        log_hex!(Debug, "data", &data);
    }
}