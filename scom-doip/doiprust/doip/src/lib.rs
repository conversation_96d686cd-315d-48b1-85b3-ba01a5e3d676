pub mod doip_src;
pub mod error_code;
pub mod util;
pub mod log;
use std::sync::Arc;
use crate::doip_src::doip_impl::ota_channel::OtaChannelImpl;


pub struct DoipFactory;

impl DoipFactory {
    pub fn create_ota_channel(sa: u16, prot: u16) -> Arc<OtaChannelImpl>{
        OtaChannelImpl::new(sa, prot)
    }
}

// 调试模式专用宏
#[macro_export]
#[cfg(debug_assertions)]
macro_rules! debug_hexdump {
    ($buffer:expr) => {{
        println!("[DEBUG] Received {} bytes:", $buffer.len());
        for (i, b) in $buffer.iter().enumerate() {
            if i % 16 == 0 {
                print!("\n{:04x}: ", i);
            }
            print!("{:02x} ", b);
        }
        println!("\n");
    }};
}

// 非调试模式空实现
#[macro_export]
#[cfg(not(debug_assertions))]
macro_rules! debug_hexdump {
    ($buffer:expr) => {{}};
}
