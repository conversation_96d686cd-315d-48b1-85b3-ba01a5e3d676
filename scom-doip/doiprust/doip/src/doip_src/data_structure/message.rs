use num_enum::TryFromPrimitive;
use crate::util::Cid;

#[derive(TryFromPrimitive)]
#[repr(u16)]
pub enum DoipMsgType{
    
    HeaderNegativeAck = 0x0000,
    VehicleIdentRequest,
    VehicleIdentRequestEid,
    VehicleIdentRequestVin,
    VehicleIdentResponse,
    RoutingActivationRequest,
    RoutingActivationResponse,
    AliveCheckRequest,
    AliveCheckResponse,
    DoipEntityStatusRequest = 0x4001,
    DoipEntityStatusResponse,
    DiagPmInfoRequest,
    DiagPmInfoResponse,
    DiagnosticMessage = 0x8001,
    DiagnosticPositiveAck,
    DiagnosticNegativeAck,
}

#[derive(Default)]
pub struct DoipHeader {
    pub protocol_version: u8,
    pub inverse_version: u8,
    pub payload_type: u16,
    pub payload_length: u32,
}

#[derive(Default)]
pub struct DoipMessage {
    pub header: DoipHeader,
    pub payload: Vec<u8>,
}

#[derive(Debug, <PERSON>lone)]
pub struct RoutingActivationRequest {
    pub sa: u16,
    pub activation_type: u8,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct EventInfo {
    pub cid: Cid,
    /* reserve */
}
impl EventInfo {
    // 公共构造函数
    pub fn new(cid: Cid) -> Self {
        Self { cid }
    }
}