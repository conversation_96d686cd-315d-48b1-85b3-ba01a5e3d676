use num_enum::TryFromPrimitive;

/* DoIP 诊断报文否定响应的错误码 */
#[derive(PartialEq, Debug, Clone, Copy)]
pub(crate) enum DoipDiagErrorCode {
    Success = 0x00,
    InvalidSourceAddress = 0x02,
    UnknownTargetAddress = 0x03,
    DiagnosticMessageTooLarge = 0x04,
    OutOfMemory = 0x05,
    TargetUnreachable = 0x06,
    UnknownNetwork = 0x07,
    TransportProtocolError = 0x08,
    /* 自定义错误 */
    UdsMsgDelaySend = 0x09,
    InvalidMsgLength,
}

impl DoipDiagErrorCode {
    pub(crate) fn description(&self) -> &'static str {
        match self {
            DoipDiagErrorCode::Success => "No error",
            DoipDiagErrorCode::InvalidSourceAddress => "Invalid source address 无效源地址",
            DoipDiagErrorCode::UnknownTargetAddress => "Unknown target address 未知目标地址",
            DoipDiagErrorCode::DiagnosticMessageTooLarge => "Diagnostic message too large 诊断报文过长",
            DoipDiagErrorCode::OutOfMemory => "Out of memory 内存不足",
            DoipDiagErrorCode::TargetUnreachable => "Target unreachable 目标不可达",
            DoipDiagErrorCode::UnknownNetwork => "Unknown network 未知的网络",
            DoipDiagErrorCode::TransportProtocolError => "Transport protocol error 传输协议错误",
            DoipDiagErrorCode::UdsMsgDelaySend => "ecu未准备好,延时发送",
            DoipDiagErrorCode::InvalidMsgLength => "uds 报文长度不匹配"
        }
    }
}

/* Doip 头部否定响应码 */
pub mod head_negative_code {
     // 协议版本不匹配
     pub(crate) const INCORRECT_PATTERN_FORMAT: u8 = 0x00;
     // 未知的负载类型
     pub(crate) const UNKNOWN_PAYLOAD_TYPE: u8 = 0x01;
     // 报文长度过长
     pub(crate) const MESSAGE_TOO_LARGE: u8 = 0x02;
     // 内存不足
     pub(crate) const OUT_OF_MEMORY: u8 = 0x03;
     // 无效的负载长度
     pub(crate) const INVALID_PAYLOAD_LENGTH: u8 = 0x04;
}

/* DoIP 路由激活报文响应码 */
pub mod routing_activation_code {
    // 未知源地址错误码
    pub(crate) const UNKNOWN_SOURCE_ADDRESS: u8 = 0x00;
    // 所有套接字已激活错误码
    pub(crate) const ALL_SOCKETS_ACTIVE: u8 = 0x01;
    // 收到不同 SA 错误码（SA: Security Association）
    pub(crate) const DIFFERENT_SA_RECEIVED: u8 = 0x02;
    // SA 已在其他位置注册错误码
    pub(crate) const SA_REGISTERED_ELSEWHERE: u8 = 0x03;
    // 缺少认证错误码
    pub(crate) const MISSING_AUTHENTICATION: u8 = 0x04;
    // 确认被拒绝错误码
    pub(crate) const REJECTED_CONFIRMATION: u8 = 0x05;
    // 不支持的路由类型错误码
    pub(crate) const UNSUPPORTED_ROUTING_TYPE: u8 = 0x06;
    // 路由成功状态码
    pub(crate) const ROUTING_SUCCESS: u8 = 0x10;
    // 需要确认状态码
    pub(crate) const CONFIRMATION_REQUIRED: u8 = 0x11;
}


/* DoIP 解析错误码 */
#[derive(Debug, Clone, Copy, PartialEq)]
pub(crate) enum DoipParseErrorCode {
    Success = 0x00,
    IllegalData,
    PayloadUnmatch,
}

impl DoipParseErrorCode {
    pub(crate) fn description(&self) -> &'static str {
        match self {
            DoipParseErrorCode::Success => "No error",
            DoipParseErrorCode::IllegalData => "Illegal data",
            DoipParseErrorCode::PayloadUnmatch => "Payload unmatched",
        }
    }
}

/* 数据转换错误码 */
#[derive(Debug, Clone, Copy)]
pub(crate) enum ConvErrorCode {
    Success = 0,
    ShortDoipHeader,
    DoipPayloadLenUnmatch,
    EmptyDoipPayload,
    ShortUdsPayload,
}

impl ConvErrorCode {
    pub(crate) fn description(&self) -> &'static str {
        match self {
            ConvErrorCode::Success => "No error",
            ConvErrorCode::ShortDoipHeader => "Short DoIP header",
            ConvErrorCode::DoipPayloadLenUnmatch => "DoIP payload length unmatched",
            ConvErrorCode::EmptyDoipPayload => "Empty DoIP payload",
            ConvErrorCode::ShortUdsPayload => "Short UDS payload",
        }
    }
}


