use crate::doip_src::doip_impl::ota_channel::{OtaChannelImpl, Result};
use crate::util::{Cid, DiagnosticMessage, ListenerEventType};
use crate::doip_src::data_structure::message::{DoipMessage, DoipMsgType, EventInfo};
use crate::doip_src::data_structure::doip_error_code::{routing_activation_code, 
                                                        head_negative_code, 
                                                        DoipParseErrorCode, 
                                                        DoipDiagErrorCode};
use std::convert::TryFrom;
use std::sync::Arc;
use crate::doip_src::infrastruture::async_io_manager::AsyncIoManager;
use crate::doip_src::infrastruture::general::{map_get_value, map_contains_value};
use crate::{log_e,log_i,log_d,log_hex};

use super::general::map_remove_by_value;

pub(crate) struct DoipManager{
    pub sa_ : u16
}

impl DoipManager {
    pub(crate) fn new(sa: u16) -> Arc<Self>{
        Arc::new(Self{
            sa_ : sa
        })
    }

    /* 构造doip报文数据 */
    pub(crate) fn create_diag_request(&self, ta: u16, uds_msg: Vec<u8>) -> Vec<u8>{
        let mut req = Vec::with_capacity(12 + uds_msg.len());

        req.extend_from_slice(&[0x02, 0xfd, 0x80, 0x01]);
        
        /* payload_len = SA + TA + uds_msg */
        let payload_len = 2 + 2 + uds_msg.len() as u32;
        req.extend_from_slice(&payload_len.to_be_bytes());

        /* payload */
        req.push((self.sa_ >> 8) as u8);
        req.push((self.sa_ & 0xff) as u8);
        req.push((ta >> 8) as u8);
        req.push((ta & 0xff) as u8);
        req.extend(&uds_msg);

        req
    }

    pub(crate) fn create_routing_activation_request(&self) -> Vec<u8>{
        let mut req = Vec::new();

        /* doip ra head */
        req.extend_from_slice(&[0x02, 0xfd, 0x00, 0x05, 0x00, 0x00, 0x00, 0x07]);
        
        /* payload */
        req.push((self.sa_ >> 8) as u8);
        req.push((self.sa_ & 0xff) as u8);
        req.push(0x00); // Activation-Type
        req.extend_from_slice(&[0x00, 0x00, 0x00, 0x00]);

        req
    }

    pub(crate) fn check_routing_activation_resp(ra_type: u8) -> bool{
        match ra_type {
            routing_activation_code::ROUTING_SUCCESS => {
                true
            }
            _ => {
                false
            }
        }
    }

    async fn doip_head_nack(&self, channel: &OtaChannelImpl, cid: Cid, payload: Vec<u8>)-> std::io::Result<()>{
        let nack_code = payload.get(0);
        match nack_code {
            Some(&head_negative_code::INCORRECT_PATTERN_FORMAT) => {
                self.close_socket(channel,cid).await
            }
            Some(&head_negative_code::UNKNOWN_PAYLOAD_TYPE) => {
                /* Discard DoIP message */
            }
            Some(&head_negative_code::MESSAGE_TOO_LARGE) => {
                /* Discard DoIP message */
            }
            Some(&head_negative_code::OUT_OF_MEMORY) => {
                /* Discard DoIP message */
            }
            Some(&head_negative_code::INVALID_PAYLOAD_LENGTH) => {
                self.close_socket(channel,cid).await
            }

            Some(_) => {
                log_e!("undefine doip head negative NACK code:{:?}",nack_code)
            }

            None => {
                log_e!("input is empty")
            }
        }

        Ok(())
    }

    async fn close_socket(&self, channel: &OtaChannelImpl, cid: Cid){
        if let Ok(b) = map_contains_value(&channel.get_async_ip_cid_map(), &cid) {
            if b {
                let _ = channel.async_io.close_connector(cid).await;
                let _ = map_remove_by_value(&channel.get_async_ip_cid_map(), &cid);
            }
        }
    }

    async fn doip_routing_activation(&self, channel: &OtaChannelImpl, cid: Cid, payload: Vec<u8>)-> std::io::Result<()>{
        let _extend_addr = ((payload[0] as u16) << 8) | (payload[1] as u16);
        let _entity_addr = ((payload[2] as u16) << 8) | (payload[3] as u16);
        let code = payload[4];
        /* 默认不解析 ReservedbyISO13400 && Reservedfor OEM*/

        match code {
            routing_activation_code::ROUTING_SUCCESS => {
                log_i!("routing activation success");
                channel.async_io.set_activate_status(cid, true).await;
                return Ok(())
            }
            routing_activation_code::CONFIRMATION_REQUIRED => {
                /* 向车辆发送激活确认请求 */
                return Ok(())
            }
            routing_activation_code::UNKNOWN_SOURCE_ADDRESS => {
                /* 不激活路由，关闭套接字 */
                self.close_socket(channel,cid).await;
            }
            routing_activation_code::ALL_SOCKETS_ACTIVE => {
                self.close_socket(channel,cid).await;
            }
            routing_activation_code::DIFFERENT_SA_RECEIVED => {
                self.close_socket(channel,cid).await;
            }
            routing_activation_code::SA_REGISTERED_ELSEWHERE => {
                self.close_socket(channel,cid).await;
            }
            routing_activation_code::MISSING_AUTHENTICATION => {
                /* 不激活路由和注册 */
            }

            routing_activation_code::REJECTED_CONFIRMATION => {
                self.close_socket(channel,cid).await;
            }
            routing_activation_code::UNSUPPORTED_ROUTING_TYPE => {
                /* 激活路由，关闭套接字 */
                self.close_socket(channel,cid).await;

                return Ok(())
            }
            _ => {

            }
        }

        log_i!("routing activation response code : {}", code);
        return Err(std::io::Error::new(std::io::ErrorKind::Other, format!("routing activation failed, code : {}", code)))
    }


    /**
     * 根据doip报文类型作相应的报文响应
      */
    pub async fn doip_recv_process(&self, channel: &OtaChannelImpl, cid: Cid, data: Vec<u8>){
        let mut doip_msg = DoipMessage::default();
        let error_code = self.doip_msg_parse(data, &mut doip_msg);
        match error_code {
            DoipParseErrorCode::Success =>{
                log_d!("parse doip msg success");
            }
            _ => {
                log_e!("parse doip msg error");
                channel.on_event(ListenerEventType::InvalidDoipMsg, EventInfo::default()).await;
            }
        }

        match  DoipMsgType::try_from(doip_msg.header.payload_type){
            Ok(msg_type) => match msg_type{
                DoipMsgType::HeaderNegativeAck => {
                    let _ret = self.doip_head_nack(channel, cid, doip_msg.payload).await;
                }

                DoipMsgType::RoutingActivationResponse =>{
                    log_i!("recv routing activation resp");
                    let _ret = self.doip_routing_activation(channel, cid, doip_msg.payload).await;
                    
                    /* let ra_code = RoutingActivationErrorCode::try_from(doip_msg.payload[4]);
                    match  ra_code{
                        Ok(RoutingActivationErrorCode::RoutingSuccess) => {
                            channel.async_io.set_activate_status(cid, true).await;
                        }

                        _ => {
                            log_e!("routing activation failed : activation type undefine, recv code is {}",doip_msg.payload[4]);
                        }
                    } */
                }

                DoipMsgType::AliveCheckResponse => {
                    self.send_alive_check_resp(&channel.async_io, cid);
                }

                DoipMsgType::DiagnosticMessage => {
                    let mut uds_msg: DiagnosticMessage = DiagnosticMessage::default();
                    let ret = self.diag_uds_parse(doip_msg.payload, &mut uds_msg);
                    if ret == DoipDiagErrorCode::Success {
                        log_d!("解析uds报文成功");
                        let entity_lock = map_get_value(&channel.get_async_cid_uds_map(), &cid);
                        match entity_lock {
                            Ok(entity_lock) => {
                                tokio::spawn(async move {
                                    let entity = entity_lock.lock().await;
                                    let _ = entity.uds_msg_handle(uds_msg).await;
                                });
                            }
                            Err(e) => {
                                log_e!("not found cid:{} in map", cid);
                            }
                        }
                        
                    }
                }

    
                _ => {
                    // println!("error");
                }
            }
            Err(e) => {
                /* payload_type转枚举失败 */
            }
        }
    }

    /**
     *  解析收到的doip报文（head + payload）
    */
    pub(crate) fn doip_msg_parse(&self, mut msg: Vec<u8>, doip_msg : &mut DoipMessage) -> DoipParseErrorCode {
        const GENERIC_HEADER_LEN: usize = 8;

        let mut index: usize= 0;

        if msg.len() < GENERIC_HEADER_LEN
        {
            return DoipParseErrorCode::IllegalData;
        }
        
        /* 解析head */
        doip_msg.header.protocol_version = msg[index];
        index += 1;
        doip_msg.header.inverse_version = msg[index];
        index += 1;

        let payload_type_bytes = &msg[index..index+2];
        doip_msg.header.payload_type = u16::from_be_bytes([payload_type_bytes[0],payload_type_bytes[1]]);
        index += 2;

        let payload_len_bytes = &msg[index..index+4];
        doip_msg.header.payload_length = u32::from_be_bytes([
            payload_len_bytes[0],
            payload_len_bytes[1],
            payload_len_bytes[2],
            payload_len_bytes[3],
        ]);
        index += 4;

        /* 验证报文长度 */
        if msg.len() != GENERIC_HEADER_LEN + doip_msg.header.payload_length as usize {
            return DoipParseErrorCode::PayloadUnmatch;
        }

        /* 解析doip payload */
        doip_msg.payload.extend(msg.split_off(GENERIC_HEADER_LEN));

        DoipParseErrorCode::Success

    }

    /**
     * 解析doip报文的诊断报文
      */
    pub(crate) fn diag_uds_parse(&self, mut msg : Vec<u8>, uds_resp: &mut DiagnosticMessage) -> DoipDiagErrorCode{
        if msg.len() < 4 {
            return DoipDiagErrorCode::InvalidMsgLength;
        }

        let sa = u16::from_be_bytes([msg[0],msg[1]]);
        let ta = u16::from_be_bytes([msg[2],msg[3]]);

        let ret = self.check_diag_msg(sa, ta);
        if ret == DoipDiagErrorCode::Success {
            let payload = msg.split_off(4);
            let _ = std::mem::replace(&mut uds_resp.data, payload);
        }else {
            return ret;
        }

        DoipDiagErrorCode::Success
    }

    /**
     * 校验诊断报文合法性
      */
    fn check_diag_msg(&self, sa: u16, ta: u16) -> DoipDiagErrorCode{ 
        if ta != self.sa_ {
            return DoipDiagErrorCode::InvalidSourceAddress;
        }

        if Self::check_target_addr(sa) == false {
            return DoipDiagErrorCode::UnknownTargetAddress;
        }
        DoipDiagErrorCode::Success
    }

    fn check_target_addr(ta: u16) -> bool {
        true
    }

    /* 构造doip报文数据 */
    fn create_alive_resp(&self) -> Vec<u8>{
        let mut resp = Vec::new();

        resp.extend_from_slice(&[0x02, 0xfd, 0x00, 0x08, 0x00, 0x00, 0x00, 0x02]);
        resp.push((self.sa_ >> 8) as u8);
        resp.push((self.sa_ & 0xff) as u8);

        resp
    }

    fn create_doip_entity_status_req() -> Vec<u8>{
        let mut resp = Vec::new();

        resp.extend_from_slice(&[0x02, 0xfd, 0x40, 0x01, 0x00, 0x00, 0x00, 0x00]);
        resp
    }

    fn create_power_mode_req() -> Vec<u8>{
        let mut resp = Vec::new();

        resp.extend_from_slice(&[0x02, 0xfd, 0x40, 0x03, 0x00, 0x00, 0x00, 0x00]);
        resp
    }

    fn send_alive_check_resp(&self, io_manager: &Arc<AsyncIoManager>, cid: Cid){
        let resp = self.create_alive_resp();

        /* io_manager.clone()的实例应该共享同一个读写锁 */
        let io_manager_clone = io_manager.clone();
        tokio::task::spawn(async move{
            let result = io_manager_clone.async_write(cid, resp).await;
            match result {
                Ok(()) => {
                    /* do nothing */
                }

                Err(e) => {
                    eprintln!("send_alive_check_resp failed : {}", e);
                }
            }
        });
    }
        
}