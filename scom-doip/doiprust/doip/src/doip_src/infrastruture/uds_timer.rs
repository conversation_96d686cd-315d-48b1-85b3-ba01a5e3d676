use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::{<PERSON>te<PERSON>, RwLock};
use tokio::time::{Duration, Instant};
use std::io::{Result, Error, <PERSON><PERSON><PERSON><PERSON><PERSON>};
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use tokio_util::sync::CancellationToken;
use crate::doip_src::doip_impl::ota_channel::OtaChannelImpl;
use crate::util::{Cid, TimerCallback};
use crate::{log_d, log_e, log_i, log_w};

use super::async_io_manager::AsyncIoManager;

#[derive(Debug, Clone)]
pub struct TimeoutConfig {
    pub normal_timeout: Duration,
    pub nrc78_timeout: Duration,
    pub physical_interval: Duration,
    pub functional_interval: Duration,
    pub keep_alive_interval: Duration,
}

struct Timer {
    uds_resp_timer: Mutex<Option<tokio::task::Join<PERSON><PERSON><PERSON><()>>>,
    keep_alive_timer: Mutex<Option<(Jo<PERSON><PERSON><PERSON><PERSON><()>, CancellationToken)>>,
}

struct TimeStamp {
    last_physical: AtomicU64,
    last_functional: AtomicU64,
}

pub struct TimerManager {
    config: RwLock<TimeoutConfig>,
    state: TimeStamp,
    timer_list: Timer,
}

impl TimerManager {
    pub fn new() -> Self {
        Self {
            config: RwLock::new(TimeoutConfig{
                normal_timeout: Duration::from_millis(2000),
                nrc78_timeout: Duration::from_millis(7000),
                physical_interval: Duration::from_millis(50),
                functional_interval: Duration::from_millis(50),
                keep_alive_interval: Duration::from_millis(2000),
            }),
            state: TimeStamp {
                last_physical: AtomicU64::new(0),
                last_functional: AtomicU64::new(0),
            },
            timer_list: Timer{
                uds_resp_timer: Mutex::new(None),
                #[allow(dead_code)]
                keep_alive_timer:  Mutex::new(
                    None::<(JoinHandle<()>, CancellationToken)>
                ),
            }
        }
    }

    pub async fn check_interval(&self, is_functional: bool) -> Result<()> {
        let state = &self.state;
        let config = self.config.read().await;
    
        /* get current time(ms) */
        let now = Self::current_millis();
        
        /* get the last timestamp */
        let last_timestamp = if is_functional {
            state.last_functional.load(Ordering::Acquire)
        } else {
            state.last_physical.load(Ordering::Acquire)
        };
    
        /* first */
        if last_timestamp == 0 {
            Self::update_timestamp(state, is_functional, now);
            return Ok(());
        }
    
        let interval_ms = if is_functional {
            config.functional_interval.as_millis() as u64
        } else {
            config.physical_interval.as_millis() as u64
        };
    
        /* Calculate the time difference. */
        let elapsed_ms = now.saturating_sub(last_timestamp);
    
        /* wait remaining time */
        if elapsed_ms < interval_ms {
            let remaining = Duration::from_millis(interval_ms - elapsed_ms);
            tokio::time::sleep(remaining).await;
        }
    
        /* update time status */
        let new_now = Self::current_millis();
        Self::update_timestamp(state, is_functional, new_now);
    
        Ok(())
    }
    
    fn current_millis() -> u64 {
        coarsetime::Clock::now_since_epoch().as_millis() as u64
    }
    
    fn update_timestamp(state: &TimeStamp, is_functional: bool, timestamp: u64) {
        match is_functional {
            true => state.last_functional.store(timestamp, Ordering::Release),
            false => state.last_physical.store(timestamp, Ordering::Release),
        }
    }

    pub async fn start_keep_alive_timer(&self, handler: Arc<AsyncIoManager>, cid:Cid, sa: u16, ta: u16) -> Result<()> {
        log_d!("start keep alive timer");
        let mut timer = self.timer_list.keep_alive_timer.lock().await;
        let config = self.config.read().await.clone();

        /* close previous timer */
        if let Some((old_handle, old_token)) = timer.take() {
            log_d!("close previous keep alive timer");
            old_token.cancel();
            old_handle.await.map_err(|e| {
                Error::new(
                    ErrorKind::Other,
                    format!("Failed to join keep-alive task: {}", e)
                )
            })?;
        }

        let mut req: Vec<u8> = vec![0x02, 0xfd, 0x80, 0x01, 0x00, 0x00, 0x00, 0x06];
        req.push((sa >> 8) as u8);
        req.push((sa & 0xff) as u8);
        req.push((ta >> 8) as u8);
        req.push((ta & 0xff) as u8);
        req.push(0x3e);
        req.push(0x80);
        let req_arc = Arc::new(req);

        {
            let cancel_token = CancellationToken::new();
            let write_handle = handler.clone();
            /* create a new timer */
            let handle = tokio::spawn({
                let cancel_token = cancel_token.clone();
                async move {
                    let start = tokio::time::Instant::now() + config.keep_alive_interval;
                    let mut interval = tokio::time::interval_at(start, config.keep_alive_interval);
                    loop {
                        tokio::select! {
                            _ = cancel_token.cancelled() => {
                                break;
                            }

                            _ = interval.tick() => {
                                log_w!("send heartbeat");
                                let req = req_arc.as_ref().clone();
                                let _ = write_handle.async_write(cid, req).await;
                            }
                        }
                    }
                }
            });
    
            // store timer info
            *timer = Some((handle, cancel_token));
        }

        Ok(())
    }

    pub async fn stop_keep_alive_timer(&self) -> Result<()> {
        let mut timer = self.timer_list.keep_alive_timer.lock().await;
        if let Some((handle, token)) = timer.take() {
            token.cancel();
            handle.await.map_err(|e| {
                Error::new(ErrorKind::Other, format!("Join error: {}", e))
            })?;
        }
        Ok(())
    }

    pub async fn start_response_timer(&self, is_nrc78: bool, handler: &Option<TimerCallback>) -> Result<()>{
        log_d!("start wait resp timer");
        let mut timer = self.timer_list.uds_resp_timer.lock().await;
        let config = self.config.read().await.clone();

        let timeout = if is_nrc78 {
            config.nrc78_timeout
        } else {
            config.normal_timeout
        };

        // close previous timer
        if let Some(handle) = timer.take() {
            handle.abort(); 
        }

        // create a new timer
        if let Some(cb) = handler {
            let callback = cb.clone();
            let handle = tokio::spawn(async move {
                tokio::time::sleep(timeout).await;
                log_d!("uds service resp timer trigle");
                callback();
            });
            
            *timer = Some(handle);
        } else {
            *timer = None;
            return Err(Error::new(
                ErrorKind::Other,
                format!("create {} error: callback is null", if is_nrc78 { "NRC78 timer" } else { "uds resp timer" })));
        }

        Ok(())
    }

    pub async fn stop_response_timer(&self) -> Result<()> {
        log_d!("close wait resp timer");
        let mut timer = self.timer_list.uds_resp_timer.lock().await;
        if let Some(handle) = timer.take() {
            handle.abort();
        }
        Ok(())
    }

}