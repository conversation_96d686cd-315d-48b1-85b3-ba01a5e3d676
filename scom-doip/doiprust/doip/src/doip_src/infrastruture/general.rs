use std::collections::HashMap;
use std::net::{Ipv4Addr, Ipv6Addr};
use std::sync::{Arc, RwLock};
use std::hash::Hash;
use std::io::{Result, <PERSON><PERSON><PERSON>, <PERSON><PERSON>r<PERSON><PERSON>};

fn ipv4_valid_check(ip: &str) -> bool {
    if let Ok(ipv4) = ip.parse::<Ipv4Addr>() {
        // 额外检查字符串格式防止前导零
        let parts: Vec<&str> = ip.split('.').collect();
        if parts.len() != 4 {
            return false;
        }
        for part in parts {
            if part.starts_with('0') && part.len() > 1 {
                return false;
            }
            if part.parse::<u8>().is_err() {
                return false;
            }
        }
        return ip == ipv4.to_string();
    }
    false
}

fn ipv6_valid_check(ip: &str) -> bool {
    ip.parse::<Ipv6Addr>().is_ok()
}

pub(crate) fn ip_valid_check(ip: &str) -> bool {
    ipv4_valid_check(ip) || ipv6_valid_check(ip)
}

/* 插入键值对 */
pub fn map_add_item<K, V>(map: &Arc<RwLock<HashMap<K, V>>>, key: K, value: V) -> Result<()> 
where
    K: Eq + Hash + Send + Sync + 'static,
    V: Send + Sync + 'static,
{
    let mut lock = map.write().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    lock.insert(key, value);
    Ok(())
}

pub fn map_contains_key<K, V>(
    map: &Arc<RwLock<HashMap<K, V>>>,
    key: &K,
) -> Result<bool>
where
    K: Eq + Hash + Sync + 'static,
{
    let lock = map.read().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    let b = lock.contains_key(key);
    match b {
        true => Ok(true),
        false => Ok(false)
    }
}

pub fn map_contains_value<K, V>(
    map: &Arc<RwLock<HashMap<K, V>>>,
    value: &V,
) -> Result<bool>
where
    K: Eq + Hash + Sync + 'static,
    V: Eq,  // 需要 value 支持相等比较
{
    // 获取读锁（不会阻塞写锁，但会等待写锁释放）
    let lock = map.read()
        .map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    
    // 遍历所有值，检查是否存在匹配项
    let exists = lock.values().any(|v| v == value);
    
    match exists {
        true => Ok(true),
        false => Ok(false)
    }
}

/* 通过 key 查找 value */
pub fn map_get_value<K, V>(map: &Arc<RwLock<HashMap<K, V>>>, key: &K) -> Result<V>
where
    K: Eq + Hash + Sync + 'static,
    V: Clone + Send + Sync + 'static,
{
    let lock = map.read().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    lock.get(key)
        .cloned()
        .ok_or_else(|| Error::new(ErrorKind::NotFound, "Key not found in [async] hashmap"))
}

/* 通过 value 查找 key */
pub fn map_get_key<K, V>(map: &Arc<RwLock<HashMap<K, V>>>, value: &V) -> Result<K>
where
    K: Clone + Eq + Hash + Send + Sync + 'static,
    V: PartialEq + Send + Sync + 'static,
{
    let lock = map.read().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    lock.iter()
        .find_map(|(k, v)| if v == value { Some(k.clone()) } else { None })
        .ok_or_else(|| Error::new(ErrorKind::NotFound, "Value not found in [async] hashmap"))
}

/* 通过key删除 key-value 对 */
pub fn map_remove_by_key<K, V>(map: &Arc<RwLock<HashMap<K, V>>>, key: &K) -> Result<()>
where
    K: Eq + Hash + Sync + 'static,
    V: Send + Sync + 'static,
{
    let mut lock = map.write().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    if lock.remove(key).is_some() {
        Ok(())
    } else {
        Err(Error::new(ErrorKind::NotFound, "Key not found in [async] hashmap"))
    }
}

/* 通过value删除 key-value 对 */
pub fn map_remove_by_value<K, V>(map: &Arc<RwLock<HashMap<K, V>>>, value: &V) -> Result<()>
where
    K: Clone + Eq + Hash + Send + Sync + 'static,
    V: PartialEq + Send + Sync + 'static,
{
    let mut lock = map.write().map_err(|_| Error::new(ErrorKind::Other, "lock poisoned"))?;
    
    let mut target_key = None;
    
    for (k, v) in lock.iter() {
        if v == value {
            target_key = Some(k.clone());
            break;
        }
    }

    match target_key {
        Some(key) => {
            lock.remove(&key);
            Ok(())
        }
        None => Err(Error::new(
            ErrorKind::NotFound,
            "Value not found in [async] hashmap"
        ))
    }
}