use std::collections::{HashMap, VecDeque};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::io::{Result, Error, ErrorKind};
use tokio::net::tcp::{OwnedReadHalf, OwnedWriteHalf};
use tokio::sync::{mpsc, watch, broadcast, RwLock, Mutex};
use tokio::task::Jo<PERSON><PERSON><PERSON><PERSON>;
use crate::doip_src::socket::async_stream;
use crate::{log_d, log_e, log_hex, log_i};
use crate::util::{Cid, ListenerEventType};
use crate::doip_src::data_structure::message::EventInfo;
use tokio::time::{timeout, Duration};


#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq)]
pub enum ConnectionStatus {
    Init,
    Connected,
    Connecting,
    Disconnected,
    WaitActivation,
}

enum GuardianEvent {
    Reconnect,
    CloseConnector,
}

pub struct Connector {
    activate: bool,

    write_tx: mpsc::Sender<Vec<u8>>,
    status_tx: broadcast::Sender<ConnectionStatus>,
    _read_handle: JoinHandle<()>,
    _write_handle: JoinHandle<()>,

    guardian_tx: mpsc::Sender<GuardianEvent>,
    pub status: Arc<Mutex<ConnectionStatus>>,
    pub message_queue: Arc<Mutex<VecDeque<Vec<u8>>>>,
    remote_addr: (String, u16),
}

pub struct AsyncIoManager {
    /* 每个tcpstream创建后都有一个唯一cid进行映射 */
    next_cid: Arc<AtomicU64>,
    /* 将cid与连接映射，上层通过cid进行操作 */
    pub connections: Arc<RwLock<HashMap<Cid, Connector>>>,
    // 数据通道：传输正常接收数据
    data_tx: mpsc::Sender<(Cid, Vec<u8>)>,
    // 事件通道：传输异常事件
    event_tx: mpsc::Sender<(ListenerEventType, EventInfo)>,
}

impl AsyncIoManager {
    const RECONNECT_TIMEOUT: Duration = Duration::from_secs(3);
    const MAX_RETRY_ATTEMPTS: u32 = 5; //retry time
    const WRITE_CHANNEL_SIZE: usize = 100;

    pub fn new() -> (Arc<Self>, mpsc::Receiver<(Cid, Vec<u8>)>, mpsc::Receiver<(ListenerEventType, EventInfo)>)  {
        let (data_tx, data_rx) = mpsc::channel(1024);
        let (event_tx, event_rx) = mpsc::channel(1024);
        let manager =  Arc::new(Self {
            next_cid: Arc::new(AtomicU64::new(1)),
            connections: Arc::new(RwLock::new(HashMap::new())),
            data_tx,
            event_tx,
        });

        (manager, data_rx, event_rx)
    }

    // 建立新的 TCP 连接
    pub async fn connect(self: Arc<Self>, ip: &str, port: u16) -> Result<Cid> {
        let stream = async_stream::async_connect(ip, port).await?;
        let cid = self.next_cid.fetch_add(1, Ordering::SeqCst);
        log_i!("create connect - cid {}",cid);
        let (read_half, write_half) = stream.into_split();
        let (status_tx, _) = broadcast::channel(100);

        let (guardian_tx, guardian_rx) = mpsc::channel(10);
        let status = Arc::new(Mutex::new(ConnectionStatus::Connecting));
        let message_queue = Arc::new(Mutex::new(VecDeque::new()));

        /* create write task */
        let (write_tx, write_handle) = self.spawn_writer(
            write_half,
            message_queue.clone(),
            status.clone(),
            status_tx.subscribe(),
            guardian_tx.clone(),
        );

        /* create read task */
        let read_handle = self.spawn_reader(
            cid,
            read_half,
            status.clone(),
            status_tx.subscribe(),
            guardian_tx.clone(),
            self.data_tx.clone()
        );

        /* create monitor task */
        self.clone().spawn_guardian(
            cid,
            guardian_rx,
            ip.to_string(),
            port,
            status.clone(),
            status_tx.clone(),
            message_queue.clone(),
        );

        *status.lock().await = ConnectionStatus::WaitActivation;
        /* store connection info */
        self.connections.write().await.insert(
            cid,
            Connector {
                activate: false,
                write_tx,
                status_tx,
                _read_handle: read_handle,
                _write_handle: write_handle,
                guardian_tx,
                status,
                message_queue,
                remote_addr: (ip.to_string(), port),
            },
        );

        Ok(cid)
    }

    fn spawn_reader(
        &self,
        cid: Cid,
        mut reader: OwnedReadHalf,
        status: Arc<Mutex<ConnectionStatus>>,
        mut status_rx: broadcast::Receiver<ConnectionStatus>,
        guardian_tx: mpsc::Sender<GuardianEvent>,
        data_tx: mpsc::Sender<(Cid, Vec<u8>)>
    ) -> JoinHandle<()> {
        tokio::spawn(async move {
            let mut buffer = [0u8; 4096];
            loop {
                tokio::select! {
                    Ok(status) = status_rx.recv() => {
                        if status != ConnectionStatus::Connected &&
                           status != ConnectionStatus::WaitActivation
                        {
                            log_e!("检测到连接断开，退出读任务");
                            break;
                        }
                    }

                    
                    result = async_stream::read_half(&mut reader, &mut buffer) =>{
                        match result {
                            Ok(0) | Err(_) => { // 连接断开或错误
                                if *status.lock().await == ConnectionStatus::Connected{
                                    *status.lock().await = ConnectionStatus::Disconnected;
                                    log_e!("读任务异常，推送重连事件");
                                    let _ = guardian_tx.send(GuardianEvent::Reconnect).await;
                                }
                                
                                break;
                            }
                            Ok(n) => {
                                if let Err(e) = data_tx.send((cid, buffer[..n].to_vec())).await{
                                    /* 推送数据丢失事件 */
                                    log_i!("推送数据");
                                }
                            }
                        }
                    }
                }
            }
            drop(reader);
            log_e!("read task closed");
        })
    }

    fn spawn_writer(
        &self,
        mut writer: OwnedWriteHalf,
        message_queue: Arc<Mutex<VecDeque<Vec<u8>>>>,
        status: Arc<Mutex<ConnectionStatus>>,
        mut status_rx: broadcast::Receiver<ConnectionStatus>,
        guardian_tx: mpsc::Sender<GuardianEvent>,
    ) -> (mpsc::Sender<Vec<u8>>, JoinHandle<()>)
    {
        let (write_tx, mut write_rx) = mpsc::channel::<Vec<u8>>(100);
        
        let handle = tokio::spawn(async move {
            loop {
                tokio::select! {
                    Ok(status) = status_rx.recv() => {
                        if status != ConnectionStatus::Connected &&
                           status != ConnectionStatus::WaitActivation
                        {
                            log_e!("检测到连接断开，退出写入任务");
                            break;
                        }
                    }

                    data = write_rx.recv() =>{
                        log_i!("write task: receive data");
                        match data {
                            Some(data) => {
                                let mut offset = 0;
                                let data_len = data.len();

                                log_i!("current status = {:?}", *status.lock().await);
                                if *status.lock().await != ConnectionStatus::Connected &&
                                    *status.lock().await != ConnectionStatus::WaitActivation
                                {
                                     // 保存未发送数据
                                     message_queue.lock().await.push_back(data);

                                     while let Ok(data) = write_rx.try_recv() {
                                         message_queue.lock().await.push_back(data);
                                     }
                                     break;
                                }

                                // 分段写入处理
                                while offset < data_len {
                                    let chunk = &data[offset..];
                                    match async_stream::write_half(&mut writer, chunk).await {
                                        Ok(n) => {
                                            offset+= n;
                                        }
                                        Err(e) => {                                            
                                            // 保存未发送数据并触发重连
                                            message_queue.lock().await.push_back(data);

                                            while let Ok(data) = write_rx.try_recv() {
                                                message_queue.lock().await.push_back(data);
                                            }

                                            if *status.lock().await == ConnectionStatus::Connected{
                                                *status.lock().await = ConnectionStatus::Disconnected;
                                                log_e!("写任务推送重连事件");
                                                let _ = guardian_tx.send(GuardianEvent::Reconnect).await;
                                            }
                                            break;
                                        }
                                    }
                                }
                            }
                            None => {
                                log_e!("write_rx.recv() => None");
                                break;
                            } // 通道关闭
                        }
                    }
                }
            }

            drop(writer);
            log_e!("write task closed");
        });
        
        (write_tx, handle)
    }

    fn spawn_guardian(
        self: Arc<Self>,
        cid: Cid,
        mut guardian_rx: mpsc::Receiver<GuardianEvent>,
        ip: String,
        port: u16,
        status: Arc<Mutex<ConnectionStatus>>,
        status_tx: broadcast::Sender<ConnectionStatus>,
        message_queue: Arc<Mutex<VecDeque<Vec<u8>>>>,
    ) {
        tokio::spawn(async move {
            let mut retries = 0;
            loop {
                if let Some(event) = guardian_rx.recv().await {
                    match event {
                        GuardianEvent::Reconnect => {
                            *status.lock().await = ConnectionStatus::Connecting;
                            self.set_activate_status(cid, false).await;
                            log_i!("connect status change to [ConnectionStatus::Connecting]");

                            /* Notify the old read/write task to exit */
                            let _ = status_tx.send(ConnectionStatus::Connecting);
                            
                            let start_time = tokio::time::Instant::now();
                            let mut connected = false;
                            
                            while start_time.elapsed() < Self::RECONNECT_TIMEOUT 
                                && retries < Self::MAX_RETRY_ATTEMPTS 
                            {
                                match self.reconnect(cid, &ip, port).await {
                                    Ok(()) => {
                                        // /* send old message */
                                        // let mut queue = message_queue.lock().await;
                                        // while let Some(data) = queue.pop_front() {
                                        //     let _ = self.async_write(cid, data).await;
                                        // }
                                        
                                        connected = true;
                                        break;
                                    }
                                    Err(e) => {
                                        retries += 1;
                                        tokio::time::sleep(Duration::from_secs(1)).await;
                                    }
                                }
                            }

                            /* check reconnect result */
                            if !connected {
                                *status.lock().await = ConnectionStatus::Disconnected;
                                self.disconnect(cid).await;
                                log_e!("connector[cid:{}] reconnect timeout, closed", cid);
                                let _ = self.send_event(ListenerEventType::Disconnection, EventInfo::new(cid)).await;
                                break;
                            } else {
                                log_i!("reconnected - wait routing activation");
                                *status.lock().await = ConnectionStatus::WaitActivation;
                                log_i!("connect status change to [ConnectionStatus::WaitActivation]");
                                log_i!("send activation event to  channel layer");
                                let _ = self.send_event(ListenerEventType::Reconnected, EventInfo::new(cid)).await;
                                retries = 0;
                            }
                        }

                        GuardianEvent::CloseConnector => {
                            *status.lock().await = ConnectionStatus::Disconnected;
                            self.disconnect(cid).await;
                            let _ = self.send_event(ListenerEventType::Disconnection, EventInfo::new(cid)).await;
                            log_e!("connector[cid:{}] closed", cid);
                            break;
                        }
                    }
                }
            }
            
        });
    }

    async fn reconnect(&self,
        cid: Cid,
        ip: &str,
        port: u16,
    )-> Result<()>{
        log_i!("reconnecting...");
        let stream = async_stream::async_connect(ip, port).await?;
        let (read, write) = stream.into_split();

        let mut conn_map = self.connections.write().await;
        if let Some(old_conn) = conn_map.get_mut(&cid) {
            let (new_write_tx, new_write_handle) = self.spawn_writer(
                write,
                old_conn.message_queue.clone(),
                old_conn.status.clone(),
                old_conn.status_tx.subscribe(),
                old_conn.guardian_tx.clone()
            );
            
            let new_read_handle = self.spawn_reader(
                cid,
                read,
                old_conn.status.clone(),
                old_conn.status_tx.subscribe(),
                old_conn.guardian_tx.clone(),
                self.data_tx.clone()
            );

            /* update connector info */
            old_conn._read_handle = new_read_handle;
            old_conn._write_handle = new_write_handle;
            old_conn.write_tx = new_write_tx;
        } else {
            return Err(std::io::Error::new(std::io::ErrorKind::NotFound, "reconnect error: can't found old connector"))
        }
        Ok(())
    }
  
    // 异步写入数据
    pub async fn async_write(&self, cid: Cid, data: Vec<u8>) -> Result<()> {
        let connections = self.connections.read().await;
        match connections.get(&cid) {
            Some(conn) => {
                let current_status = *conn.status.lock().await;
                if current_status == ConnectionStatus::Connected ||
                   current_status == ConnectionStatus::WaitActivation
                {
                    log_i!("send data to write task");
                    conn.write_tx.send(data).await.map_err(|e| {
                        std::io::Error::new(std::io::ErrorKind::BrokenPipe, format!("write_tx.send error : {}",e))
                    })
                }else {
                    log_e!("connector not ready, push message to queue");
                    conn.message_queue.lock().await.push_back(data);
                    
                    Ok(())
                }
            }
            _ => Err(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Connection not found",
            )),
        }
    }

    pub async fn close_connector(&self, cid: Cid) -> Result<()>{
        let conn_list = self.connections.read().await;
        if let Some(conn) = conn_list.get(&cid) {
            let _ = conn.guardian_tx.send(GuardianEvent::CloseConnector).await;
            Ok(())
        }
        else {
            Err(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                format!("not found connector with cid:{}",cid)
            ))
        }
    }

    async fn disconnect(&self, cid: Cid){
        let mut connections = self.connections.write().await;
        if let Some(conn) = connections.remove(&cid) {
            let _ = conn.status_tx.send(ConnectionStatus::Disconnected);
            // 等待任务完成
            if let Err(_) = timeout(Duration::from_secs(5), async {
                tokio::join!(conn._read_handle, conn._write_handle)
            }).await {
                /* 强制关闭 */
            }
        }
    }

    async fn send_event(&self, event_type: ListenerEventType, event: EventInfo) -> Result<()> {
        self.event_tx.send((event_type, event)).await.map_err(
            |e| std::io::Error::new(
            std::io::ErrorKind::BrokenPipe,
            format!("Failed to send event: {}", e)
        ))
    }

    pub async fn set_activate_status(&self, cid: Cid, status: bool){
        let mut conn_list = self.connections.write().await;
        if let Some(conn) = conn_list.get_mut(&cid){
            conn.activate = status;
        }
    }

    pub async fn check_activate_status(&self, cid: Cid)-> Result<bool>{
        let mut status = false;
        let conn_list = self.connections.read().await;
        if let Some(conn) = conn_list.get(&cid) {
            status =  conn.activate;
        }else {
            return Err(Error::new(
                std::io::ErrorKind::Other,
                format!("not found connector with cid({}) in conn_list",cid)));
        }

        Ok(status)
    }

    pub async fn set_connect_status(&self, cid: Cid, status:ConnectionStatus){
        let mut conn_list = self.connections.write().await;
        if let Some(conn) = conn_list.get_mut(&cid){
            *conn.status.lock().await = status;
        }
    }

    pub async fn get_connect_status(&self, cid: Cid) -> ConnectionStatus{
        let mut status = ConnectionStatus::Init;
        let conn_list = self.connections.read().await;
        if let Some(conn) = conn_list.get(&cid) {
            status =  *conn.status.lock().await;
        }

        status
    }
}
