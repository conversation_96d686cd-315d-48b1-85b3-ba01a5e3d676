use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tokio::sync::{<PERSON>tex, RwLock};

use crate::doip_src::infrastruture::uds_timer::TimerManager;
use crate::{log_d, log_e, log_hex, log_i, log_w};
use std::io::{Result, <PERSON>rror, ErrorKind};
use crate::util::{Cid, DiagnosticMessage, ServerCallback, UdsCallback};

use super::async_io_manager::AsyncIoManager;


pub struct UdsManager {
    server_callback: Arc<RwLock<ServerCallback>>,
    is_func_addr: RwLock<bool>,
    addr_func: RwLock<u16>,
    is_default_session: RwLock<bool>,
    timer_manager: Arc<TimerManager>,
}

impl UdsManager {
    const TAG:&'static str = "UdsManager";

    pub fn new() -> Self {
        let instance = UdsManager {
            server_callback: Arc::new(RwLock::new(ServerCallback::default())),
            is_func_addr: RwLock::new(true),
            addr_func: RwLock::new(0x006f),
            is_default_session: RwLock::new(true),
            timer_manager: Arc::new(TimerManager::new())
        };

        log_d!("create UdsManager success");

        instance
    }

    pub async fn init(&self, callback: ServerCallback) -> Result<()> {
        log_i!("register uds callback");
        let mut sc = self.server_callback.write().await;
        
        // 逐字段更新配置
        macro_rules! update_callback {
            ($field:ident) => {
                if let Some(cb) = callback.$field {
                    sc.$field = Some(cb);
                }
            };
        }

        update_callback!(service_10h_callback);
        update_callback!(service_11h_callback);
        update_callback!(service_14h_callback);
        update_callback!(service_19h_callback);
        update_callback!(service_22h_callback);
        update_callback!(service_23h_callback);
        update_callback!(service_27h_callback);
        update_callback!(service_28h_callback);
        update_callback!(service_2Ah_callback);
        update_callback!(service_2Ch_callback);
        update_callback!(service_2Eh_callback);
        update_callback!(service_2Fh_callback);
        update_callback!(service_31h_callback);
        update_callback!(service_34h_callback);
        update_callback!(service_36h_callback);
        update_callback!(service_37h_callback);
        update_callback!(service_38h_callback);
        update_callback!(service_3Dh_callback);
        update_callback!(service_3Eh_callback);
        update_callback!(service_85h_callback);
        update_callback!(service_29h_callback);
        update_callback!(service_7fh_callback);

        update_callback!(timer_nrc78_callback);
        update_callback!(timer_resp_callback);


        log_i!("register uds callback success");
        log_i!("uds init over");
        Ok(())
    }

    async fn keep_session_check(&mut self, req: &Vec<u8>){
        match req.get(0) {
            Some(0x10) => {
                match req.get(1) {
                    Some(0x01) => {
                        let mut guard = self.is_default_session.write().await;
                        *guard = true;
                    }
                    Some(0x02) => {
                        let mut guard = self.is_default_session.write().await;
                        *guard = false;
                    }
                    Some(0x03) => {
                        let mut guard = self.is_default_session.write().await;
                        *guard = false;
                    }

                    Some(_) => {}
                    None => {}
                }
            }

            Some(_) => {}
            None => {}
        }
    }

    async fn addressing_mode_check(&mut self, ta: u16){
        let guard = self.addr_func.read().await;
        if ta == *guard {
            let mut f_guard = self.is_func_addr.write().await;
            *f_guard = true;
        }else {
            let mut f_guard = self.is_func_addr.write().await;
            /* 默认所有uds报文都需要间隔50ms，根据需要调整 */
            // *f_guard = false;
        }
    }
    
    pub async fn timer_config(
        &mut self, 
        handle: Arc<AsyncIoManager>, 
        cid:Cid, 
        sa: u16, 
        ta: u16, 
        req: Vec<u8>
    ){
        self.keep_session_check(&req).await;
        self.addressing_mode_check(ta).await;
        let _ = self.timer_manager.check_interval(*self.is_func_addr.read().await).await;
        
        /* start timer */
        let sc = self.server_callback.read().await;
        match req.get(0) {
            Some(0x3e) => {}

            _=> {
                if *self.is_default_session.read().await == true {
                    // let _ = self.timer_manager.stop_keep_alive_timer();
                    let _ = self.timer_manager.stop_keep_alive_timer().await;
                }else {
                    // let _ = self.timer_manager.start_keep_alive_timer(&sc.timer_kepp_alive_callback);
                    let _ = self.timer_manager.start_keep_alive_timer(handle, cid, sa, ta).await;
                }
            }
        }
        
        // let _ = self.timer_manager.start_response_timer(false, &sc.timer_resp_callback);
        let _ = self.timer_manager.start_response_timer(false, &sc.timer_resp_callback).await;
    }

    pub async fn uds_msg_handle(&self, uds_msg: DiagnosticMessage) -> Result<()>{
        log_d!("uds msg handle");
        if uds_msg.data.is_empty() {
            log_e!("uds resp data is empty");
            return Err(Error::new(ErrorKind::Other, format!("[{}] - [uds_msg_handle] : uds resp data is empty",Self::TAG)));
        }

        /* close timer */
        let _ = self.timer_manager.stop_response_timer().await;

        let sid = uds_msg.data[0];
        let sc = self.server_callback.read().await;
        
        /* 调试使用clone */
        let (ta, data) = (uds_msg.ta, uds_msg.data.clone());

        match sid {
            0x50 => self.call_handler(&sc.service_10h_callback, ta, data),
            0x51 => self.call_handler(&sc.service_11h_callback, ta, data),
            0x54 => self.call_handler(&sc.service_14h_callback, ta, data),
            0x59 => self.call_handler(&sc.service_19h_callback, ta, data),
            0x62 => self.call_handler(&sc.service_22h_callback, ta, data),
            0x63 => self.call_handler(&sc.service_23h_callback, ta, data),
            0x67 => self.call_handler(&sc.service_27h_callback, ta, data),
            0x68 => self.call_handler(&sc.service_28h_callback, ta, data),
            0x6A => self.call_handler(&sc.service_2Ah_callback, ta, data),
            0x6C => self.call_handler(&sc.service_2Ch_callback, ta, data),
            0x6E => self.call_handler(&sc.service_2Eh_callback, ta, data),
            0x6F => self.call_handler(&sc.service_2Fh_callback, ta, data),
            0x71 => self.call_handler(&sc.service_31h_callback, ta, data),
            0x74 => self.call_handler(&sc.service_34h_callback, ta, data),
            0x76 => self.call_handler(&sc.service_36h_callback, ta, data),
            0x77 => self.call_handler(&sc.service_37h_callback, ta, data),
            0x78 => self.call_handler(&sc.service_38h_callback, ta, data),
            0x7D => self.call_handler(&sc.service_3Dh_callback, ta, data),
            0x7E => self.call_handler(&sc.service_3Eh_callback, ta, data),
            0xC5 => self.call_handler(&sc.service_85h_callback, ta, data),
            0x69 => self.call_handler(&sc.service_29h_callback, ta, data),

            0x7f => self.negative_resp(&sc.service_7fh_callback ,ta, data).await,
            _ => {
                log_e!("sid:{} callback unregister", sid);
            }
        }

        Ok(())
    }

    fn call_handler(&self, handler: &Option<UdsCallback>, ta: u16, data: Vec<u8>) {
        if let Some(cb) = handler {
            // log_i!("ta:{}",ta);
            log_hex!(Info,"uds resp", data.clone());
            cb(ta, data);
        } else {
            log_e!("callback is null");
        }
    }

    async fn negative_resp(&self, handler: &Option<UdsCallback>, ta: u16, data: Vec<u8>){
        match data.get(2) {
            /* ecu pending */
            Some(0x78) => {
                let sc = self.server_callback.read().await;
                let _ = self.timer_manager.start_response_timer(true, &sc.timer_nrc78_callback).await;
            }

            /* ecu negative resp */
            _ => {
                if let Some(cb) = handler {
                    // log_i!("ta:{}",ta);
                    log_hex!(Info,"uds resp", data.clone());
                    cb(ta, data);
                } else {
                    log_e!("callback is null");
                }
            }
        }
    }
}