use std::sync::{Arc, RwLock, OnceLock};
use crate::util::{ListenerEventType, ListenerEventInfo, ListenerCallback};

pub struct SyncListener {
    mask: std::sync::atomic::AtomicU8,
    callback: RwLock<Option<ListenerCallback>>,
}

impl SyncListener {
    pub fn instance() -> &'static Arc<Self> {
        static INSTANCE: OnceLock<Arc<SyncListener>> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            Arc::new(Self {
                mask: std::sync::atomic::AtomicU8::new(ListenerEventType::NoEvent.bits()),
                callback: RwLock::new(None),
            })
        })
    }

    pub fn set_callback(&self, cb: impl Fn(ListenerEventType, ListenerEventInfo) + Send + Sync + 'static) {
        *self.callback.write().unwrap() = Some(Box::new(cb));
    }

    pub fn register_event(&self, mask: ListenerEventType) {
        self.mask.fetch_or(mask.bits(), std::sync::atomic::Ordering::SeqCst);
    }

    pub fn notify(&self, event_type: ListenerEventType, info: ListenerEventInfo) {
        let current_mask = self.mask.load(std::sync::atomic::Ordering::Acquire);
        if (current_mask & event_type.bits()) != 0 {
            if let Some(cb) = self.callback.read().unwrap().as_ref() {
                cb(event_type, info);
            }
        }
    }
}