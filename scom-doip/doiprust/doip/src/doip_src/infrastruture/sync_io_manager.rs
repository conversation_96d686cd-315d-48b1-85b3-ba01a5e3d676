use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::{Arc, RwLock};
use std::collections::HashMap;
use std::net::TcpStream;
use std::io::{Read, Result, Write};
use std::io::{Error, ErrorKind};

use crate::doip_src::socket::sync_stream;
use crate::util::Cid;

#[derive(Clone)]
struct Connection {
    stream: Arc<RwLock<TcpStream>>
}
pub struct SyncIoManager {
    /* 每个tcpstream创建后都有一个唯一cid进行映射 */
    next_cid: Arc<AtomicU64>,
    /* 将cid与连接映射，上层通过cid进行操作 */
    connections: Arc<RwLock<HashMap<Cid, Connection>>>,
}

impl SyncIoManager {
    pub fn new() -> Arc<Self> {
        Arc::new(Self {
            next_cid: Arc::new(AtomicU64::new(1)), 
            connections: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    pub fn connect(&self, ip: String, port: u16) -> Result<Cid> {
        let stream = sync_stream::connect(ip, port);
        match stream {
            Ok(stream) => {
                stream.set_nonblocking(false)?;
                let cid = self.next_cid.fetch_add(1, Ordering::SeqCst);
                let mut conn = self.connections.write().map_err(|e| Error::new(ErrorKind::Other, "Lock poisoned"))?;
                conn.insert(cid, Connection{stream: Arc::new(RwLock::new(stream))});
                Ok(cid)
            }
            Err(e) => {
                return Err(Error::new(ErrorKind::Other, format!("create sync connect failed: {}", e)));
            }
        }
    }

    pub fn disconnect(&self, cid: Cid)-> Result<()>{
        let mut conn_map = self.connections.write()
            .map_err(|e| Error::new(ErrorKind::Other, "Lock poisoned"))?;

        if let Some(mut conn) = conn_map.remove(&cid) {
            let mut lock = conn.stream.write()
                .map_err(|e| Error::new(ErrorKind::Other, "Lock poisoned"))?;
            
            let error_code = sync_stream::disconnect(&mut *lock);
            match error_code {
                Err(e) => {
                    return Err(Error::new(ErrorKind::Other, format!("close connection[{}] failed: {}",cid, e)));
                }
                Ok(()) => {
                    /* do nothing */
                }
            }
        }

        Ok(())
    }

    pub fn read(&self, cid: Cid, buf: &mut Vec<u8>)-> Result<usize>{
        let mut conn_map = self.connections.write().map_err(|e| Error::new(ErrorKind::Other, "Lock poisoned"))?;
        match conn_map.get_mut(&cid) {
            Some(conn) => {
                let mut stream_lock = conn.stream.write()
                    .map_err(|e| Error::new(ErrorKind::Other, "Stream lock failed"))?;

                sync_stream::read(&mut *stream_lock, buf)
                    .map_err(|e| Error::new(
                        ErrorKind::Other,
                        format!("Receive from [{}] failed: {}", cid, e)
                    ))
            }
            _ => Err(Error::new(
                ErrorKind::NotFound,
                format!("Connection {} not found", cid)
            ))
        }
    }

    pub fn write(&self, cid: Cid, buf: &[u8]) -> Result<()> {
        let mut conn_map = self.connections.write()
            .map_err(|e| Error::new(ErrorKind::Other, "Lock poisoned"))?;

        match conn_map.get_mut(&cid) {
            Some(conn) => {
                let mut stream_lock = conn.stream.write()
                    .map_err(|e| Error::new(ErrorKind::Other, "Stream lock failed"))?;

                sync_stream::write_all(&mut *stream_lock, buf)
                    .map_err(|e| Error::new(
                        ErrorKind::Other,
                        format!("Send to [{}] failed: {}", cid, e)
                    ))
            }
            None => Err(Error::new(
                ErrorKind::NotFound,
                format!("Connection {} not found", cid)
            ))
        }
    }
}