use std::io::{Read, Result, Write};
use std::net::{IpAddr, Shutdown, SocketAddr, TcpStream};
use std::str::FromStr;

pub fn connect(ip: String, port: u16) -> Result<TcpStream> {
    let ip_addr = match IpAddr::from_str(&ip) {
        Ok(addr) => addr,
        Err(_) => {
            return Err(std::io::Error::new(
                std::io::ErrorKind::InvalidInput,
                format!("Invalid IP address format: {}", ip)
            ))
        }
    };

    // 组合成Socket地址
    let addr = SocketAddr::new(ip_addr, port);

    TcpStream::connect(addr)
}


pub fn disconnect(stream: &TcpStream) -> Result<()> {
    stream.shutdown(Shutdown::Both)
}

pub fn read(stream: &mut TcpStream, buf: &mut Vec<u8>) -> Result<usize> {
    if buf.capacity() == 0 {
        return Err(std::io::Error::new(
            std::io::ErrorKind::InvalidInput,
            "Buffer capacity must be greater than zero",
        ));
    }

    let original_capacity = buf.capacity();
    buf.clear();
    buf.resize(original_capacity, 0); 

    let bytes_read = match stream.read(buf) {
        Ok(n) => n,
        Err(e) => {
            buf.truncate(0); 
            return Err(e);
        }
    };

    buf.truncate(bytes_read);
    Ok(bytes_read)
}

pub fn write_all(stream: &mut TcpStream, buf: &[u8]) -> Result<()> {
    stream.write_all(buf)
}

pub fn set_nodelay(stream: &TcpStream, nodelay: bool) -> Result<()> {
    stream.set_nodelay(nodelay)
}

pub fn set_timeout(
    stream: &TcpStream,
    timeout: Option<std::time::Duration>,
) -> Result<()> {
    stream.set_read_timeout(timeout)?;
    stream.set_write_timeout(timeout)
}