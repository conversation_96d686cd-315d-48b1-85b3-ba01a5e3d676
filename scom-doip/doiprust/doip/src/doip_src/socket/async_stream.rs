use std::net::SocketAddr;
use std::str::FromStr;
use std::time::Duration;
use tokio::net::{TcpStream, tcp::{OwnedWriteHalf, OwnedReadHalf}};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use crate::doip_src::data_structure::socket_typedef::SocketConfigOption;

pub(crate) async fn socket_conf_signal(sockfd: &TcpStream, config_option: SocketConfigOption) -> std::io::Result<()> {
    let config_options = [config_option];
    socket_conf(sockfd, &config_options).await
}

pub(crate) async fn socket_conf(sockfd: &TcpStream, opt: &[SocketConfigOption]) -> std::io::Result<()> {
    for option in opt {
        match option {
            SocketConfigOption::NoDelay(val) => {
                sockfd.set_nodelay(*val)?;
            }
        }
    }
    Ok(())
}

pub(crate) async fn async_connect(ip: &str, port: u16) -> std::io::Result<TcpStream> {
    let addr_str = format!("{}:{}", ip, port);
    let addr = SocketAddr::from_str(&addr_str)
        .map_err(|e| std::io::Error::new(std::io::ErrorKind::InvalidInput, e))?;
    
    let connect_future = TcpStream::connect(addr);
    let stream = match tokio::time::timeout(Duration::from_secs(5), connect_future).await {
        Ok(Ok(stream)) => stream,
        Ok(Err(e)) => return Err(e),       // 普通连接错误
        Err(_) => return Err(std::io::Error::new( // 超时错误
            std::io::ErrorKind::TimedOut,
            format!("Connection to {} timed out after 5s", addr)
        )),
    };
    socket_conf_signal(&stream, SocketConfigOption::NoDelay(true)).await?;
    
    Ok(stream)
}

pub(crate) async fn write(stream: &mut TcpStream, buffer: &[u8]) -> std::io::Result<usize> {
    stream.write(buffer).await
}

pub(crate) async fn read(stream: &mut TcpStream, buffer: &mut [u8]) -> std::io::Result<usize> {
    stream.read(buffer).await
}

pub(crate) async fn write_half(write_half: &mut OwnedWriteHalf, buffer: &[u8]) -> std::io::Result<usize> {
    write_half.write(buffer).await
}

pub async fn read_half( read_half: &mut OwnedReadHalf, buffer: &mut [u8],) -> std::io::Result<usize> {
    read_half.read(buffer).await
}

pub(crate) async fn close(mut stream: TcpStream) -> std::io::Result<()> {
    stream.shutdown().await
}