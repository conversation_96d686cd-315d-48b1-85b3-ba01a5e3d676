use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::{RwLock, Mutex, mpsc};
type SyncRwLock<T> = std::sync::RwLock<T>;

use tokio::time::{Duration, Instant};
use crate::log::LogLevel;
use crate::{log, log_d, log_e, log_hex, log_i, log_w};
use crate::util::{MsgProperty, ServerCallback};
use crate::doip_src::infrastruture::listener::SyncListener;
use crate::doip_src::infrastruture::async_io_manager::{AsyncIoManager, ConnectionStatus};
use crate::doip_src::infrastruture::sync_io_manager::SyncIoManager;
use crate::doip_src::infrastruture::doip_manager::DoipManager;
use crate::doip_src::infrastruture::uds_manager::UdsManager;
use crate::error_code::{DoipError, ErrorCode};
use crate::doip_src::infrastruture::general::*;
use crate::util::{ListenerEventType, ListenerEventInfo, OtaDataInfo, Cid, DiagnosticMessage};
use crate::doip_src::data_structure::message::{EventInfo, DoipMsgType, DoipMessage};
use crate::doip_src::data_structure::doip_error_code::{DoipDiagErrorCode, routing_activation_code, DoipParseErrorCode};

type Callback = Box<dyn Fn(Vec<u8>) + Send + Sync + 'static>;
pub type Result<T> = std::result::Result<T, DoipError>;

pub  struct OtaChannelImpl{
    port_ : u16,
    callback: Arc<RwLock<Option<Callback>>>,
    listener : &'static Arc<SyncListener>,
    pub async_io : Arc<AsyncIoManager>,
    pub sync_io : Arc<SyncIoManager>,
    pub manager : Arc<DoipManager>,

    /* 适配map接口采用两张表映射<ip-cid-entity> */
    async_cid_uds_map : Arc<SyncRwLock<HashMap<Cid, Arc<Mutex<UdsManager>>>>>,
    sync_cid_uds_map : Arc<SyncRwLock<HashMap<Cid, Arc<Mutex<UdsManager>>>>>,
    async_ip_cid_map : Arc<SyncRwLock<HashMap<String, Cid>>>,
    sync_ip_cid_map : Arc<SyncRwLock<HashMap<String, Cid>>>,
}

impl OtaChannelImpl{
    pub fn new(sa: u16, port : u16) -> Arc<Self>{
        let (async_io, data_rx, event_rx) = AsyncIoManager::new();

        let instance = Arc::new(Self {
            port_: port,
            callback: Arc::new(RwLock::new(None)),
            listener: SyncListener::instance(),
            async_io,
            sync_io: SyncIoManager::new(),
            manager: DoipManager::new(sa),
            async_cid_uds_map: Arc::new(SyncRwLock::new(HashMap::new())),
            sync_cid_uds_map: Arc::new(SyncRwLock::new(HashMap::new())),
            async_ip_cid_map: Arc::new(SyncRwLock::new(HashMap::new())),
            sync_ip_cid_map: Arc::new(SyncRwLock::new(HashMap::new())),
        });

        /* 克隆Arc实例启动接收通道（共享同一实例） */
        let data_task_instance = Arc::clone(&instance);
        let event_task_instance = Arc::clone(&instance);
        data_task_instance.start_data_task(data_rx);
        event_task_instance.start_event_task(event_rx);

        log_d!("create OtaChannelImpl success");
        instance
    }

    pub fn init(&self){
        let _ = log::init(LogLevel::Info);
        // let _ = self.uds.init(callback_list);

        coarsetime::Instant::now();
        /* 从配置文件解析所需参数 */
        log_d!("OtaChannelImpl init over");
    }

    async fn data_recv_handle(&self, cid: Cid, data: Vec<u8>){
        self.manager.doip_recv_process(self, cid, data).await;
    }

     // 启动数据处理任务
     fn start_data_task(
        self: Arc<Self>,
        mut data_rx: mpsc::Receiver<(Cid, Vec<u8>)>
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            while let Some((cid, data)) = data_rx.recv().await {
                log_hex!(Debug, "doip resp msg", data.clone());

                /* 调用data_recv_handle接口处理数据 */
                self.data_recv_handle(cid, data).await;
            }
        })
    }

    pub async fn on_event(&self, mask: ListenerEventType, event: EventInfo){
        let mut msg = ListenerEventInfo::default();

        match mask {
            ListenerEventType::Reconnected => {
                log_i!("on_event: receive event - ListenerEventType::Reconnected");
                let _ = self.send_activate_req(event.cid).await;
                
                if let Err(e) = self.get_activate_status(event.cid).await {
                    log_e!("routing activation error: {}",e);
        
                    let _ = self.async_io.close_connector(event.cid).await;
                    // return Err(ErrorCode::RoutingActivationError.descript(format!("routing activation error: {}", e)));
                }

                log_i!("routing activation success");

                /* send empty msg to clear queue  */
                {
                    let conn_list = self.async_io.connections.read().await;
                    if let Some(conn) = conn_list.get(&event.cid){
                        let mut queue = conn.message_queue.lock().await;
                        while let Some(data) = queue.pop_front() {
                            let _ = self.async_io.async_write(event.cid, data).await;
                            
                        }
                        *conn.status.lock().await = ConnectionStatus::Connected;
                    }
                    log_i!("connect status change to [ConnectionStatus::Connected]");
                }

                self.async_io.set_activate_status(event.cid, true).await;
                log_w!("connector:{} reconnect success",event.cid);
            }
            ListenerEventType::Disconnection => {
                match map_get_key(&self.async_ip_cid_map, &event.cid){
                    Ok(ip) => {
                        msg.ip = ip;
                        msg.message = format!("ip:{} disconnected",msg.ip);
                    }

                    Err(e) => {
                        log_e!("not found [ip - cid({})] : {}", event.cid, e);
                        return;
                    }
                }

                /* release resources */
                match map_get_value(&self.async_cid_uds_map, &event.cid) {
                    Ok(entity) => {
                        drop(entity);
                    }
                    Err(e) => {
                        log_e!("not found [cid({}) - uds_entity] : {}", event.cid, e);
                        return;
                    }
                };

                /* delete key-value */
                if let Err(e) = map_remove_by_key(&self.async_cid_uds_map, &event.cid) {
                    log_e!("delete async_cid_uds_map with cid{} error: {}", event.cid, e);
                }
                if let Err(e) = map_remove_by_value(&self.async_ip_cid_map, &event.cid) {
                    log_e!("delete async_ip_cid_map with cid{} error: {}", event.cid, e);
                }


                self.listener.notify(mask, msg);
            }
            ListenerEventType::InvalidDoipMsg => {
                self.listener.notify(mask, msg);
            }
            ListenerEventType::SendError => {
                self.listener.notify(mask, msg);
            }
            _ => {

            }
        }
    }

    fn start_event_task(
        self: Arc<Self>,
        mut event_rx: mpsc::Receiver<(ListenerEventType, EventInfo)>
    ) -> tokio::task::JoinHandle<()> {
        tokio::spawn(async move {
            while let Some((event_type, event)) = event_rx.recv().await {
                /* 调用on_event处理事件 */
                self.on_event(event_type, event).await;
            }
        })
    }

    pub fn get_async_cid_uds_map(&self)-> Arc<SyncRwLock<HashMap<Cid, Arc<Mutex<UdsManager>>>>>{
        self.async_cid_uds_map.clone()
    }

    pub fn get_async_ip_cid_map(&self)-> Arc<SyncRwLock<HashMap<String, Cid>>>{
        self.async_ip_cid_map.clone()
    }

    pub fn get_sync_cid_uds_map(&self)-> Arc<SyncRwLock<HashMap<Cid, Arc<Mutex<UdsManager>>>>>{
        self.sync_cid_uds_map.clone()
    }

    pub fn get_sync_ip_cid_map(&self)-> Arc<SyncRwLock<HashMap<String, Cid>>>{
        self.sync_ip_cid_map.clone()
    }


/* async communication */
    pub fn register_on_event(&self, callback: impl Fn(ListenerEventType, ListenerEventInfo) + Send + Sync + 'static){
        self.listener.set_callback(callback);
    }

    pub fn register_listener_event(&self, mask: ListenerEventType){
        self.listener.register_event(mask);
    }

    pub async fn get_reconnect_status(&self, ip: String) -> bool{
        let cid = map_get_value(&self.async_ip_cid_map, &ip);
        match cid {
            Ok(cid) => {
                match self.async_io.check_activate_status(cid).await {
                    Ok(activation) => activation,
                    Err(e) => {
                        log_e!("check activation status error:{}",e);
                        false
                    }
                }
            }
            Err(e) => {
                log_e!("get cid by {} error:{}",&ip, e);
                        false
            }
        }
    } 

    pub async fn async_connect(&self, ip: String, callback_list: ServerCallback)-> Result<()>{
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript(format!("Input ip is invalid")));
        }

        /* create connector */
        let cid_clone:Cid;
        let result = self.async_io.clone().connect(&ip, self.port_).await;
        match result {
            Ok(cid) => {
                cid_clone = cid;
                /* create uds entity */
                let uds_entity = Arc::new(Mutex::new(UdsManager::new()));
                {
                    let uds_entity_lock = uds_entity.lock().await;
                    let _ = uds_entity_lock.init(callback_list).await;
                }

                /* store in hashmap */
                if let Err(e) = map_add_item(&self.async_cid_uds_map, cid, uds_entity){
                    log_e!("bind cid-uds_entity error : {}",e);
                    let _ = self.async_io.close_connector(cid).await;
                    return Err(ErrorCode::AsyncConnectError.descript(e.to_string()));
                }

                if let Err(e) = map_add_item(&self.async_ip_cid_map, ip.clone(), cid) {
                    log_e!("bind ip-cid error : {}",e);
                    let _ = self.async_io.close_connector(cid).await;
                    return Err(ErrorCode::WriteHashmapError.descript(format!("{}", e)));
                };
            }

            Err(e) => {
                log_e!("async_connect error: {}",e);
                return  Err(ErrorCode::AsyncConnectError.descript(e.to_string()))
            }
        }

        /* routing activation */
        let _ = self.send_activate_req(cid_clone).await;
        if let Err(e) = self.get_activate_status(cid_clone).await {
            log_e!("routing activation error: {}",e);

            let _ = self.async_io.close_connector(cid_clone).await;
            return Err(ErrorCode::RoutingActivationError.descript(format!("routing activation error: {}", e)));
        }

        self.async_io.set_activate_status(cid_clone,true).await;
        self.async_io.set_connect_status(cid_clone, ConnectionStatus::Connected).await;
        log_i!("routing activation success");
        log_i!("connect to {}",ip);
        Ok(())
    }

    pub async fn disconnect_async(&self, ip: String)-> Result<()>{
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript("Input ip is invalid".to_string()));
        }

        match map_get_value(&self.async_ip_cid_map, &ip) {
            Ok(cid) => {
                if let Err(e) = self.async_io.close_connector(cid).await {
                    log_e!("async disconnect error: {}",e)
                }
            }

            Err(e) => {
                log_e!("serch cid error: {}",e);
                return Err(ErrorCode::ConnectClosedError.descript(e.to_string()));
            }
        }

        log_d!("disconnect to {}",&ip);
        Ok(())
    }

    pub async fn write_async(&self, msg: OtaDataInfo)-> Result<()>{
        if !ip_valid_check(&msg.ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript("Input ip is invalid".to_string()));
        }

        if msg.uds_req.is_empty() {
            log_e!("Input data is empty");
            return Err(ErrorCode::EmptyMsgError.descript("Input is empty".to_string()));
        }

        /* uds req print */
        // if msg.uds_req[0] == 0x36 {
        //     log_i!("uds req : 0x36 {} + data", msg.uds_req[1]);
        // }else {
        //     log_hex!(Info,"uds req", msg.uds_req.clone());
        // }
        let uds_req = msg.uds_req;

        let req = self.manager.create_diag_request(msg.ta, uds_req.clone());
        match map_get_value(&self.async_ip_cid_map, &msg.ip) {
            Ok(cid) => {
                match map_get_value(&self.async_cid_uds_map, &cid) {
                    Ok(uds_entity_lock) => {
                        let mut entity = uds_entity_lock.lock().await;
                        entity.timer_config(self.async_io.clone(), cid, self.manager.sa_, msg.ta, uds_req.clone()).await;
                    }

                    Err(e) => {
                        log_e!("serch cid-time_manager error: {}",e);
                        return Err(ErrorCode::ConnectClosedError.descript(e.to_string()));
                    }
                }

                /* check connect activate status */
                if let Err(e) = self.get_activate_status(cid).await {
                    log_e!("connector:{} not activate", cid);
                    return Err(ErrorCode::RoutingActivationError.descript(e.to_string()));;
                }

                /* uds req print */
                if uds_req[0] == 0x36 {
                    log_i!("uds req : 0x36 {} + data", uds_req[1]);
                }else {
                    log_hex!(Info,"uds req", uds_req.clone());
                }

                if let Err(e) = self.async_io.async_write(cid, req).await {
                    log_e!("send uds req error: {}",e);
                    return Err(ErrorCode::SendMsgError.descript(e.to_string()));
                }

                /* zcu upgrade: wait ecu reboot finish */
                if msg.mask == 1 {
                    log_w!("set ecu reboot flag");
                    self.async_io.set_activate_status(cid, false).await;
                }
            }

            Err(e) => {
                log_e!("serch cid error: {}",e);
                return Err(ErrorCode::ConnectClosedError.descript(e.to_string()));
            }
        }

        Ok(())
    }

    async fn get_activate_status(&self, cid: Cid)-> std::io::Result<()>{
        /* 查询路由激活状态，超时时间3s(网络层未定义，自定义3s) */
        let start_time = Instant::now();
        let timeout_duration = Duration::from_secs(8);
        let retry_interval = Duration::from_millis(200); // 每次重试间隔200ms
        loop{
            if start_time.elapsed() > timeout_duration {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::TimedOut,
                    "wait routing activation code timeout",
                ));
            }

            match self.async_io.check_activate_status(cid).await {
                Ok(status)=>{
                    if status == true {
                        return Ok(())
                    }else {
                        
                    }
                }

                Err(e) => {}
            }

            tokio::time::sleep(retry_interval).await;
        }
    }

    async fn send_activate_req(&self, cid: Cid) -> std::io::Result<()> {
        let req = self.manager.create_routing_activation_request();
        self.async_io.async_write(cid, req).await
    }
    
/* sync communication  */
    fn sync_parse_doip_msg(&self, msg: Vec<u8>, doip_msg: &mut DoipMessage)-> bool{
        let error_code = self.manager.doip_msg_parse(msg, doip_msg);

        match error_code {
            DoipParseErrorCode::Success => {
                true
            }
            _ => {
                false
            }
        }
    }

    pub fn sync_connect(&self, ip: String, port : u16)-> Result<()>{
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript(format!("Input ip is invalid"))); 
        }

        let cid = self.sync_io.connect(ip.clone(), port);
        match cid {
            Ok(cid) => {
                log_i!("connect to {}",&ip);
                /* 路由激活 */
                if let Err(e) = self.sync_routing_activation(cid) {
                    log_e!("routing activation failed");
                    if let Err(e) = self.sync_io.disconnect(cid) {
                        log_e!("disconnect failed:{}",e);
                    }
                    return Err(ErrorCode::RoutingActivationError.descript(format!("{}", e)));
                }

                log_i!("routing activation success");

                if let Err(e) = map_add_item(&self.sync_ip_cid_map, ip, cid) {
                    log_e!("store connector info failed");
                    if let Err(e) = self.sync_io.disconnect(cid) {
                        log_e!("disconnect failed:{}",e);
                    }
                    return Err(ErrorCode::WriteHashmapError.descript(format!("{}", e)));
                };
            }

            Err(e) => {
                return Err(ErrorCode::SyncConnectError.descript(format!("{}", e)));
            }
        }

        Ok(())
    }

    fn sync_routing_activation(&self, cid: Cid) -> std::io::Result<()> {
        let req = self.manager.create_routing_activation_request();
        let mut resp: Vec<u8> = vec![0u8; 4096];

        /* send routing activation req => MCU */
        if let Err(e) = self.sync_io.write(cid, &req) {
            return Err(std::io::Error::new(
                std::io::ErrorKind::UnexpectedEof,
                format!("routing activation req : send req error - {}", e)));
        }

        /* read routing activation response code */
        let bytes_read = self.sync_io.read(cid, &mut resp);
        match bytes_read {
            Ok(bytes_read) => {
                if bytes_read == 0 {
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::UnexpectedEof,
                        "routing activation resp : receive 0 byte "));
                }
            }

            Err(e) => {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::UnexpectedEof,
                    format!("routing activation resp : receive resp error - {}",e)));
            }
        }
        
        let mut doip_msg = DoipMessage::default();
        let error_code = self.manager.doip_msg_parse(resp, &mut doip_msg);

        match error_code {
            DoipParseErrorCode::Success => {
                if doip_msg.header.payload_type == DoipMsgType::RoutingActivationResponse as u16 {
                    let ra_code = doip_msg.payload[4];
                    match  ra_code{
                        routing_activation_code::ROUTING_SUCCESS => {
                            return Ok(())
                        }

                        _ => {
                            return Err(std::io::Error::new(
                                        std::io::ErrorKind::InvalidData,
                                        format!("routing activation resp : code [{}] != 0x10", ra_code)));
                        }
                    }
                }else {
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        "unmatch doip msg type( is not RoutingActivationResponse)"));
                }
            }

            _ => {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::InvalidData,
                    "routing activation resp : parse doip msg error"));
            }
        }
    }

    pub fn sync_disconnect(&self, ip: String)-> Result<()>{
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript(format!("Input ip is invalid")));
        }

        match map_get_value(&self.sync_ip_cid_map, &ip) {
            Ok(cid) => {
                let result = self.sync_io.disconnect(cid);
                if let Err(e) = result {
                    return Err(ErrorCode::SyncDisconnectError.descript(format!("{}", e)));
                }

                log_i!("{} disconnected", &ip);

                if let Err(e) = map_remove_by_key(&self.sync_ip_cid_map, &ip) {
                    log_e!("remove connect info error:{}",e);
                }

                Ok(())
            }

            Err(e) => {
                return Err(ErrorCode::FindHashmapError.descript(format!("not find connect {} : {}", &ip, e)));
            }
        }
    }
    
    pub fn sync_read(&self, ip: String, recv: &mut DiagnosticMessage) -> Result<usize> {
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript(format!("Input ip is invalid")));
        } 

        let mut read_bytes: usize = 0;
        let cid = map_get_value(&self.sync_ip_cid_map, &ip);
        match cid {
            Ok(cid) => {
                loop{
                    let mut buffer: Vec<u8> = vec![0u8; 4096];
                    let bytes = self.sync_io.read(cid, &mut buffer);
                    match bytes {
                        Ok(0) => {
                            return Err(ErrorCode::ConnectClosedError.descript(format!("server closed connection")));
                        }
                        Ok(bytes) => {
                            crate::debug_hexdump!(&buffer[..bytes]);
                            /* 解析报文 */
                            let mut doip_msg = DoipMessage::default();
                            if self.sync_parse_doip_msg(buffer, &mut doip_msg) == false {
                                println!("doip报文解析出错");
                                return Err(ErrorCode::DoipFormatUnmatchError.descript(format!("parse doip msg error")));
                            }

                            if doip_msg.header.payload_type == DoipMsgType::DiagnosticMessage as u16 {
                                let ret = self.manager.diag_uds_parse(doip_msg.payload, recv);
                                if ret == DoipDiagErrorCode::Success {
                                    read_bytes = recv.data.len();
                                    break;  
                                }else if ret == DoipDiagErrorCode::UdsMsgDelaySend {
                                    continue;
                                }else {
                                    /* 对于uds报文异常的情况，应进行doip否定应答 */
                                    return Err(ErrorCode::ReadMsgError.descript(format!("uds resp msg format error")));
                                }
                            }else {
                                // return Err(ErrorCode::ReadMsgError.descript(format!("Not receive uds resp msg")));
                            }
                        }
                        Err(e) => {
                            return Err(ErrorCode::ReadMsgError.descript(format!("Receive data from server by cid[{}] error: {}", cid, e)));
                        }
                    }
                }

                Ok(read_bytes)
            }

            Err(e) => {
                return Err(ErrorCode::FindHashmapError.descript(format!("{}", e)));
            }
        }
    }

    pub fn sync_write(&self, ip: String, ta: u16, data: Vec<u8>) -> Result<()> {
        if !ip_valid_check(&ip) {
            log_e!("Input ip is invalid");
            return Err(ErrorCode::InvalidIpError.descript(format!("Input ip is invalid")));
        }

        if data.is_empty() {
            return Err(ErrorCode::EmptyMsgError.descript("Input is empty".to_string()));
        }

        let cid = map_get_value(&self.sync_ip_cid_map, &ip);
        match cid {
            Ok(cid) => {
                /* 组装doip报文 */
                let req = self.manager.create_diag_request(ta, data);
                let bytes = self.sync_io.write(cid, &req);
                match bytes {
                    Ok(()) => Ok(()),
                    Err(e) => {
                        return Err(ErrorCode::SendMsgError.descript(format!("Send to server by {} error: {}", &ip, e)));
                    }
                }
            }

            Err(e) => {
                return Err(ErrorCode::FindHashmapError.descript(format!("{}", e)));
            }
        }
    }
}