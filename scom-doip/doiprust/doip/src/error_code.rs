use std::fmt;

// 基础错误码枚举（无默认描述）
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ErrorCode {
    Success = 0x00,
    /* ip无效 */
    InvalidIpError,
    /* 建立tcp同步连接失败 */
    SyncConnectError,
    /* 建立tcp异步连接失败 */
    AsyncConnectError,
    /* 断开tcp同步连接失败 */
    SyncDisconnectError,
    /* 断开tcp异步连接失败 */
    AsyncDisconnectError,
    /* 路由激活失败 */
    RoutingActivationError,
    /* 路由激活响应码错误 */
    UnmatchRoutingActivationCodeError,
    /* 未收到路由激活响应报文 */
    NoRoutingActivationRespError,
    /* 存储<ip,cid>失败 */
    WriteHashmapError,
    /* 查找键值对失败 */
    FindHashmapError,
    /* 删除键值对失败 */
    RemoveHashmapError,
    /* 没有查询到与ip匹配的TCP连接信息 */
    NonexistentConnectError, 
    /* 对端已断开tcp连接 */
    ConnectClosedError,
    /* doip报文解析失败，不符合doip协议格式 */
    DoipFormatUnmatchError,
    /* 待写数据为空 */
    EmptyMsgError,
    /* 数据发送失败 */
    SendMsgError,
    /* 数据读取失败 */
    ReadMsgError,
    /* 发生系统错误 */
    ErrnoError,
}

// 为ErrorCode实现链式调用构造器
impl ErrorCode {
    pub fn descript(self, description:  String) -> DoipError {
        DoipError::new(self, description)
    }
}

#[derive(Debug, Clone)]
pub struct DoipError {
    pub code: ErrorCode,
    pub description: String,
}

impl DoipError {
    pub fn new(code: ErrorCode, description: String) -> Self {
        Self {
            code,
            description,
        }
    }

    pub fn code(&self) -> ErrorCode {
        self.code
    }

    pub fn description(&self) -> String {
        self.description.clone()
    }
}


impl fmt::Display for DoipError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "[{:?}] {}", self.code, self.description)
    }
}

impl std::error::Error for DoipError {}