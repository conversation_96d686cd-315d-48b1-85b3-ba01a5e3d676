use bitflags::bitflags;
pub type Cid = u64;
pub type ListenerCallback = Box<dyn Fn(ListenerEventType, ListenerEventInfo) + Send + Sync + 'static>;
use std::sync::Arc;
/* event info */
bitflags! {
    #[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq)]
    pub struct ListenerEventType: u8 {
        const NoEvent               = 0b00000000;
        const Disconnection         = 0b00000001;
        const InvalidDoipMsg        = 0b00000010;
        const SendError             = 0b00000100;
        const ConnectionTimeout     = 0b00001000;
        const Reconnected           = 0b00010000;

    }
}

#[derive(Default, Debug, Clone)]
pub struct ListenerEventInfo {
    pub ip: String,
    pub message: String,
}

#[derive(Debug, Clone)]
pub enum MsgProperty {
    Normal = 0,
    EcuReboot = 1,
    KeepAlive = 2,
}

/* ota channel info */
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct OtaDataInfo{
    pub ip : String,
    pub ta : u16,
    pub uds_req : Vec<u8>,
    pub mask: u8, //0-normal, 1 - ecu reboot, 2 - heartbeat
}

/* diag channel info */
#[derive(Debug, Clone, Default)]
pub struct DiagDataInfo{
    ip : String,
    ta : u16,
    uds_req : Vec<u8>,
    uds_resp : Vec<u8>,
}

#[derive(Default, Debug, Clone)]
pub struct DiagnosticMessage {
    pub sa: u16,
    pub ta: u16,
    pub data: Vec<u8>,
}

/* uds callback */
pub type UdsCallback = Box<dyn Fn(u16, Vec<u8>) + Send + Sync>;

pub type TimerCallback = Arc<dyn Fn() + Send + Sync + 'static>;

pub struct ServerCallback {
    /* uds 服务回调 */
    pub service_10h_callback: Option<UdsCallback>,  // Diagnostic Session Control
    pub service_11h_callback: Option<UdsCallback>,  // ECU Reset
    pub service_14h_callback: Option<UdsCallback>,  // Clear Diagnostic Information
    pub service_19h_callback: Option<UdsCallback>,  // Read DTC Information
    pub service_22h_callback: Option<UdsCallback>,  // Read Data By Identifier
    pub service_23h_callback: Option<UdsCallback>,  // Read Memory By Address
    pub service_27h_callback: Option<UdsCallback>,  // Security Access
    pub service_28h_callback: Option<UdsCallback>,  // Communication Control
    pub service_2Ah_callback: Option<UdsCallback>,  // Check Programming Dependencies
    pub service_2Ch_callback: Option<UdsCallback>,  // Dynamically Define Data Identifier
    pub service_2Eh_callback: Option<UdsCallback>,  // Write Data By Identifier
    pub service_2Fh_callback: Option<UdsCallback>,  // Input Output Control By Identifier
    pub service_31h_callback: Option<UdsCallback>,  // Routine Control
    pub service_34h_callback: Option<UdsCallback>,  // Request Download
    pub service_36h_callback: Option<UdsCallback>,  // Transfer Data
    pub service_37h_callback: Option<UdsCallback>,  // Request Transfer Exit
    pub service_38h_callback: Option<UdsCallback>,  // Request File Transfer
    pub service_3Dh_callback: Option<UdsCallback>,  // Write Memory By Address
    pub service_3Eh_callback: Option<UdsCallback>,  // Tester Present
    pub service_85h_callback: Option<UdsCallback>,  // Control DTC Settings
    pub service_29h_callback: Option<UdsCallback>,  // Authentication
    pub service_7fh_callback: Option<UdsCallback>,  // Negative resp

    /* 定时器回调 */
    pub timer_resp_callback: Option<TimerCallback>,
    pub timer_nrc78_callback: Option<TimerCallback>, 
}

impl Default for ServerCallback {
    fn default() -> Self {
        ServerCallback {
            service_10h_callback: None,
            service_11h_callback: None,
            service_14h_callback: None,
            service_19h_callback: None,
            service_22h_callback: None,
            service_23h_callback: None,
            service_27h_callback: None,
            service_28h_callback: None,
            service_2Ah_callback: None,
            service_2Ch_callback: None,
            service_2Eh_callback: None,
            service_2Fh_callback: None,
            service_31h_callback: None,
            service_34h_callback: None,
            service_36h_callback: None,
            service_37h_callback: None,
            service_38h_callback: None,
            service_3Dh_callback: None,
            service_3Eh_callback: None,
            service_85h_callback: None,
            service_29h_callback: None,
            service_7fh_callback: None,
            
            timer_nrc78_callback: None,
            timer_resp_callback: None,
        }
    }
}