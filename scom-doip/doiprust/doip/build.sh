#!/bin/bash

set -e

# 常量定义
DEFAULT_PLATFORM="x86"
DEFAULT_MODE="debug"
TARGET_MAP=(
    ["arm"]="x86_64-unknown-linux-gnu"
    ["x86"]="aarch64-unknown-linux-gnu"
)

# 帮助信息
usage() {
    cat <<EOF
Usage: $0 [OPTIONS]
交叉编译脚本（支持x86/arm架构）

Options:
  -x86          编译x86架构 (默认)
  -arm          编译arm64架构
  -d            调试模式 (默认)
  -r            Release模式
  -h, --help    显示帮助信息

示例：
  # 编译arm release版本
  $0 -arm -r

  # 编译x86 debug版本（默认）
  $0

  # 混合参数顺序
  $0 -r -x86
EOF
}

RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# 参数解析
parse_args() {
    local has_platform=0
    local has_mode=0

    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                usage
                exit 0
                ;;
            -x86)
                platform="x86"
                has_platform=1
                ;;
            -arm)
                platform="arm"
                has_platform=1
                ;;
            -d)
                build_mode="debug"
                has_mode=1
                ;;
            -r)
                build_mode="release"
                has_mode=1
                ;;
            *)
                echo "错误：未知参数 '$1'"
                usage
                exit 1
                ;;
        esac
        shift
    done

    # 设置默认值
    platform="${platform:-$DEFAULT_PLATFORM}"
    build_mode="${build_mode:-$DEFAULT_MODE}"

    if [[ $platform == "x86" ]]; then
        toolchain="x86_64-unknown-linux-gnu"
    elif [[ $platform == "arm" ]]; then
        toolchain="aarch64-unknown-linux-gnu"
    else
        echo "未知平台"
        exit 2
    fi

    # 检测冲突参数
    if [[ $has_platform -gt 1 ]]; then
        echo "冲突：不能同时指定多个平台参数"
        usage
        exit 1
    fi

    if [[ $has_mode -gt 1 ]]; then
        echo "冲突：不能同时指定多个编译模式"
        usage
        exit 1
    fi
}

# 编译执行
run_build() {
    local cargo_args=()

    # 构建参数
    cargo_args+=("--target" "$toolchain")
    [[ "$build_mode" == "release" ]] && cargo_args+=(--release)

    cargo clean

    # 执行编译
    if cargo build "${cargo_args[@]}" -vv ;then
        show_build_info "finished" "$GREEN"
    else
        show_build_info "failed" "$RED"
    fi
}

# 输出构建信息
show_build_info() {
    local status=$1
    local color=$2
    local build_dir="target/$toolchain/$build_mode"
    
    echo "---------------------------------------------------------"
    echo -e "  ${color}Compilation ${status}${NC}"
    echo "  platform: $platform ($toolchain)"
    echo "  build mode: $build_mode"
    echo "  target dir: $build_dir"
    echo "---------------------------------------------------------"
}

main() {
    parse_args "$@"
    run_build
}

main "$@"