# 设置 CMake 最低版本要求
cmake_minimum_required(VERSION 3.10)

# 设置项目名称
project(MyProject)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

#源文件
set(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
file(GLOB_RECURSE LIB_SOURCES "${SRC_DIR}/*.cpp")
#回显
message("Final LIB_SOURCES:")
foreach(SOURCE ${LIB_SOURCES})
    message("  ${SOURCE}")
endforeach()

set(INCLUDE_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)

# 头文件路径
file(GLOB_RECURSE HEADER_DIRS LIST_DIRECTORIES true "${INCLUDE_ROOT_DIR}/*")
set(INCLUDE_DIRS ${INCLUDE_ROOT_DIR})
foreach(HEADER_DIR ${HEADER_DIRS})
    if(IS_DIRECTORY ${HEADER_DIR})
        list(APPEND INCLUDE_DIRS ${HEADER_DIR})
    endif()
endforeach()
#回显
message("Final INCLUDE_DIRS:")
foreach(DIR ${INCLUDE_DIRS})
    message("  ${DIR}")
endforeach()

#编译动态库
add_library(doipcxx SHARED ${LIB_SOURCES})
target_include_directories(doipcxx PRIVATE ${INCLUDE_DIRS})

# install
set(CMAKE_INSTALL_PREFIX "${CMAKE_CURRENT_SOURCE_DIR}/install")

install(TARGETS doipcxx
        LIBRARY DESTINATION lib  
)

file(GLOB API_HEADERS "${INCLUDE_ROOT_DIR}/api/*.h" "${INCLUDE_ROOT_DIR}/api/*.hpp")
install(FILES ${API_HEADERS}
        DESTINATION include
)