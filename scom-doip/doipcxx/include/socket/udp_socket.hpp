#pragma once
#include <vector>
#include <string>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>

/*
 * UDP 相关 socket 选项（部分与 TCP 通用）
 */
#define SO_BROADCAST      6  /* 允许发送广播数据报 */
#define IP_MULTICAST_TTL 33  /* 设置组播TTL */
#define IP_ADD_MEMBERSHIP 35 /* 加入组播组 */
#define IP_DROP_MEMBERSHIP 36 /* 离开组播组 */

class UdpClient {
public:
    // 地址信息结构体
    struct SockAddr {
        std::string ip;
        uint16_t port;
        sockaddr_in GetNativeAddr() const {
            sockaddr_in addr{};
            addr.sin_family = AF_INET;
            inet_pton(AF_INET, ip.c_str(), &addr.sin_addr);
            addr.sin_port = htons(port);
            return addr;
        }
    };

public:
    UdpClient();
    ~UdpClient();

    // Socket 配置
    static int SetReusePort(int sockfd, bool enable);
    static int SetBroadcast(int sockfd, bool enable);
    static bool SetNonBlocking(int sockfd);
    static bool SetBlocking(int sockfd);
    
    // Socket 创建
    static int CreateSocket();
    
    // 地址绑定
    static bool Bind(int sockfd, const std::string& local_ip, uint16_t local_port);
    
    // 数据发送
    static ssize_t SendTo(int sockfd, 
                         const std::vector<uint8_t>& buffer,
                         const SockAddr& dest_addr);
    
    // 数据接收                         
    static ssize_t RecvFrom(int sockfd,
                           std::vector<uint8_t>& buffer,
                           SockAddr* src_addr = nullptr);
    
    // 面向连接的 UDP (伪连接)
    static bool Connect(int sockfd, const SockAddr& remote_addr);
    static ssize_t Send(int sockfd, const std::vector<uint8_t>& buffer);
    static ssize_t Recv(int sockfd, std::vector<uint8_t>& buffer);
    
    // 资源释放
    static bool Close(int sockfd);
    
    // 组播控制
    static bool JoinMulticastGroup(int sockfd, 
                                  const std::string& multicast_ip,
                                  const std::string& local_ip);
    static bool LeaveMulticastGroup(int sockfd,
                                   const std::string& multicast_ip,
                                   const std::string& local_ip);
};