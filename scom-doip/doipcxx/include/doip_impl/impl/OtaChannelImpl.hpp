#pragma once
#include <string>
#include <functional>
#include <shared_mutex>
#include "TcpAsyncManager.hpp"
#include "type.hpp"
#include "Interface.hpp"
#include "DoipManager.hpp"
#include "Listener.hpp"

class OtaChannelImpl : public IDoipChannel<OtaDataInfo>{
public:
    explicit OtaChannelImpl(uint16_t SA, int port);

    void connect(const std::string& ip) override;
    void disconnect(const std::string& ip) override;
    void connectAsync(const std::string& ip) override;
    void disconnectAsync(const std::string& ip) override;
    void closeChannel() override;
    
    uint32_t read(OtaDataInfo& payload) override;
    void write(const OtaDataInfo& payload) override;
    /**
     * 异步写数据接口
     * @param msg   用户数据
     */
    void writeAsync(const OtaDataInfo& msg) override;
    /**
     * 异步通信回调注册
     * @param callback   回调函数
     */
    void registerCallback(const CallbackType& callback) override;

    void registerListener(const ListenerCallback& callback) override;
    void registerListenerEvent(ListenerEventType mask) override;
private:
    int port_;
    CallbackType callback_;
    Listener& listener_;
    TcpAsyncManager& async_io_;
    DoipManager manager_;
    std::shared_mutex rw_mutex_; //优化点：一把锁会锁住所有映射表，而针对不同的映射表可以同时进行读写
    std::unordered_map<std::string, int> async_ipsock_map_;
    std::unordered_map<std::string, int> sync_ipsock_map_;

    /**
     * 读写层回调接口
     * @param data   收到的doip报文数据
     */
    void callbackHandler(int socket, const std::vector<uint8_t>& data);

    /**
     * 异常事件监听回调接口
     * @param ListenerEventType   事件掩码
     * @param EventInfo           事件数据结构
     */
    void eventListener(ListenerEventType mask, const EventInfo& event);


    /**
     * 组装doip报文，发送数据
     * @param write_info   
     */
    void writeProcess(const WriteDataInfo& write_info);
    /**
     * 向doip实体发送路由激活请求并获取激活状态（同步）
     * @param sockFd  
     */
    void routingActivation(int32_t sockFd);
    /**
     * doip诊断报文处理
     * @param sockFd  
     * @return 激活成功-true
     */
    bool doipMessageHandleAsync(int socket, const std::vector<uint8_t>& data, DoipMsgInfo& doip_msg);
    bool doipMessageHandle(int socket, const std::vector<uint8_t>& data, DoipMsgInfo& doip_msg);
    DoipDiagErrorCode udsMessageParse(const DoipMsgInfo& doip_msg, std::vector<uint8_t>& uds_resp);
};