#pragma once
#include <vector>
#include <queue>
#include <unordered_map>
#include <mutex>
#include <thread>
#include <atomic>
#include <functional>
#include "type.hpp"

class TcpAsyncManager {
public:
    //单例
    static TcpAsyncManager& getInstance();

    // 删除拷贝构造函数，防止通过拷贝创建新实例
    TcpAsyncManager(const TcpAsyncManager&) = delete;
    // 删除赋值运算符，防止通过赋值创建新实例
    TcpAsyncManager& operator=(const TcpAsyncManager&) = delete;

    ~TcpAsyncManager();

    using Callback = std::function<void(int, const std::vector<uint8_t>&)>;
    void registerCallback(Callback callback);
    /**
     * 注册socket
     * @param socket_fd   TCP连接socket
     */
    void registerSocket(int socket_fd);
    /**
     * 注销socket并清理资源
     * @param socket_fd 要关闭的socket
     */
    void unregisterSocket(int socket_fd);

    /**
     * 注册事件推送回调函数
     * @param callback    读数据回调函数
     */
    using EventPush = std::function<void(ListenerEventType,const EventInfo&)>;
    void registerEventListener(EventPush callback);

    /**
     * 异步发送数据（线程安全）
     * @param socket_fd 目标socket
     * @param data      要发送的二进制数据
     */
    void asyncWrite(int socket_fd, const std::vector<uint8_t>& data);

private:
    // 构造函数设为私有，防止外部实例化
    TcpAsyncManager();
    EventPush event_cb_;

    // 静态成员变量声明
    static std::atomic<TcpAsyncManager*> instance;
    static std::mutex mutex_;

    //--------------- 读线程相关 ---------------
    fd_set read_fds_;              // 读文件描述符集合
    std::mutex read_fds_mutex_;    // 保护read_fds_
    int max_read_fd_ = 0;          // select需要的最大文件描述符

    //--------------- 写线程相关 ---------------
    fd_set write_fds_;             // 写文件描述符集合
    std::mutex write_fds_mutex_;   // 保护write_fds_
    int max_write_fd_ = 0;

    //--------------- 共享数据 ---------------
    std::atomic<bool> stop_;       // 停止标志
    Callback cb_;   // channel层回调
    std::mutex socket_mutex_;
    std::mutex callbacks_mutex_;   // 回调注册互斥锁

    // 发送队列数据结构（每个socket独立队列）
    std::unordered_map<int, std::queue<std::vector<uint8_t>>> send_queues_;
    std::mutex send_queues_mutex_; // 消息队列互斥锁

    std::thread read_thread_;      // 读线程
    std::thread write_thread_;     // 写线程

    //--------------- 读线程操作 ---------------
    void addToReadSet(int fd);
    void removeFromReadSet(int fd);
    // 读线程主循环
    void readThreadFunc();

    //--------------- 写线程操作 ---------------
    void addToWriteSet(int fd);
    void removeFromWriteSet(int fd);
    // 写线程主循环
    void writeThreadFunc();
};