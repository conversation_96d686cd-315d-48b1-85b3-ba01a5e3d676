#pragma once
#include <string>
#include <unordered_map>
#include <shared_mutex>

class General
{
private:
    /* data */
public:
    static bool ipv4ValidCheck(const std::string& ip);
    static bool ipv6ValidCheck(const std::string& ip);
    static bool ipValidCheck(const std::string& ip);
    static void mapAddItem(std::unordered_map<std::string, int>& map, const std::string& ip, int socket, std::shared_mutex& mutex);
    static int mapFindItem(std::unordered_map<std::string, int>& map,const std::string& ip, std::shared_mutex& mutex);
    static std::string mapFindItem(std::unordered_map<std::string, int>& map,int socket, std::shared_mutex& mutex);
    static void mapRemoveItem(std::unordered_map<std::string, int>& map, const std::string& ip, std::shared_mutex& lock);
    static void mapRemoveItem(std::unordered_map<std::string, int>& map, int socket_fd, std::shared_mutex& mutex);
};