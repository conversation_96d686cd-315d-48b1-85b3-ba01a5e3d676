#pragma once
#include <vector>
#include "type.hpp"
#include "ErrorCode.hpp"
#include "Listener.hpp"
#include <mutex>
#include <functional>

class DoipManager
{
public:
    DoipManager(uint16_t SA);
    ~DoipManager();
    
    std::vector<uint8_t> createDiagReqMsg(uint16_t TA, std::vector<uint8_t> uds_msg);
    void DoipMsgRespHandle(int socket, const DoipMsgInfo doip_msg);
    /* 路由激活 */
    std::vector<uint8_t> createRoutingActivationReqMsg();
    void routingActivationRespHandle(uint8_t ra_type);

    /**
     * doip报文解析
     * @param msg  数据流
     * @param doip_msg doip报文结构
     * @return 成功-SUCCESS
     */
    DoipParseErrorCode doipMsgParse(const std::vector<uint8_t>& msg, DoipMsgInfo& doip_msg);
    DoipDiagErrorCode diagParse(const std::vector<uint8_t>& msg, std::vector<uint8_t>& uds_resp);
private:
    

    /* doip报文组包接口 */
    
    std::vector<uint8_t> createAliveCheckRespMsg();
    std::vector<uint8_t> createDoipEntityStatusReqMsg();
    std::vector<uint8_t> createPowerModeReqMsg();

    /* doip诊断 */
    bool targetAddrCheck(uint16_t TA);
    DoipDiagErrorCode doipDiagCheck(uint16_t SA, uint16_t TA);

    void sendAliveCheckResponse(int32_t sockFd);

    DoipMsgInfo doip_msg_;
    const uint16_t SA_;
    std::mutex ra_status_mutex_; //路由激活状态锁,在多线程并发连接时保证线程安全
};




