#pragma once
#include <string>

class DoipUdp {
public:
    struct BroadcastConfig {
        std::string dest_ip;
        uint16_t port; 
        int repeat = 1;     // 默认发送1次
        int interval_sec = 0; // 默认间隔0秒

        BroadcastConfig(std::string ip, uint16_t p)
            : dest_ip(std::move(ip)), port(p) {}
    };

    /**
     * @brief 发送UDP广播消息（支持任意二进制数据）
     * @param data 消息内容容器（std::vector<uint8_t>）
     * @param dest_ip 广播地址
     * @param port 目标端口
     * @param repeat 重复发送次数（0=持续）
     * @param interval_sec 发送间隔（秒）
     * @return 成功发送次数（-1=参数错误，-2=套接字错误，-3=设置广播失败）
     */
    static int SendBroadcast(const std::vector<uint8_t>& data, 
                             const BroadcastConfig& config);
};