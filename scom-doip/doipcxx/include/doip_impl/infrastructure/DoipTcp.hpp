#pragma once
#include "type.hpp"

class DoipTcp
{
private:
    /* data */
public:
    DoipTcp(/* args */);
    ~DoipTcp();

    static int connect(const std::string& ip, int port);
    static bool disconnect(int socket);
    static bool setSocketNonBlock(int sockfd);
    static ssize_t read(int32_t socket, std::vector<uint8_t>& buffer, size_t bufferSize);
    static ssize_t write(int32_t socket, const std::vector<uint8_t> buffer);
};

