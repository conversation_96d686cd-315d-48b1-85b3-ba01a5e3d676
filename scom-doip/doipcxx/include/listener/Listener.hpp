#pragma once
#include <memory>
#include <mutex>
#include <functional>
#include "format.hpp"

class Listener 
{
public:
    static Listener& getInstance();

    using ListenerCallback = std::function<void(ListenerEventType , ListenerEventInfo)>;
    void setListener(const ListenerCallback& cb);

    void Notify(ListenerEventType mask, const ListenerEventInfo event);

    void RegisterEvent(ListenerEventType mask);

private:
    ListenerEventType mask_;
    ListenerCallback cb_;
    static std::mutex mutex_;
    static std::atomic<Listener*> instance;

    Listener() : mask_(ListenerEventType::NoEvent) {}
    Listener(const Listener&) = delete;
    Listener& operator=(const Listener&) = delete;
};

