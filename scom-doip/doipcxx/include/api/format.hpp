#pragma once
#include <string>
#include <vector>
#include <cstdint>

typedef struct{
    std::string ip; //写数据时的对端ip
    unsigned short TA;
    std::vector<unsigned char> uds_req; 
    std::vector<unsigned char> uds_resp; 
}OtaDataInfo;

typedef struct{
    std::string ip; //写数据时的对端ip
    unsigned short TA;
    std::vector<unsigned char> uds_req; 
    std::vector<unsigned char> uds_resp; 
}ObdDataInfo;
typedef struct{
    std::string ip; //写数据时的对端ip
    unsigned short TA;
    std::vector<unsigned char> uds_req; 
    std::vector<unsigned char> uds_resp; 
}DiagDataInfo;

typedef struct{
    unsigned char mask;
    std::string ip;
    std::string message;
}ListenerEventInfo;

enum class ListenerEventType : uint8_t {
    NoEvent          = 0x00,
    Disconnection  = 0x01,  // TCP连接断开
    InvalidDoipMsg       = 0x02,  // 消息解析错误
    SendError      = 0x04,  // 发送失败
    ConnectionTimeout   = 0x08,  // 连接超时
};

// 位操作符重载
constexpr ListenerEventType operator|(ListenerEventType a, ListenerEventType b) {
    return static_cast<ListenerEventType>(
        static_cast<uint8_t>(a) | static_cast<uint8_t>(b));
}

constexpr bool operator&(ListenerEventType a, ListenerEventType b) {
    return static_cast<bool>(
        static_cast<uint8_t>(a) & static_cast<uint8_t>(b));
}

constexpr ListenerEventType& operator|=(ListenerEventType& a, ListenerEventType b) {
    a = static_cast<ListenerEventType>(
        static_cast<uint8_t>(a) | static_cast<uint8_t>(b));
    return a;
}

constexpr ListenerEventType& operator&=(ListenerEventType& a, ListenerEventType b) {
    a = static_cast<ListenerEventType>(
        static_cast<uint8_t>(a) & static_cast<uint8_t>(b));
    return a;
}
