#pragma once
#include <string>
#include <sstream>
#include <cstdint>

enum class DoipErrorCode : u_int8_t{
    Success = 0x00,
    /* ip无效 */
    InvalidIpError,
    /* 建立tcp同步连接失败 */
    SyncConnectError,
    /* 建立tcp异步连接失败 */
    AsyncConnectError,
    /* 路由激活失败 */
    RoutingActivationError,
    /* 路由激活响应码错误 */
    UnmatchRoutingActivationCodeError,
    /* 未收到路由激活响应报文 */
    NoRoutingActivationRespError,
    /* 没有查询到与ip匹配的TCP连接信息 */
    NonexistentConnectError, 
    /* 对端已断开tcp连接 */
    ConnectClosedError,
    /* doip报文解析失败，不符合doip协议格式 */
    DoipFormatUnmatchError,
    /* 待写数据为空 */
    EmptyMsgError,
    /* 发生系统错误 */
    ErrnoError,

};

class DoipException : public std::exception {
private:
    std::string error_message;
    DoipErrorCode error_code = DoipErrorCode::Success; // 默认初始化为0

public:
    
    DoipException(const std::string& msg) : error_code(DoipErrorCode::Success) {
        std::ostringstream oss;
        oss << "[Doip exception] " << msg;
        error_message = oss.str();
    }

    DoipException(const std::string& msg, const char* function, int line) : error_code(DoipErrorCode::Success) {
        std::ostringstream oss;
        oss << "[Doip] " << function << "()" << " - " << line << " : " << msg;
        error_message = oss.str();
    }

    /* use this */
    DoipException(DoipErrorCode code, const std::string& msg) : error_code(code) {
        std::ostringstream oss;
        oss << "[Doip exception 0x" << std::hex << static_cast<int>(code) << "] " << msg;
        error_message = oss.str();
    }

    DoipErrorCode getErrorCode() const noexcept {
        return error_code;
    }

    const char* what() const noexcept override {
        return error_message.c_str();
    }
};


