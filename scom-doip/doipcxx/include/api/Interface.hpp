#pragma once
#include <functional>
#include <memory>
#include "format.hpp"

template <typename T>
class IDoipChannel {
public:
    virtual ~IDoipChannel() = default;

    virtual void connect(const std::string& ip) = 0;
    virtual void disconnect(const std::string& ip) = 0;
    virtual void connectAsync(const std::string& ip) = 0;
    virtual void disconnectAsync(const std::string& ip) = 0;
    virtual void closeChannel() = 0;
    
    virtual uint32_t read(T& payload) = 0;
    virtual void write(const T& payload) = 0;
    virtual void writeAsync(const T& payload) = 0;
    
    using CallbackType = std::function<void(const std::vector<uint8_t>&)>;
    virtual void registerCallback(const CallbackType& callback) = 0;
    using ListenerCallback = std::function<void(ListenerEventType , ListenerEventInfo)>;
    virtual void registerListener(const ListenerCallback& callback) = 0;

    virtual void registerListenerEvent(ListenerEventType mask) = 0;
}; 