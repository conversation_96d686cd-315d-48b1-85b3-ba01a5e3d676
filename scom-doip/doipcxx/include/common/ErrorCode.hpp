#pragma once

/* DoIP诊断报文否定响应的错误码 */
enum class DoipDiagErrorCode : uint8_t{
    SUCCESS = 0x00,
    INVALID_SOURCE_ADDRESS = 0x02,
    UNKNOWN_TARGET_ADDRESS = 0x03,
    DIAGNOSTIC_MESSAGE_TOO_LARGE = 0x04,
    OUT_OF_MEMORY = 0x05,
    TARGET_UNREACHABLE = 0x06,
    UNKNOWN_NETWORK = 0x07,
    TRANSPORT_PROTOCOL_ERROR = 0x08,
    UDS_MSG_DELAY = 0X09
};
class DoipDiagErrorHandler {
public:
    static std::string GetDoipDiagECodeDescription(DoipDiagErrorCode code) {
        switch (code) {
            case DoipDiagErrorCode::SUCCESS:
                return "No error";
            case DoipDiagErrorCode::INVALID_SOURCE_ADDRESS:
                return "Invalid source address 无效源地址";
            case DoipDiagErrorCode::UNKNOWN_TARGET_ADDRESS:
                return "Unknown target address 未知目标地址";
            case DoipDiagErrorCode::DIAGNOSTIC_MESSAGE_TOO_LARGE:
                return "Diagnostic message too large 诊断报文过长";
            case DoipDiagErrorCode::OUT_OF_MEMORY:
                return "Out of memory 内存不足";
            case DoipDiagErrorCode::TARGET_UNREACHABLE:
                return "Target unreachable 目标不可达";
            case DoipDiagErrorCode::UNKNOWN_NETWORK:
                return "Unknown network 未知的网络";
            case DoipDiagErrorCode::TRANSPORT_PROTOCOL_ERROR:
                return "Transport protocol error 传输协议错误";
            default:
                return "Unknown error code";
        }
    }
};


/* DoIP路由激活报文响应码 */
enum class RoutingActivationErrorCode : uint8_t {
    UNKNOWN_SOURCE_ADDRESS = 0x00,
    ALL_SOCKETS_ACTIVE = 0x01,
    DIFFERENT_SA_RECEIVED = 0x02,
    SA_REGISTERED_ELSEWHERE = 0x03,
    MISSING_AUTHENTICATION = 0x04,
    REJECTED_CONFIRMATION = 0x05,
    UNSUPPORTED_ROUTING_TYPE = 0x06,
    ROUTING_SUCCESS = 0x10,
    CONFIRMATION_REQUIRED = 0x11
};

class RoutingActivationErrorHandler {
public:
    // 静态成员函数，用于获取路由激活错误码的描述信息
    static std::string getRoutingActivationErrorDescription(RoutingActivationErrorCode code) {
        switch (code) {
            case RoutingActivationErrorCode::UNKNOWN_SOURCE_ADDRESS:
                return "[ErrorCode: UNKNOWN_SOURCE_ADDRESS] - Unknown source address";
            case RoutingActivationErrorCode::ALL_SOCKETS_ACTIVE:
                return "[ErrorCode: ALL_SOCKETS_ACTIVE] - All concurrently supported TCP_DATA sockets are registered and active";
            case RoutingActivationErrorCode::DIFFERENT_SA_RECEIVED:
                return "[ErrorCode: DIFFERENT_SA_RECEIVED] - The source logical address received is different from that of the already activated TCP_DATA socket connection entry";
            case RoutingActivationErrorCode::SA_REGISTERED_ELSEWHERE:
                return "[ErrorCode: SA_REGISTERED_ELSEWHERE] - The source logical address is already registered and active on a different TCP_DATA socket";
            case RoutingActivationErrorCode::MISSING_AUTHENTICATION:
                return "[ErrorCode: MISSING_AUTHENTICATION] - Missing authentication";
            case RoutingActivationErrorCode::REJECTED_CONFIRMATION:
                return "[ErrorCode: REJECTED_CONFIRMATION] - Confirmation is rejected";
            case RoutingActivationErrorCode::UNSUPPORTED_ROUTING_TYPE:
                return "[ErrorCode: UNSUPPORTED_ROUTING_TYPE] - Unsupported routing activation type";
            case RoutingActivationErrorCode::ROUTING_SUCCESS:
                return "[ErrorCode: ROUTING_SUCCESS] - Routing successfully activated";
            case RoutingActivationErrorCode::CONFIRMATION_REQUIRED:
                return "[ErrorCode: CONFIRMATION_REQUIRED] - Confirmation is required to activate communication.";
            default:
                return "[ErrorCode: UNKNOWN] - Unknown error code";
        }
    }
};

enum class DoipParseErrorCode : uint8_t{
    SUCCESS = 0x00,
    ILLEGAL_DATA,
    PAYLOAD_UNMATCH,
};


enum class ConvErrorCode : uint8_t{
    SUCCESS = 0,
    SHORT_DOIP_HEADER,
    DOIP_PAYLOAD_LEN_UNMATCH,
    EMPTY_DOIP_PAYLOAD,
    SHORT_UDS_PAYLOAD
};