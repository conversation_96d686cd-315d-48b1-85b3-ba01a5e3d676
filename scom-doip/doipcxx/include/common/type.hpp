#pragma once

#include "format.hpp"

/* doip msg format */
typedef struct
{
    uint8_t version;
    uint8_t inverse_version;
    uint16_t payload_type;
    uint32_t payload_length;
}DoipHeadInfo;

typedef struct
{
    DoipHeadInfo head;
    std::vector<uint8_t> payload;
}DoipMsgInfo;

/* write msg format */
typedef struct  {
    std::string ip; //请求节点ip
    uint16_t TA;
    std::vector<uint8_t> uds_req; //uds数据
}WriteDataInfo;

/* event format */
typedef struct  {
    int socket;
    int ern;
}EventInfo;