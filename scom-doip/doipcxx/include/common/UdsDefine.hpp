#pragma once
#include "type.hpp"
#include <vector>

enum class UDSServiceID {
    DiagnosticSessionControl = 0x10,
    EcuReset                = 0x11,
    ClearDiagnosticInfo     = 0x14,
    ReadDTCInformation     = 0x19,
    ReadDataByIdentifier   = 0x22,
    ReadMemoryByAddress    = 0x23,
    SecurityAccess         = 0x27,
    CommunicationControl   = 0x28,
    ReadDataByPeriodicId   = 0x2A,
    DynamicDataDefinition  = 0x2C,
    WriteDataByIdentifier  = 0x2E,
    InputOutputControl     = 0x2F,
    RoutineControl         = 0x31,
    RequestDownload        = 0x34,
    TransferData           = 0x36,
    RequestTransferExit    = 0x37,
    RequestFileTransfer    = 0x38,
    WriteMemoryByAddress   = 0x3D,
    TesterPresent          = 0x3E,
    ControlDTCSetting      = 0x85,
    Authentication         = 0x29
};

typedef struct {
    bool isPositive;
    UDSServiceID serviceID;
    uint8_t subFunction;
    std::vector<uint8_t> responseData;
    uint8_t negativeResponseCode;
}UdsRespInfo;

