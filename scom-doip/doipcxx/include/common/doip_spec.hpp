#pragma once

/********************************************* doip payload type value ***************************************/
typedef enum{
    HEADER_NEGATIVE_ACK = 0x0000,
    VEHICLE_IDENT_REQUEST,  //udp
    VEHICLE_IDENT_REQUEST_EID,  //udp
    VEHICLE_IDENT_REQUEST_VIN,  //udp
    VEHICLE_IDENT_RESPONSE, //udp //can also be 'vehicle announcement message' 
    ROUTING_ACTIVATION_REQUEST,
    ROUTING_ACTIVATION_RESPONSE,
    ALIVE_CHECK_REQUEST,
    ALIVE_CHECK_RESPONSE,

    /* 0x0009 ~ 0x4000 Reserved */

    DOIP_ENTITY_STATUS_REQUEST = 0x4001,
    DOIP_ENTITY_STATUS_RESPONSE,
    DIAG_PM_INFO_REQUEST,
    DIAG_PM_INFO_RESPONSE,

    /* 0x4005 ~ 0x8000 Reserved */

    DIAGNOSTIC_MESSAGE = 0x8001,
    DIAGNOSTIC_POSITIVE_ACK,
    DIAGNOSTIC_NEGATIVE_ACK,

    /* 0x8004 ~ 0xEFFF Reserved */
    /* 0xF000 ~ 0xFFFF Reserved for manufacturerspecific use*/
}PayloadType;

/************************************** doip header part **********************************/
/* header format:
*   [ doip header: 8 bytes  ]
*   [02 FD 00 00 00 00 00 00]
*   protocol version                 ----->   1 byte
*   inverse protocol version        ----->   1 byte
*   payload type                     ----->   2 bytes
*   payload len                      ----->   4 bytes
 */
#define PROTOCOL_VERSION_LEN    (1)
#define INVERSE_PROTOCOL_VERSION_LEN   (1)
#define PAYLOAD_TYPE_LEN    (2)
#define PAYLOAD_DATA_LEN    (4)
#define GENERIC_HEADER_LEN  (PROTOCOL_VERSION_LEN + INVERSE_PROTOCOL_VERSION_LEN + PAYLOAD_TYPE_LEN + PAYLOAD_DATA_LEN)

/* header  nack format:
*   [  doip header: 8 bytes ] [NACK code : 1 byte]
*   [02 FD 00 00 00 00 00 01] [00]
*   protocol version                 ----->   1 byte
*   inverse protocol version         ----->   1 byte
*   payload type                     ----->   2 bytes
*   payload len                      ----->   4 bytes
*   nack code                        ----->   1 byte
 */
#define HEADER_NACK_CODE_LEN    (1)
typedef enum{
    INCORRECT_FORMAT_CODE = 0x00,
    UNKNOWN_PAYLOAD_TYPE_CODE,
    MSG_TOO_LARGE_CODE,
    OUT_OF_MEMORY_CODE,
    INVAILD_PAYLOAD_LEN_CODE,
}HEADER_NACK_CODE;


/****************** doip  vehicle announcement/vehicle ident msg part*********************/
/* request format :
*   [           doip header: 8 bytes       ] [payload : 0x00/06/11 byte]
*   [02 FD 0001/0002/0003 00 00 00 00/06/11] [00 ...]
* 
*   header                  ----->   8 bytes
*   payload type:  
*           0x0001         ----->   payload is 0x00 byte
*           0x0002(EID)    ----->   payload is 0x06 byte
*           0x0002(VIN)    ----->   payload is 0x11 byte
*/
#define EID_SIZE    (6)
#define GID_SIZE    (6)
#define VIN_SIZE    (17)

#define VEHICLE_IDENT_REQ_LEN   (0x00)
#define VEHICLE_IDENT_EID_REQ_LEN   (0x06)
#define VEHICLE_IDENT_VIN_REQ_LEN   (0x11)

/* response format :
*   [ doip header: 8 bytes  ] [payload : 0x20 byte]
*   [02 FD 00 04 00 00 00 20] [00 ...]
* 
*   header                      ----->   8 bytes
*   VIN                         ----->   17 bytes
*   DoIP entity logical address ----->   2 bytes
*   EID                         ----->   6 bytes
*   GID                         ----->   6 bytes
*   further action              ----->   1 bytes
*   VIN/GID sync status(optional) ----->   4 bytes 
*/
#define VEHICLE_IDENT_RESPONSE_LEN  (32)

/********************************* doip routing activation msg part **************************/
/* request format :
*   [   doip header: 8 bytes   ] [payload : 07/0B byte]
*   [02 FD 00 05 00 00 00 07/0B] [00 ...]
* 
*   header                  ----->   8 bytes
*   source address          ----->   2 bytes
*   activation type         ----->   1 bytes
*   reserved by iso13400-2  ----->   4 bytes
*   (optional)reserved      ----->   4 bytes
*/
#define ROUTING_ACTIVATION_REQUEST_LEN (15)

typedef enum{
    RA_DEFAULT = 0x00,
    RA_WWH_OBD = 0x01,

    /* 02~ DF ISO/SAE reserved */

    CENTRAL_SECURITY = 0xE0,    //optional

    /* E1~ FF Available for additional VM-specific use */
}ROUTING_ACTIVATION_TYPE;

/* response format :
*   [   doip header: 8 bytes   ] [payload : 09/0D byte]
*   [02 FD 00 05 00 00 00 09/0D] [00 ...]
* 
*   header                                   ----->   8 bytes
*   Logical address of client DoIP entity    ----->   2 bytes
*   Logical address of DoIP entity           ----->   2 bytes
*   Routing activation response code         ----->   1 bytes
*   reserved by iso13400-2                   ----->   4 bytes
*   (optional)reserved                       ----->   4 bytes
*/
#define CLIENT_LOGICAL_ADDR_LEN (2)
#define DOIP_ENTITY_LOGICAL_ADDR_LEN (2)
#define ROUTE_ACTIVE_RESP_CODE_LEN (1)
#define RESERVED_PART_LEN   (4)
#define ROUTING_ACTIVATION_RESP_LEN (CLIENT_LOGICAL_ADDR_LEN + DOIP_ENTITY_LOGICAL_ADDR_LEN + \
                                    ROUTE_ACTIVE_RESP_CODE_LEN + RESERVED_PART_LEN)

enum class RouteActiveRespCode {
    UNKNOWN_SA = 0x00,
    ALL_SOCKET_ACTIVE,
    SA_UNMATCH,
    SOCKET_UNMATCH,
    MISS_IDENT,    // optional
    REJECTED_CONFIRMATION,  // optional
    UNSUPPORTED_ACTIVATION_TYPE,
    NON_TLS_SOCKET,     // optional

    // 08~ 0F reserved by iso13400 - 2

    RAOUTING_SUCCESSFULLY_ACTIVATED = 0x10,   
    ROUTING_ACTIVATION_CONFIRMATION_REQUIRED,  // optional

    // 12~ FF reserved 
};

/********************************* doip alive check part **************************/
/* response format :
*   [ doip header: 8 bytes  ] [payload : 2 byte]
*   [02 FD 00 08 00 00 00 02] [00 00]
* 
*   header                                   ----->   8 bytes
*   Logical address of client DoIP entity    ----->   2 bytes
*/
#define ALIVE_CHECK_RESPONSE_LEN    (11)

/********************************* doip diag msg and ack part **************************/
/* request format :
*   [  doip header: 8 bytes ] [payload : n byte]
*   [02 FD 80 01 00 00 00 00] [00 ...]
* 
*   header                  ----->   8 bytes
*   source address          ----->   2 bytes
*   target address          ----->   2 bytes
*   user data               ----->   n bytes (uds)
*/
#define DOIP_DIAG_MSG_LEN(uds_len)  (8+2+2+uds_len)


/* diag msg ack format */
/* 
*   header          ---->    8 bytes
*   source address  ---->    2 bytes
*   target address  ---->    2 bytes
*   user data       ---->    n byte
 */
#define DIAG_MSG_SA_LEN (2)
#define DIAG_MSG_TA_LEN (2)
#define DIAG_MSG_SA_TA_LEN  (DIAG_MSG_SA_LEN + DIAG_MSG_TA_LEN)
#define DIAG_PAYLOAD_OFFSET (GENERIC_HEADER_LEN + DIAG_MSG_SA_TA_LEN)

/* diag msg positive/negative ack format */
/* 
*   header          ---->    8 bytes
*   source address  ---->    2 bytes
*   target address  ---->    2 bytes
*   ACK code        ---->    1 byte
*   (optional)Previous diagnostic message data  ---->    n bytes
 */
#define DIAG_MSG_ACK_CODE_LEN   (1)
#define DIAG_ACK_LEN   (DIAG_MSG_SA_TA_LEN + DIAG_MSG_ACK_CODE_LEN)

typedef enum{
    /* positive ack */
    DIAG_VALID_MSG_CODE = 0x00,

    /* negative ack */

    /* 0x00 ~ 0x01 reserved by iso13400-2 */

    DIAG_INVALID_SA_CODE = 0x02,
    DIAG_UNKNOW_TA_CODE,
    DIAG_MSG_TOO_LARGE_CODE,
    DIAG_OUT_OF_MEMORY_CODE,
    DIAG_TARGET_UNREACHABLE_CODE,
    DIAG_UNKNOWN_NETWORK_CODE,
    DIAG_TRANSPORT_PROTOCOL_ERROR_CODE,

    /* 09 ~ FF Reserved by iso13400-2 */

}DIAG_ACK_CODE;

/* data struct define */
typedef struct{
    PayloadType type;
    unsigned char value;
}GenericHeaderAction;