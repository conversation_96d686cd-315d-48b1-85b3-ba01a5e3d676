#include "TcpAsyncManager.hpp"
#include "DoipTcp.hpp"
#include <sys/select.h>
#include <sys/socket.h>
#include <unistd.h>
#include <fcntl.h>
#include <cstring>
#include <iostream>

std::atomic<TcpAsyncManager*> TcpAsyncManager::instance{nullptr};
std::mutex TcpAsyncManager::mutex_;

TcpAsyncManager& TcpAsyncManager::getInstance() {
    TcpAsyncManager* tmp = instance.load(std::memory_order_relaxed);
    std::atomic_thread_fence(std::memory_order_acquire);
    if (tmp == nullptr) {
        std::lock_guard<std::mutex> lock(mutex_);
        tmp = instance.load(std::memory_order_relaxed);
        if (tmp == nullptr) {
            tmp = new TcpAsyncManager();
            std::atomic_thread_fence(std::memory_order_release);
            instance.store(tmp, std::memory_order_relaxed);
        }
    }
    return *tmp;
}

TcpAsyncManager::TcpAsyncManager(){
    stop_.store(false);
    read_thread_ = std::thread(&TcpAsyncManager::readThreadFunc, this);
    write_thread_ = std::thread(&TcpAsyncManager::writeThreadFunc, this);
}

TcpAsyncManager::~TcpAsyncManager(){
    stop_.store(true);
    if (read_thread_.joinable()) read_thread_.join();
    if (write_thread_.joinable()) write_thread_.join();
}

void TcpAsyncManager::registerEventListener(EventPush callback) {
    event_cb_ = callback;
}

void TcpAsyncManager::registerCallback(Callback callback){
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    cb_ = callback;
}

void TcpAsyncManager::registerSocket(int socket_fd) {
    std::lock_guard<std::mutex> lock(socket_mutex_);
    DoipTcp::setSocketNonBlock(socket_fd);
    addToReadSet(socket_fd);
}

void TcpAsyncManager::unregisterSocket(int socket_fd) {
    {
        std::lock_guard<std::mutex> lock(send_queues_mutex_);
        send_queues_.erase(socket_fd);
    }
    removeFromReadSet(socket_fd);
}

void TcpAsyncManager::asyncWrite(int socket_fd, const std::vector<uint8_t>& data) {
    std::lock_guard<std::mutex> lock(send_queues_mutex_);
    send_queues_[socket_fd].push(data);
    addToWriteSet(socket_fd);
}

void TcpAsyncManager::addToReadSet(int fd) {
    std::lock_guard<std::mutex> lock(read_fds_mutex_);
    FD_SET(fd, &read_fds_);
    if (fd > max_read_fd_) max_read_fd_ = fd;
}

void TcpAsyncManager::removeFromReadSet(int fd) {
    std::lock_guard<std::mutex> lock(read_fds_mutex_);
    FD_CLR(fd, &read_fds_);
}

void TcpAsyncManager::readThreadFunc() {
    while (!stop_) {
        fd_set local_read_fds;
        int local_max_fd;

        // 复制读集合（避免被其他线程修改）
        {
            std::lock_guard<std::mutex> lock(read_fds_mutex_);
            local_read_fds = read_fds_;
            local_max_fd = max_read_fd_;
        }
        struct timeval tv = {0, 100000};
        int ret = select(local_max_fd + 1, &local_read_fds, nullptr, nullptr, &tv);
        if (ret > 0) {
            for (int fd = 0; fd <= local_max_fd; fd++) {
                if (FD_ISSET(fd, &local_read_fds)) {
                    std::vector<uint8_t> buffer(4096);
                    ssize_t bytes_read;
                    do {
                        bytes_read = DoipTcp::read(fd, buffer, buffer.size());
                        if (bytes_read > 0) {
                            std::vector<uint8_t> data;
                            std::move(buffer.begin(), buffer.begin() + bytes_read, std::back_inserter(data));
                            std::lock_guard<std::mutex> lock(callbacks_mutex_);
                            if (cb_ != nullptr) {
                                cb_(fd, data);
                            }else{
                                /* 不会出现该分支情况 */
                            }
                        } 
                        else if(bytes_read == 0) {
                            unregisterSocket(fd);

                            //向listener推送连接断开状态
                            EventInfo event;
                            event.socket = fd;
                            event_cb_(ListenerEventType::Disconnection, event);
                            break;
                        } 
                        else if (errno == EAGAIN || errno == EWOULDBLOCK) {
                            break;
                        }
                        else {
                            std::cout<< "undefine error" << std::endl;
                            unregisterSocket(fd);
                            EventInfo event;
                            event.socket = fd;
                            event.ern = errno;
                            event_cb_(ListenerEventType::Disconnection, event);
                            break;
                        }
                    } while(bytes_read > 0);
                }
            }
        }
    }
}

void TcpAsyncManager::addToWriteSet(int fd) {
    std::lock_guard<std::mutex> lock(write_fds_mutex_);
    FD_SET(fd, &write_fds_);
    if (fd > max_write_fd_) max_write_fd_ = fd;
}

void TcpAsyncManager::removeFromWriteSet(int fd) {
    std::lock_guard<std::mutex> lock(write_fds_mutex_);
    FD_CLR(fd, &write_fds_);
}

void TcpAsyncManager::writeThreadFunc() {
    while (!stop_) {
        fd_set local_write_fds;
        int local_max_fd;

        // 复制写集合,防止被使用时被其他线程修改
        {
            std::lock_guard<std::mutex> lock(write_fds_mutex_);
            local_write_fds = write_fds_;
            local_max_fd = max_write_fd_;
        }
        struct timeval tv = {0, 100000};
        int ret = select(local_max_fd + 1, nullptr, &local_write_fds, nullptr, &tv);
        if (ret > 0) {
            for (int fd = 0; fd <= local_max_fd; fd++) {
                if (FD_ISSET(fd, &local_write_fds)) {
                    std::lock_guard<std::mutex> lock(send_queues_mutex_);
                    auto& queue = send_queues_[fd];

                    // 发送队列中的所有数据
                    while (!queue.empty()) {
                        auto& data = queue.front();
                        ssize_t bytes_sent = DoipTcp::write(fd,data);
                        if (bytes_sent > 0) {
                            // 部分发送：移除已发送部分
                            if (bytes_sent < data.size()) {
                                data.erase(data.begin(), data.begin() + bytes_sent);
                            } else {
                                queue.pop();
                            }
                        } else if(bytes_sent == 0){
                            unregisterSocket(fd);
                            EventInfo event;
                            event.socket = fd;
                            event_cb_(ListenerEventType::Disconnection, event);
                        }else if (bytes_sent == -1) {
                            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                                break; // 下次再试
                            } else {
                                EventInfo event;
                                event.socket = fd;
                                event.ern = errno;
                                event_cb_(ListenerEventType::SendError, event);
                                // unregisterSocket(fd);
                                break;
                            }
                        }
                    }

                    // 队列空时移除写监听
                    if (queue.empty()) {
                        removeFromWriteSet(fd);
                    }
                }
            }
        }
    }
}
