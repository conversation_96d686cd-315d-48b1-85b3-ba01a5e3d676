#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <sys/socket.h>
#include "DoipTcp.hpp"
#include "tcp_socket.hpp"

DoipTcp::DoipTcp(/* args */)
{
}

DoipTcp::~DoipTcp()
{
}

int DoipTcp::connect(const std::string& ip, int port)
{
    const int total_timeout_ms = 5000; // 超时时间5秒
    const int retry_interval_ms = 100; // 重试间隔100ms
    auto start_time = std::chrono::steady_clock::now();

    while(true)
    {
        // 计算已耗时
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            current_time - start_time).count();

        // 超时检查
        if (elapsed_ms >= total_timeout_ms) {
            std::cerr << "DoipTcp: Connection timed out after " 
                      << total_timeout_ms << "ms" << std::endl;
            return -1;
        }

        int sockfd = TcpClient::createSocket();
        if (sockfd == -1) {
            std::this_thread::sleep_for(std::chrono::milliseconds(retry_interval_ms));
            continue;
        }

        if(TcpClient::setSocketNonBlock(sockfd) == false){
            TcpClient::closeSocket(sockfd);
            continue;
        }

        //连接成功
        if(TcpClient::createConnect(sockfd, ip, port) == true)
        {
            TcpClient::setSocketBlock(sockfd);
            return sockfd; 
        }

        if (errno != EINPROGRESS) {
            TcpClient::closeSocket(sockfd);
            std::this_thread::sleep_for(std::chrono::milliseconds(retry_interval_ms));
            continue;
        }

        int remaining_ms = total_timeout_ms - elapsed_ms;
        timeval timeout{
            .tv_sec = remaining_ms / 1000,
            .tv_usec = (remaining_ms % 1000) * 1000
        };

        // 监控socket状态
        fd_set write_set, error_set;
        FD_ZERO(&write_set);
        FD_ZERO(&error_set);
        FD_SET(sockfd, &write_set);
        FD_SET(sockfd, &error_set);

        int select_result = select(sockfd + 1, 
            nullptr, &write_set, &error_set, &timeout);

        if (select_result < 0) {
            TcpClient::closeSocket(sockfd);
            continue;
        }

        // 检查连接结果
        if (FD_ISSET(sockfd, &error_set)) {
            int error_code = 0;
            socklen_t len = sizeof(error_code);
            getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &error_code, &len);
            TcpClient::closeSocket(sockfd);
            std::this_thread::sleep_for(std::chrono::milliseconds(retry_interval_ms));
            continue;
        }

        if (FD_ISSET(sockfd, &write_set)) {
            int error_code = 0;
            socklen_t len = sizeof(error_code);
            getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &error_code, &len);
            
            if (error_code == 0) {
                TcpClient::setSocketBlock(sockfd);
                return sockfd;
            }
            
            TcpClient::closeSocket(sockfd);
            std::this_thread::sleep_for(std::chrono::milliseconds(retry_interval_ms));
            continue;
        }

        // 超时未响应
        TcpClient::closeSocket(sockfd);
    }
}

bool DoipTcp::disconnect(int socket)
{
    return TcpClient::closeSocket(socket);
}

bool DoipTcp::setSocketNonBlock(int sockfd)
{
    return TcpClient::setSocketNonBlock(sockfd);
}

ssize_t DoipTcp::read(int32_t socket, std::vector<uint8_t>& buffer, size_t bufferSize)
{
    return TcpClient::pread(socket, buffer, bufferSize) ;  
}

ssize_t DoipTcp::write(int32_t socket, const std::vector<uint8_t> buffer)
{
    return TcpClient::pwrite(socket, buffer);
}