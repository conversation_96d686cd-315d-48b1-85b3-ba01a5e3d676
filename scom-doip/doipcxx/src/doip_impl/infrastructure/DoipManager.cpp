#include "DoipManager.hpp"
#include "doip_spec.hpp"
#include "DoipTcp.hpp"
#include "ErrorCode.hpp"
#include "UdsDefine.hpp"
#include "Uds.hpp"
#include "DoipException.hpp"
#include <vector>
#include <iomanip>
#include <iostream>
#include <iomanip> //debug

DoipManager::DoipManager(uint16_t SA):SA_(SA)
{
}

DoipManager::~DoipManager()
{
}

void DoipManager::routingActivationRespHandle(uint8_t ra_type)
{
    RoutingActivationErrorCode code = static_cast<RoutingActivationErrorCode>(ra_type) ;

    switch (code) {
        case RoutingActivationErrorCode::UNKNOWN_SOURCE_ADDRESS:
        case RoutingActivationErrorCode::ALL_SOCKETS_ACTIVE:
        case RoutingActivationErrorCode::DIFFERENT_SA_RECEIVED:
        case RoutingActivationErrorCode::SA_REGISTERED_ELSEWHERE:
        case RoutingActivationErrorCode::MISSING_AUTHENTICATION:
        case RoutingActivationErrorCode::REJECTED_CONFIRMATION:
        case RoutingActivationErrorCode::UNSUPPORTED_ROUTING_TYPE:
            throw DoipException(RoutingActivationErrorHandler::getRoutingActivationErrorDescription(code));
            break;  
        case RoutingActivationErrorCode::ROUTING_SUCCESS:

            break;

        // 0x11-需要确认才能激活通信, 根据需求可做实现
        case RoutingActivationErrorCode::CONFIRMATION_REQUIRED:
            throw DoipException(RoutingActivationErrorHandler::getRoutingActivationErrorDescription(code));
            break;
        default:
            throw DoipException(RoutingActivationErrorHandler::getRoutingActivationErrorDescription(code));
            break;
    }
}

std::vector<uint8_t> DoipManager::createRoutingActivationReqMsg() {
    std::vector<uint8_t> msg;
    // Generic Header

    msg.emplace_back(0x02);  // Protocol Version
    msg.emplace_back(0xFD);  // Inverse Protocol Version
    msg.emplace_back(0x00);  // Payload-Type
    msg.emplace_back(0x05);
    msg.emplace_back(0x00);  // Payload-Length
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    msg.emplace_back(0x07);

    msg.emplace_back(static_cast<uint8_t>((SA_ >> 8) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>(SA_ & 0xFF));
    msg.emplace_back(RA_DEFAULT); // Activation-Type   ---RA_DEFAULT
    msg.emplace_back(0x00); // Reserved ISO(default)
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    return msg;
}

std::vector<uint8_t> DoipManager::createAliveCheckRespMsg() {
    std::vector<uint8_t> msg;
    // Generic Header
    msg.emplace_back(0x02);  // Protocol Version
    msg.emplace_back(0xFD);  // Inverse Protocol Version
    msg.emplace_back(0x00);  // Payload-Type
    msg.emplace_back(0x08);
    msg.emplace_back(0x00);  // Payload-Length
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    msg.emplace_back(0x02);
    msg.emplace_back(static_cast<uint8_t>((SA_ >> 8) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>(SA_ & 0xFF));      

    return msg;
}

std::vector<uint8_t> DoipManager::createDoipEntityStatusReqMsg()
{
    std::vector<uint8_t> msg;

    // Generic Header
    msg.emplace_back(0x02);  // Protocol Version
    msg.emplace_back(0xFD);  // Inverse Protocol Version
    msg.emplace_back(0x40);  // Payload-Type
    msg.emplace_back(0x01);
    msg.emplace_back(0x00);  // Payload-Length
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);

    return msg;
}

std::vector<uint8_t> DoipManager::createPowerModeReqMsg()
{
    std::vector<uint8_t> msg;

    // Generic Header
    msg.emplace_back(0x02);  // Protocol Version
    msg.emplace_back(0xFD);  // Inverse Protocol Version
    msg.emplace_back(0x40);  // Payload-Type
    msg.emplace_back(0x03);
    msg.emplace_back(0x00);  // Payload-Length
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);
    msg.emplace_back(0x00);

    return msg;
}

void DoipManager::sendAliveCheckResponse(int32_t sockFd) 
{
    std::vector<uint8_t> msg = createAliveCheckRespMsg();

    DoipTcp::write(sockFd, msg);
}

std::vector<uint8_t> DoipManager::createDiagReqMsg(uint16_t TA, std::vector<uint8_t> uds_msg)
{
    std::vector<uint8_t> msg;
    
    // Generic Header
    msg.emplace_back(0x02);  // Protocol Version
    msg.emplace_back(0xFD);  // Inverse Protocol Version
    msg.emplace_back(0x80);  // Payload-Type
    msg.emplace_back(0x01);

    // Payload-Length
    uint32_t payload_len = sizeof(SA_) + sizeof(TA) + uds_msg.size();
    msg.emplace_back(static_cast<uint8_t>((payload_len >> 24) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>((payload_len >> 16) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>((payload_len >> 8) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>(payload_len & 0xFF));

    // SA&TA
    msg.emplace_back(static_cast<uint8_t>((SA_ >> 8) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>(SA_ & 0xFF));
    msg.emplace_back(static_cast<uint8_t>((TA >> 8) & 0xFF));
    msg.emplace_back(static_cast<uint8_t>(TA & 0xFF));

    // uds msg
    msg.insert(msg.end(), uds_msg.begin(), uds_msg.end());

    return msg;
}

bool DoipManager::targetAddrCheck(uint16_t TA)
{
    //TA不在可识别范围内
    return true;
}

DoipDiagErrorCode DoipManager::doipDiagCheck(uint16_t SA, uint16_t TA)
{
    if(TA != SA_)
    {
        return DoipDiagErrorCode::INVALID_SOURCE_ADDRESS;
    }

    if(targetAddrCheck(SA) == false)
    {
        return DoipDiagErrorCode::UNKNOWN_TARGET_ADDRESS;
    }

    return DoipDiagErrorCode::SUCCESS;
}

DoipDiagErrorCode DoipManager::diagParse(const std::vector<uint8_t>& msg, std::vector<uint8_t>& uds_resp)
{
    uint16_t SA = static_cast<uint16_t>((msg[0]<<8)) | static_cast<uint16_t>(msg[1]);
    uint16_t TA = static_cast<uint16_t>((msg[2]<<8)) | static_cast<uint16_t>(msg[3]);

    DoipDiagErrorCode ret = doipDiagCheck(SA, TA);
    if(ret == DoipDiagErrorCode::SUCCESS)
    {
        /* 特殊情况处理 */
        if(msg[4] == 0x7f && msg[6] == 0x78) return DoipDiagErrorCode::UDS_MSG_DELAY;

        /* uds_payload：当前只返回uds数据，不包含TA地址 */
        std::move(msg.begin()+4, msg.end(), std::back_inserter(uds_resp));
        
        return DoipDiagErrorCode::SUCCESS;
    }

    return ret;
}


DoipParseErrorCode DoipManager::doipMsgParse(const std::vector<uint8_t>& msg, DoipMsgInfo& doip_msg)
{
    uint8_t index = 0;

    if (msg.size() < GENERIC_HEADER_LEN)
    {
        return DoipParseErrorCode::ILLEGAL_DATA;
    }

    doip_msg.head.version = msg[index++];
    doip_msg.head.inverse_version = msg[index++];
    doip_msg.head.payload_type = (msg[index++] << 8) | msg[index++];
    doip_msg.head.payload_length = (static_cast<uint32_t>(msg[index++]) << 24) |
                                        (static_cast<uint32_t>(msg[index++]) << 16) |
                                        (static_cast<uint32_t>(msg[index++]) << 8) |
                                        msg[index++];
    if (msg.size() != GENERIC_HEADER_LEN + doip_msg.head.payload_length)
    {
         return DoipParseErrorCode::PAYLOAD_UNMATCH;
    }

    size_t dataLength = doip_msg.head.payload_length; 
    std::move(std::move_iterator(msg.begin() + index),
              std::move_iterator(msg.begin() + index + dataLength),
              std::back_inserter(doip_msg.payload));

    //  success
    return DoipParseErrorCode::SUCCESS;
}

void DoipManager::DoipMsgRespHandle(int socket, const DoipMsgInfo doip_msg)
{
    //doip 报文处理
    switch(doip_msg.head.payload_type)
    {
        case HEADER_NEGATIVE_ACK:

            break;
        case ALIVE_CHECK_RESPONSE:
            sendAliveCheckResponse(socket);
            break;
        case DOIP_ENTITY_STATUS_RESPONSE:

            break;
        case DIAG_PM_INFO_RESPONSE:

            break;
        case DIAGNOSTIC_POSITIVE_ACK:

            break;
        case DIAGNOSTIC_NEGATIVE_ACK:

            break;
    }

}
