#include <iostream>
#include <vector>
#include <mutex>
#include <algorithm>
#include <iterator>
#include "General.hpp"


bool General::ipv4ValidCheck(const std::string& ip)
{
    int segment_count = 0;
    int num = 0;
    int digit_count = 0;
    bool leading_zero = false;

    for (size_t i = 0; i < ip.length(); ++i) {
        char c = ip[i];
        
        if (isdigit(c)) {
            // 处理前导零检测
            if (digit_count == 0 && c == '0' && i+1 < ip.length() && isdigit(ip[i+1])) {
                leading_zero = true;
            }
            
            num = num * 10 + (c - '0');
            digit_count++;
            
            // 数字长度超过3位立即返回失败
            if (digit_count > 3) return false;
            
        } else if (c == '.') {
            // 处理分段结束
            if (digit_count == 0 || num > 255 || leading_zero) return false;
            
            segment_count++;
            num = 0;
            digit_count = 0;
            leading_zero = false;
        } else {
            // 非法字符
            return false;
        }
    }

    // 最后一段的验证
    if (segment_count != 3 || digit_count == 0 || num > 255 || leading_zero) {
        return false;
    }

    return true;
}

bool General::ipv6ValidCheck(const std::string& ip)
{
    if (ip.empty() || ip.length() > 39) return false;

    std::vector<std::string> segments;
    std::string current;
    bool has_compression = false;
    size_t colon_count = 0;

    for (size_t i = 0; i < ip.length(); ++i) {
        char c = ip[i];

        if (c == ':') {
            ++colon_count;
            if (i + 1 < ip.length() && ip[i+1] == ':') {
                if (has_compression) return false;
                has_compression = true;
                ++i;
                if (!current.empty()) {
                    segments.push_back(current);
                    current.clear();
                }
                segments.push_back("");
            } else {
                if (current.empty() && segments.empty()) return false;
                segments.push_back(current);
                current.clear();
            }
        } else if (isxdigit(c)) {
            current += tolower(c);
            if (current.length() > 4) return false;
        } else {
            return false;
        }
    }

    if (!current.empty()) segments.push_back(current);

    const size_t seg_count = segments.size();
    if (has_compression) {
        if (seg_count < 1 || seg_count > 7) return false;
    } else {
        if (seg_count != 8) return false;
    }

    if (has_compression) {
        auto it = find(segments.begin(), segments.end(), "");
        if (it == segments.end()) return false;

        if (it != segments.begin() && it != prev(segments.end())) {
            if (*(prev(it)) == "" || *(next(it)) == "") return false;
        }
    }

    for (const auto& seg : segments) {
        if (seg.empty()) continue;
        if (seg.length() < 1 || seg.length() > 4) return false;
    }

    if (ip.find("::") == 0 && ip.rfind("::") != 0) return false;
    if (ip.find(".") != std::string::npos) return false;

    return true;
}

bool General::ipValidCheck(const std::string& ip)
{
    return ipv4ValidCheck(ip) || ipv6ValidCheck(ip);
}

void General::mapAddItem(std::unordered_map<std::string, int>& map, const std::string& ip, int socket, std::shared_mutex& mutex)
{
    std::unique_lock<std::shared_mutex> lock(mutex);
    map[ip] = socket;
}

int General::mapFindItem(std::unordered_map<std::string, int>& map, const std::string& ip, std::shared_mutex& mutex)
{
    std::shared_lock<std::shared_mutex> lock(mutex);
    auto it = map.find(ip);
        if (it != map.end()) {
            return it->second;
        }
        return -1; 
}

std::string General::mapFindItem(std::unordered_map<std::string, int>& map, int socket, std::shared_mutex& mutex)
{
    std::shared_lock<std::shared_mutex> lock(mutex);
    for (const auto& pair : map) {
        if (pair.second == socket) {
            return pair.first;
        }
    }
    return "";
}

void General::mapRemoveItem(std::unordered_map<std::string, int>& map, const std::string& ip, std::shared_mutex& mutex)
{   
    std::unique_lock<std::shared_mutex> lock(mutex);
    auto it = map.find(ip);
        if (it != map.end()) {
            std::cout << "Removed mapping: IP=" << ip << ", Socket FD=" << it->second << std::endl;
            
            map.erase(it);
        } else {
            std::cout << "No mapping found for IP: " << ip << std::endl;
        }
}

void General::mapRemoveItem(std::unordered_map<std::string, int>& map, int socket_fd, std::shared_mutex& mutex) 
{
    std::unique_lock<std::shared_mutex> lock(mutex);

    bool found = false;
    auto it = map.begin();
    
    while (it != map.end()) {
        if (it->second == socket_fd) {
            it = map.erase(it);
            found = true;
        } else {
            ++it;
        }
    }

    if (!found) {
        std::cout << "No mapping found for Socket FD: " 
                  << socket_fd << std::endl;
    }
}