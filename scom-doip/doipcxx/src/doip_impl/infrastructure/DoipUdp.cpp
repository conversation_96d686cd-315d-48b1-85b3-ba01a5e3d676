#include <unistd.h>
#include "udp_socket.hpp"
#include "DoipUdp.hpp"

int DoipUdp::SendBroadcast(const std::vector<uint8_t>& data,
                          const BroadcastConfig& config) {
    // 参数验证前置
    if (config.dest_ip.empty() || config.port == 0) {
        return -1;
    }

    int sockfd = UdpClient::CreateSocket();
    if(sockfd == -1) return -2;

    if(UdpClient::SetBroadcast(sockfd, true) == -1) {
        UdpClient::Close(sockfd);
        return -3;
    }

    UdpClient::SockAddr dest_addr{
        .ip = config.dest_ip,
        .port = config.port
    };

    int success_count = 0;
    for(int i=0; i<config.repeat; ++i){
        if(UdpClient::SendTo(sockfd, data, dest_addr) == data.size()) {
            ++success_count;
        }
        sleep(config.interval_sec);
    }

    UdpClient::Close(sockfd);
    return success_count;
}