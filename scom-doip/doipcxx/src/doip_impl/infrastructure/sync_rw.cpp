#include "sync_rw.hpp"
#include "DoipTcp.hpp"
#include <iostream>
#include <iomanip> //debug
ssize_t SyncIoManager::read(int32_t socket, std::vector<uint8_t>& data){
    ssize_t bytes_read;
    do {
        std::vector<uint8_t> buffer;
        buffer.resize(4096);
        bytes_read = DoipTcp::read(socket, buffer, buffer.size());
        if (bytes_read > 0)
        {
            // 拷贝有效数据
            std::move(buffer.begin(), buffer.begin() + bytes_read, std::back_inserter(data));
        }
    } while (bytes_read == -1 && errno == EINTR);
    
    return bytes_read;
}

ssize_t SyncIoManager::write(int32_t socket, std::vector<uint8_t>& data) {
    ssize_t total_sent = 0;
    
    while (!data.empty()) {
        ssize_t bytes_sent = DoipTcp::write(socket, data);
        
        if (bytes_sent > 0) {
            total_sent += bytes_sent;
            data.erase(data.begin(), data.begin() + bytes_sent);
        } else if (bytes_sent == 0) {
            break; 
        } else {
            if (errno == EINTR) {
                continue;  // 被中断信号打断，重试
            }
            return -1;
        }
    }
    
    return total_sent;
}
