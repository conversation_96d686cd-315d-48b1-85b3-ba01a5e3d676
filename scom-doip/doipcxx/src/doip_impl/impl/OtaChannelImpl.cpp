#include <iostream>
#include <string.h>
#include <iomanip> //debug
#include "OtaChannelImpl.hpp"
#include "DoipTcp.hpp"
#include "doip_spec.hpp"
#include "DoipException.hpp"
#include "General.hpp"
#include "sync_rw.hpp"

OtaChannelImpl::OtaChannelImpl(uint16_t SA, int port):port_(port),listener_(Listener::getInstance()),
            async_io_(TcpAsyncManager::getInstance()),manager_(SA)
{
    async_io_.registerEventListener([this](ListenerEventType type, const EventInfo& info) {
            this->eventListener(type, info);
        });
    async_io_.registerCallback([this](int socket, const std::vector<uint8_t>& data) {
            this->callbackHandler(socket, data);
            });
}

void OtaChannelImpl::connect(const std::string& ip)
{
    /* ip合法性校验 */
    if(General::ipValidCheck(ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError,"Input ip is invalid");
    }

    /* 创建连接 */
    int sock = DoipTcp::connect(ip, port_);
    if (sock == -1) {
        throw DoipException(DoipErrorCode::SyncConnectError, "Failed to create a TCP connection");
    }

    /* 路由激活 */
    try{
        routingActivation(sock);
    }catch(const DoipException& e){
        DoipTcp::disconnect(sock);
        throw DoipException(DoipErrorCode::RoutingActivationError, std::string("Route activation failed -- " + std::string(e.what())));
    }

    /* 注册socket到同步连接表 */
    General::mapAddItem(sync_ipsock_map_, ip, sock, rw_mutex_);
}

void OtaChannelImpl::disconnect(const std::string& ip)
{
    /* ip合法性校验 */
    if(General::ipValidCheck(ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError, "Input data [ip] is invalid");
    }
    
    /* 查找ip映射的socket并断开连接 */
    int socket = General::mapFindItem(sync_ipsock_map_, ip, rw_mutex_);
    if(socket == -1)
    {
        throw DoipException(DoipErrorCode::NonexistentConnectError, "No connection matching the input IP");
    }
    DoipTcp::disconnect(socket);

    /* 删除key-value对 */
    General::mapRemoveItem(sync_ipsock_map_, ip, rw_mutex_); //优化点：MapFindItem已经搜索到socket，MapRemoveItem内部再次执行了一次搜索
}

uint32_t OtaChannelImpl::read(OtaDataInfo& payload)
{
    bool retry = false;
    DoipDiagErrorCode ret_code;
    std::vector<u_int8_t> uds_resp;

    /* 查找ip对应的socket */
    int socket = General::mapFindItem(sync_ipsock_map_, payload.ip ,rw_mutex_);
    if(socket == -1)
    {
        std::ostringstream oss;
        oss << "connection [ " << payload.ip << " ] inexistence or not is sync connection";
        throw DoipException(DoipErrorCode::NonexistentConnectError,oss.str());
    }

    do{
        std::vector<uint8_t> data;
        ssize_t bytes_read = SyncIoManager::read(socket, data);
        if(bytes_read <= 0)
        {
            General::mapRemoveItem(sync_ipsock_map_, socket, rw_mutex_); 
            throw DoipException(DoipErrorCode::ConnectClosedError, "Server has closed the TCP connection");
        }

        DoipMsgInfo doip_msg;
        if(doipMessageHandle(socket, data, doip_msg) == false)
        {
            DoipException(DoipErrorCode::DoipFormatUnmatchError, "parse doip msg failed");
        }

        /* 协议栈只向外部转发诊断报文，非诊断报文由协议栈直接应答 */
        if(doip_msg.head.payload_type != DIAGNOSTIC_MESSAGE)
        {
            retry = true;
            manager_.DoipMsgRespHandle(socket, doip_msg);
        }
        else
        {
            ret_code = udsMessageParse(doip_msg, uds_resp);
            if(ret_code == DoipDiagErrorCode::SUCCESS)
            {
                std::move(std::move_iterator(uds_resp.begin()), 
                        std::move_iterator(uds_resp.end()),
                        std::back_inserter(payload.uds_resp));
                break;
            }
            else if(ret_code == DoipDiagErrorCode::UDS_MSG_DELAY)
            {
                /* 0x78为延时发送, 等待ecu就绪 */
                retry = true;
                continue;
            }
        }        
    }while(retry == true);

    return uds_resp.size();
}

void OtaChannelImpl::write(const OtaDataInfo& payload)
{
    if(General::ipValidCheck(payload.ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError,"Input ip is invalid");
    }

    if(payload.uds_req.empty())
    {
        throw DoipException(DoipErrorCode::EmptyMsgError, "input is empty");
    }
   
    /* 将输入的uds消息构造成doip诊断报文 */
    std::vector<uint8_t> buffer = manager_.createDiagReqMsg( 
                                        payload.TA,  
                                        payload.uds_req);
    
    /* 同步属性的socket只能采用同步写 */
    int sock = General::mapFindItem(sync_ipsock_map_, payload.ip, rw_mutex_);
    if ( sock != -1)
    {
        ssize_t bytes = DoipTcp::write(sock, buffer);
        if(bytes == 0)
        {
            throw DoipException(DoipErrorCode::ConnectClosedError, "Server has closed the TCP connection");
        }
        else if(bytes == -1)
        {
            throw DoipException(DoipErrorCode::ErrnoError, std::string(strerror(errno)));
        }
    }
    else
    {
        std::ostringstream oss;
        oss << "write failed, due to : connection [ " << payload.ip << " ] inexistence or not is sync connection";
        throw DoipException(DoipErrorCode::NonexistentConnectError,oss.str());
    }
}


void OtaChannelImpl::connectAsync(const std::string& ip) 
{
    if(General::ipValidCheck(ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError,"Input ip is invalid");
    }

    int sock = DoipTcp::connect(ip, port_);
    if (sock == -1)
    {
        throw DoipException(DoipErrorCode::AsyncConnectError, "Failed to create a TCP connection");
    }

    std::cout << "Connected to server [" << ip << " - " << port_ << "]" << std::endl;

    //路由激活
    try{
        routingActivation(sock);
    }catch(const DoipException& e){
        DoipTcp::disconnect(sock);
        throw DoipException(DoipErrorCode::RoutingActivationError, std::string("Route activation failed -- " + std::string(e.what())));
    }

    /* 注册socket到异步哈希表 */
    General::mapAddItem(async_ipsock_map_, ip, sock, rw_mutex_);
    async_io_.registerSocket(sock);
}

void OtaChannelImpl::disconnectAsync(const std::string& ip) 
{
    if(General::ipValidCheck(ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError, "Input ip is invalid");
    }

    int socket = General::mapFindItem(async_ipsock_map_, ip, rw_mutex_);
    if(socket == -1)
    {
        throw DoipException(DoipErrorCode::NonexistentConnectError, "No connection matching the input IP");
    }
    DoipTcp::disconnect(socket);

    /* 异步读写层的资源也需要释放 */
    async_io_.unregisterSocket(socket);
    General::mapRemoveItem(async_ipsock_map_, ip, rw_mutex_);
}

void OtaChannelImpl::closeChannel()
{
    std::unique_lock<std::shared_mutex> lock(rw_mutex_);
    for (auto it = async_ipsock_map_.begin(); it != async_ipsock_map_.end(); ) {
        const std::string& ip = it->first;
        int socket = it->second;
        if (socket != -1) {
           DoipTcp::disconnect(socket);
        }
        it = async_ipsock_map_.erase(it);
    }

    for (auto it = sync_ipsock_map_.begin(); it != sync_ipsock_map_.end(); ) {
        const std::string& ip = it->first;
        int socket = it->second;
        if (socket != -1) {
            DoipTcp::disconnect(socket);
        }
        it = sync_ipsock_map_.erase(it);
    }
}

void OtaChannelImpl::writeAsync(const OtaDataInfo& msg)
{
    WriteDataInfo reqm;

    if(General::ipValidCheck(msg.ip) == false)
    {
        throw DoipException(DoipErrorCode::InvalidIpError, "Input ip is invalid");
    }

    if(msg.uds_req.empty())
    {
        throw DoipException(DoipErrorCode::EmptyMsgError, "input is empty");
    }

    reqm.ip = msg.ip;
    reqm.TA = msg.TA;
    reqm.uds_req = msg.uds_req;

    writeProcess(reqm);
}

void OtaChannelImpl::writeProcess(const WriteDataInfo& reqm)
{
    std::vector<uint8_t> buffer = manager_.createDiagReqMsg( 
                                        reqm.TA,  
                                        reqm.uds_req);

    int sock = General::mapFindItem(async_ipsock_map_, reqm.ip, rw_mutex_);
    if (sock != -1) {
        async_io_.asyncWrite(sock, buffer);
    }
    else
    {
        std::ostringstream oss;
        oss << "write failed, due to : connection [ " << reqm.ip << " ] inexistence or not is async connection";
        throw DoipException(DoipErrorCode::NonexistentConnectError,oss.str());
    }
}

void OtaChannelImpl::registerCallback(const CallbackType& callback)
{
    callback_ = callback;
}
void OtaChannelImpl::registerListener(const ListenerCallback& callback)
{
    listener_.setListener(callback);
}

void OtaChannelImpl::registerListenerEvent(ListenerEventType mask)
{
    listener_.RegisterEvent(mask);
}

void OtaChannelImpl::callbackHandler(int socket, const std::vector<uint8_t>& data)
{
    DoipMsgInfo doip_msg;
    if(doipMessageHandleAsync(socket, data, doip_msg) == false)
    {
        std::cout << "doip报文解析出错" << std::endl;
        /* 若doip报文格式错误，由于是回调函数被读写层调用不做异常抛出，让程序正常运行。通过事件抛送给上层 */
        EventInfo event;
        eventListener(ListenerEventType::InvalidDoipMsg, event);
        return;
    }

    /* 协议栈只向外部转发诊断报文，非诊断报文由协议栈直接应答 */
    if(doip_msg.head.payload_type != DIAGNOSTIC_MESSAGE)
    {
        manager_.DoipMsgRespHandle(socket, doip_msg);
    }
    else
    {
        /* 解析uds报文 */
        std::vector<uint8_t> buffer;
        DoipDiagErrorCode ret_code = udsMessageParse(doip_msg, buffer);
        if(ret_code == DoipDiagErrorCode::SUCCESS)
        {
            callback_(buffer);
        }
        else if(ret_code == DoipDiagErrorCode::UDS_MSG_DELAY)
        {
            /* 0x78为延时发送，直接忽略本条消息 */
        }
        else
        {
            /* 对于uds报文异常的情况，应进行doip否定应答 */
        }
    }
}

void OtaChannelImpl::eventListener(ListenerEventType mask, const EventInfo& event)
{
    ListenerEventInfo msg;
    switch (mask)
    {
        case ListenerEventType::Disconnection :
            DoipTcp::disconnect(event.socket);
            async_io_.unregisterSocket(event.socket);
            msg.ip = General::mapFindItem(async_ipsock_map_, event.socket, rw_mutex_);
            General::mapRemoveItem(async_ipsock_map_, msg.ip, rw_mutex_);
            listener_.Notify(mask, msg);
            break;
        case ListenerEventType::InvalidDoipMsg :
            listener_.Notify(mask, msg);
            break;
        case ListenerEventType::SendError :
            listener_.Notify(mask, msg);
            break;
        default:
            break;
        }
}


bool OtaChannelImpl::doipMessageHandleAsync(int socket, const std::vector<uint8_t>& data, DoipMsgInfo& doip_msg)
{
    DoipParseErrorCode error_code;

    /* 解析doip报文 */
    error_code = manager_.doipMsgParse(data, doip_msg);
    if(error_code != DoipParseErrorCode::SUCCESS)
    {
        return false;
    }

    return true;
}

bool OtaChannelImpl::doipMessageHandle(int socket, const std::vector<uint8_t>& data, DoipMsgInfo& doip_msg)
{
    DoipParseErrorCode error_code;

    /* 解析doip报文 */
    error_code = manager_.doipMsgParse(data, doip_msg);
    if(error_code != DoipParseErrorCode::SUCCESS)
    {
        return false;
    }

    return true;
}

DoipDiagErrorCode OtaChannelImpl::udsMessageParse(const DoipMsgInfo& doip_msg, std::vector<uint8_t>& uds_resp)
{    
    return manager_.diagParse(doip_msg.payload, uds_resp);;
}

void OtaChannelImpl::routingActivation(int32_t sockFd)
{
    std::vector<uint8_t> msg = manager_.createRoutingActivationReqMsg();
    std::vector<uint8_t> recv;

    ssize_t write_bytes = SyncIoManager::write(sockFd, msg);
    if(write_bytes == 0)
    {
        throw DoipException(DoipErrorCode::ConnectClosedError, "send routing activation request failed : connection closed");
    }
    else if(write_bytes == -1)
    {
        std::ostringstream oss;
        oss << "send routing activation request failed : " << std::string(strerror(errno));
        throw DoipException(DoipErrorCode::ErrnoError, oss.str());
    }

    ssize_t read_bytes = SyncIoManager::read(sockFd, recv);
    if(read_bytes > 0)
    {
        DoipMsgInfo doip_msg;
        DoipParseErrorCode error_code;

        error_code = manager_.doipMsgParse(recv, doip_msg);
        if(error_code == DoipParseErrorCode::SUCCESS)
        {
            if(doip_msg.head.payload_type == ROUTING_ACTIVATION_RESPONSE)
            {
                try{
                    manager_.routingActivationRespHandle(doip_msg.payload[4]);
                }catch(const DoipException& e){
                    throw DoipException(DoipErrorCode::UnmatchRoutingActivationCodeError, std::string(e.what()));
                }
            }
            else
            {
                throw DoipException(DoipErrorCode::NoRoutingActivationRespError, "No routing activation response has been received");
            }
        }
        else
        {
            throw DoipException(DoipErrorCode::DoipFormatUnmatchError, "parse doip msg failed");
        }
    }
    else if(read_bytes == 0){
        throw DoipException(DoipErrorCode::ConnectClosedError, "receive routing activation respon failed : connection closed");
    }
    else if(read_bytes == -1){
        std::ostringstream oss;
        oss << "receive routing activation respon failed : " << std::string(strerror(errno));
        throw DoipException(DoipErrorCode::ErrnoError, std::string(strerror(errno)));
    }
}