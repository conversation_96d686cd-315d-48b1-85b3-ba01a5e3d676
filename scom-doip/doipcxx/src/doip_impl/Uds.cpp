#include "UdsDefine.hpp"
#include "Uds.hpp"
#include <vector>
#include <cstdint>
#include <stdexcept>

// 响应报文解析函数
UdsRespInfo UDSonIP::ParseUdsRespMsg(const std::vector<uint8_t>& data) {
    if (data.empty()) throw std::invalid_argument("Empty response data");

    UdsRespInfo response;
    if (data[0] == 0x7F) { // Negative response
        if (data.size() < 3) throw std::invalid_argument("Invalid negative response");
        
        response.isPositive = false;
        response.serviceID = static_cast<UDSServiceID>(data[1]);
        response.negativeResponseCode = data[2];
        return response;
    }

    // Positive response
    response.isPositive = true;
    uint8_t responseSID = data[0];
    uint8_t originalSIDValue = responseSID - 0x40;
    UDSServiceID originalSID = static_cast<UDSServiceID>(originalSIDValue);

    if (!isValidServiceID(originalSID)) {
        throw std::invalid_argument("Invalid service ID in response");
    }

    response.serviceID = originalSID;
    size_t dataStartIndex = 1;

    if (serviceHasSubFunction(originalSID)) {
        if (data.size() < 2) throw std::invalid_argument("Missing subfunction");
        response.subFunction = data[1];
        dataStartIndex = 2;
    }

    if (data.size() > dataStartIndex) {
        response.responseData.assign(data.begin() + dataStartIndex, data.end());
    }

    return response;
}

bool UDSonIP::isValidServiceID(UDSServiceID service) {
    switch (service) {
        case UDSServiceID::DiagnosticSessionControl:
        case UDSServiceID::EcuReset:
        case UDSServiceID::ClearDiagnosticInfo:
        case UDSServiceID::ReadDTCInformation:
        case UDSServiceID::ReadDataByIdentifier:
        case UDSServiceID::ReadMemoryByAddress:
        case UDSServiceID::SecurityAccess:
        case UDSServiceID::CommunicationControl:
        case UDSServiceID::Authentication:
        case UDSServiceID::ReadDataByPeriodicId:
        case UDSServiceID::DynamicDataDefinition:
        case UDSServiceID::WriteDataByIdentifier:
        case UDSServiceID::InputOutputControl:
        case UDSServiceID::RoutineControl:
        case UDSServiceID::RequestDownload:
        case UDSServiceID::TransferData:
        case UDSServiceID::RequestTransferExit:
        case UDSServiceID::RequestFileTransfer:
        case UDSServiceID::WriteMemoryByAddress:
        case UDSServiceID::TesterPresent:
        case UDSServiceID::ControlDTCSetting:
            return true;
        default: return false;
    }
}

bool UDSonIP::serviceHasSubFunction(UDSServiceID service) {
    switch (service) {
        case UDSServiceID::DiagnosticSessionControl:
        case UDSServiceID::EcuReset:
        case UDSServiceID::SecurityAccess:
        // Add services with subfunctions...
            return true;
        default: return false;
    }
}

