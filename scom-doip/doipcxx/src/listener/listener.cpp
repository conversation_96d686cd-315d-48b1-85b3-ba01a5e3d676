#include "Listener.hpp"
#include <atomic>
#include <iostream>

std::atomic<Listener*> Listener::instance{nullptr};
std::mutex Listener::mutex_;

Listener& Listener::getInstance() {
    Listener* tmp = instance.load(std::memory_order_relaxed);
    std::atomic_thread_fence(std::memory_order_acquire);
    if (tmp == nullptr) {
        std::lock_guard<std::mutex> lock(mutex_);
        tmp = instance.load(std::memory_order_relaxed);
        if (tmp == nullptr) {
            tmp = new Listener();
            std::atomic_thread_fence(std::memory_order_release);
            instance.store(tmp, std::memory_order_relaxed);
        }
    }
    return *tmp;
}

void Listener::setListener(const ListenerCallback& cb) {
    cb_ = cb;
}

void Listener::Notify(ListenerEventType mask, const ListenerEventInfo event){
    if(mask & mask_){
        if (cb_) {
            cb_(mask, event);
        }
    }
}

void Listener::RegisterEvent(ListenerEventType mask)
{
    mask_ |= mask;
}