#include "udp_socket.hpp"
#include <iostream>
#include <cstring>
#include <unistd.h>
#include <arpa/inet.h>
#include <fcntl.h>

UdpClient::UdpClient() = default;
UdpClient::~UdpClient() = default;

int UdpClient::SetReusePort(int sockfd, bool enable) {
    int opt = enable ? 1 : 0;
    return setsockopt(sockfd, SOL_SOCKET, SO_REUSEPORT, &opt, sizeof(opt));
}

int UdpClient::SetBroadcast(int sockfd, bool enable) {
    int opt = enable ? 1 : 0;
    return setsockopt(sockfd, SOL_SOCKET, SO_BROADCAST, &opt, sizeof(opt));
}

bool UdpClient::SetNonBlocking(int sockfd) {
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags == -1) return false;
    return fcntl(sockfd, F_SETFL, flags | O_NONBLOCK) != -1;
}

bool UdpClient::SetBlocking(int sockfd) {
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags == -1) return false;
    return fcntl(sockfd, F_SETFL, flags & ~O_NONBLOCK) != -1;
}

int UdpClient::CreateSocket() {
    int sockfd = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (sockfd == -1) {
        std::cerr << "Create UDP socket failed: " << strerror(errno) << std::endl;
    }
    return sockfd;
}

bool UdpClient::Bind(int sockfd, const std::string& local_ip, uint16_t local_port) {
    sockaddr_in addr{};
    addr.sin_family = AF_INET;
    addr.sin_port = htons(local_port);
    
    if (inet_pton(AF_INET, local_ip.c_str(), &addr.sin_addr) <= 0) {
        std::cerr << "Invalid local address: " << local_ip << std::endl;
        return false;
    }
    
    if (bind(sockfd, reinterpret_cast<sockaddr*>(&addr), sizeof(addr)) == -1) {
        std::cerr << "Bind failed: " << strerror(errno) << std::endl;
        return false;
    }
    return true;
}

ssize_t UdpClient::SendTo(int sockfd, 
                         const std::vector<uint8_t>& buffer,
                         const SockAddr& dest_addr) {
    if (sockfd == -1 || buffer.empty()) return -1;
    
    sockaddr_in addr = dest_addr.GetNativeAddr();
    return sendto(sockfd, buffer.data(), buffer.size(), 0,
                 reinterpret_cast<sockaddr*>(&addr), sizeof(addr));
}

ssize_t UdpClient::RecvFrom(int sockfd,
                           std::vector<uint8_t>& buffer,
                           SockAddr* src_addr) {
    constexpr size_t kDefaultBufferSize = 65507; // UDP 最大载荷
    buffer.resize(kDefaultBufferSize);
    
    sockaddr_in addr{};
    socklen_t addr_len = sizeof(addr);
    
    ssize_t len = recvfrom(sockfd, buffer.data(), buffer.size(), 0,
                          reinterpret_cast<sockaddr*>(&addr), &addr_len);
    if (len > 0) {
        buffer.resize(len);
        if (src_addr) {
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &addr.sin_addr, ip_str, sizeof(ip_str));
            src_addr->ip = ip_str;
            src_addr->port = ntohs(addr.sin_port);
        }
    }
    return len;
}

bool UdpClient::Connect(int sockfd, const SockAddr& remote_addr) {
    sockaddr_in addr = remote_addr.GetNativeAddr();
    return connect(sockfd, reinterpret_cast<sockaddr*>(&addr), sizeof(addr)) == 0;
}

ssize_t UdpClient::Send(int sockfd, const std::vector<uint8_t>& buffer) {
    if (sockfd == -1 || buffer.empty()) return -1;
    return send(sockfd, buffer.data(), buffer.size(), 0);
}

ssize_t UdpClient::Recv(int sockfd, std::vector<uint8_t>& buffer) {
    constexpr size_t kDefaultBufferSize = 65507;
    buffer.resize(kDefaultBufferSize);
    
    ssize_t len = recv(sockfd, buffer.data(), buffer.size(), 0);
    if (len > 0) {
        buffer.resize(len);
    }
    return len;
}

bool UdpClient::JoinMulticastGroup(int sockfd, 
                                  const std::string& multicast_ip,
                                  const std::string& local_ip) {
    ip_mreq mreq{};
    inet_pton(AF_INET, multicast_ip.c_str(), &mreq.imr_multiaddr);
    inet_pton(AF_INET, local_ip.c_str(), &mreq.imr_interface);
    
    return setsockopt(sockfd, IPPROTO_IP, IP_ADD_MEMBERSHIP, 
                     &mreq, sizeof(mreq)) == 0;
}

bool UdpClient::LeaveMulticastGroup(int sockfd,
                                   const std::string& multicast_ip,
                                   const std::string& local_ip) {
    ip_mreq mreq{};
    inet_pton(AF_INET, multicast_ip.c_str(), &mreq.imr_multiaddr);
    inet_pton(AF_INET, local_ip.c_str(), &mreq.imr_interface);
    
    return setsockopt(sockfd, IPPROTO_IP, IP_DROP_MEMBERSHIP, 
                     &mreq, sizeof(mreq)) == 0;
}

bool UdpClient::Close(int sockfd) {
    return close(sockfd) == 0;
}