#include <iostream>
#include <string>
#include <cstring>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include "tcp_socket.hpp"
#include "ErrorCode.hpp"

TcpClient::TcpClient(/* args */)
{
}

TcpClient::~TcpClient()
{
}


int32_t TcpClient::setSockPortReuse(int sockfd, bool val)
{
    int32_t opt = val;
    return setsockopt(sockfd, SOL_SOCKET, SO_REUSEPORT, &opt, sizeof(opt));
}

int32_t TcpClient::setSockNoDelay(int sockfd, bool val)
{
    int32_t opt = val;
    return setsockopt(sockfd, IPPROTO_TCP, TCP_NODELAY, &opt, sizeof(opt));
}

bool TcpClient::setSocketNonBlock(int sockfd)
{
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags == -1) return false;
    
    if (fcntl(sockfd, F_SETFL, flags | O_NONBLOCK) == -1) {
        return false;
    }

    return true;
}

bool TcpClient::setSocketBlock(int sockfd)
{
    int flags = fcntl(sockfd, F_GETFL, 0);
    if (flags == -1) {
        return false;
    }

    flags &= ~O_NONBLOCK;

    if (fcntl(sockfd, F_SETFL, flags) == -1) {
        return false;
    }

    return true;
}

int32_t TcpClient::createSocket() 
{
    int32_t sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd == -1) 
    {
        std::cout << "TcpClient: 创建socket失败" << std::endl;
        return sockfd;
    }
    
    if(setSockPortReuse(sockfd,true) != 0)
    {
        std::cout << "TcpClient: 设置端口复用失败" << std::endl;
        close(sockfd);
        return -1;
    }
    
#ifdef TCP_NODELAY
    int noDelay = 1;
    if(setSockNoDelay(sockfd,true) != 0)
    {
        std::cout << "TcpClient: 设置nodelay失败" << std::endl;
        close(sockfd);
        return -1;
    }
#endif

    return sockfd;
}

bool TcpClient::createConnect(int sockfd, const std::string& ip, int port)
{
    struct sockaddr_in serverAddr;
    std::memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(port);

    if (inet_pton(AF_INET, ip.c_str(), &serverAddr.sin_addr) <= 0) {
        std::cerr << "Invalid address or address not supported: " << ip << std::endl;
        return false;
    }

    if (connect(sockfd, reinterpret_cast<struct sockaddr*>(&serverAddr), sizeof(serverAddr)) == -1) {
        // std::cerr << "Connection failed: " << std::strerror(errno) << std::endl;
        return false;
    }

    return true;
}

ssize_t TcpClient::pwrite(int32_t socket, std::vector<uint8_t> buffer)
{
    if (socket < 0) {
        std::cerr << "Invalid socket descriptor." << std::endl;
        return 0;
    }

    if (buffer.empty()) {
        std::cerr << "Data to write is empty." << std::endl;
        return 0;
    }

    ssize_t sentBytes = send(socket, buffer.data(), buffer.size(), MSG_DONTWAIT);
    if (sentBytes == -1) {
        std::cerr << "Error sending data: " << std::strerror(errno) << std::endl;
    }

    return sentBytes;
}

ssize_t TcpClient::pread(int32_t socket, std::vector<uint8_t>& buffer, size_t bufferSize) {
    if (socket < 0) {
        std::cerr << "Invalid socket descriptor." << std::endl;
        return -1;
    }

    buffer.resize(bufferSize);

    ssize_t receivedBytes = recv(socket, buffer.data(), bufferSize, 0);
    if (receivedBytes <= 0) {
        // std::cerr << "Error receiving data: " << std::strerror(errno) << std::endl;
        return receivedBytes;
    }

    if (receivedBytes > 0) {
        buffer.resize(receivedBytes);
    }

    return receivedBytes;
}

bool TcpClient::closeSocket(int32_t socket) {

    if (socket != -1 && close(socket) == -1) {
        std::cerr << "Error closing socket: " << std::strerror(errno) << std::endl;
        return false;
    }

    // std::cout << "Socket closed successfully." << std::endl;
    return true;
}