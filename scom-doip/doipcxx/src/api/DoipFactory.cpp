#include "DoipFactory.hpp"
// #include "DiagChannelImpl.hpp"
#include "OtaChannelImpl.hpp"

// template <>
// std::unique_ptr<IDoipChannel<DiagDataInfo>> 
// DoipFactory::createChannel(unsigned short int SA, int port) {
//     return std::make_unique<DiagChannelImpl>(SA, port);
// }

template <>
std::unique_ptr<IDoipChannel<OtaDataInfo>> 
DoipFactory::createChannel(unsigned short int SA, int port) {
    return std::make_unique<OtaChannelImpl>(SA, port);
}

/* 新增模块 */
// template <>
// std::unique_ptr<IDoipChannel<reserve>> 
// DoipFactory::createChannel(int port) {
//     return std::make_unique<reserve>(port);
// }

// 显式实例化
template class IDoipChannel<DiagDataInfo>;
template class IDoipChannel<OtaDataInfo>;
/* 新增模块显式实例化 */
// template class IDoipChannel<reserve>;

/* 问题记录：
1、select的超时时长目前为100ms，后续制定

 */