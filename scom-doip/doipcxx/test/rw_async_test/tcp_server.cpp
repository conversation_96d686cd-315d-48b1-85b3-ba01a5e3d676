#include <iostream>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <vector>
#include <iomanip>
#include <thread>
#include <mutex>

#define PORT 13400
#define BUFFER_SIZE 4096 + 1024

std::mutex print_mutex;

typedef struct
{
    uint8_t version;
    uint8_t inverse_version;
    uint16_t payload_type;
    uint32_t payload_length;
}DoipHeadInfo;

typedef struct
{
    DoipHeadInfo head;
    std::vector<uint8_t> payload;
}DoipMsgInfo;

std::vector<uint8_t> udsResp(bool is_positive, uint16_t TA, uint16_t SA, uint16_t count)
{
    std::vector<uint8_t> msg;
    if(is_positive == true)
    {
        // Generic Header
        msg.emplace_back(0x02);  // Protocol Version
        msg.emplace_back(0xFD);  // Inverse Protocol Version
        msg.emplace_back(0x80);  // Payload-Type
        msg.emplace_back(0x01);

        // Payload-Length
        uint32_t payload_len = sizeof(SA) + sizeof(TA) + 2;
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 24) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 16) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(payload_len & 0xFF));

        // SA&TA
        msg.emplace_back(static_cast<uint8_t>((SA >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(SA & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((TA >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(TA & 0xFF));

        msg.emplace_back(0x76);
        msg.emplace_back(count);
    }
    else
    {
        // Generic Header
        msg.emplace_back(0x02);  // Protocol Version
        msg.emplace_back(0xFD);  // Inverse Protocol Version
        msg.emplace_back(0x80);  // Payload-Type
        msg.emplace_back(0x01);

        // Payload-Length
        uint32_t payload_len = sizeof(SA) + sizeof(TA) + 3;
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 24) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 16) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((payload_len >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(payload_len & 0xFF));

        // SA&TA
        msg.emplace_back(static_cast<uint8_t>((SA >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(SA & 0xFF));
        msg.emplace_back(static_cast<uint8_t>((TA >> 8) & 0xFF));
        msg.emplace_back(static_cast<uint8_t>(TA & 0xFF));

        msg.emplace_back(0x7f);
        msg.emplace_back(0x36);
        msg.emplace_back(count);
    }

    return msg;
}

bool DoipMsgParse(const std::vector<uint8_t>& msg, DoipMsgInfo& doip_msg)
{
    u_int8_t index = 0;

    if (msg.size() < 8)
    {
        return false;
    }

    doip_msg.head.version = msg[index++];
    doip_msg.head.inverse_version = msg[index++];
    doip_msg.head.payload_type = (msg[index++] << 8) | msg[index++];
    doip_msg.head.payload_length = (static_cast<uint32_t>(msg[index++]) << 24) |
                                        (static_cast<uint32_t>(msg[index++]) << 16) |
                                        (static_cast<uint32_t>(msg[index++]) << 8) |
                                        msg[index++];
    if (msg.size() != 8 + doip_msg.head.payload_length)
    {
         //EccorCode
         return false;
    }

    size_t dataLength = doip_msg.head.payload_length; 
    doip_msg.payload.assign(msg.begin() + index, msg.begin() + index + dataLength);

    //  success
    return true;
}

std::vector<uint8_t> udsParse(std::vector<uint8_t>& uds_msg)
{
    uint16_t TA = static_cast<uint16_t>((uds_msg[0]<<8)) | static_cast<uint16_t>(uds_msg[1]);
    uint16_t SA = static_cast<uint16_t>((uds_msg[2]<<8)) | static_cast<uint16_t>(uds_msg[3]);
    uint8_t sid = uds_msg[4];
    std::vector<uint8_t> msg;

    switch (sid)
    {
        case 0x36:
            static uint8_t count = 0;
            if(count == uds_msg[5]){
                //肯定应答
                msg = udsResp(true, TA, SA, count++);
            }
            else{
                std::cout << "固件包分块错误，收到的块：" << uds_msg[5]<< "  当前块：" << count <<std::endl;
                //否定应答
                msg = udsResp(false, TA, SA, count++);
            }
            break;
        
        default:
            break;
    }

    return msg;
}

std::vector<uint8_t> routingActivationResp(std::vector<uint8_t>& msg)
{
    std::vector<uint8_t> resp;

    resp.emplace_back(0x02);  // Protocol Version
    resp.emplace_back(0xFD);  // Inverse Protocol Version
    resp.emplace_back(0x00);  // Payload-Type
    resp.emplace_back(0x06);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x0D);

    /* payload */
    resp.emplace_back(msg[0]);
    resp.emplace_back(msg[1]);
    resp.emplace_back(0x00);
    resp.emplace_back(0x64);
    resp.emplace_back(0x10);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);
    resp.emplace_back(0x00);

    return resp;
}

//解析doip诊断报文
std::vector<uint8_t> doipResp(std::vector<uint8_t>& msg)
{
    std::vector<uint8_t> resp;
    DoipMsgInfo doip_msg;
    if(DoipMsgParse(msg, doip_msg) != true)
    {
        std::cout << "doip报文错误" << std::endl;
        return resp;
    }
    
    switch (doip_msg.head.payload_type)
    {
        case 0x8001:
            std::cout << "doip诊断报文" << std::endl;
            resp = udsParse(doip_msg.payload);
            break;
        case 0x0005:
            std::cout << "路由激活报文" << std::endl;
            resp = routingActivationResp(doip_msg.payload);
            break;
        
        default:
            break;
    }
    

    return resp;
}

void handle_client(int client_fd, sockaddr_in client_addr) {
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "New client connected with socket fd: " << client_fd << ", IP: " 
                  << inet_ntoa(client_addr.sin_addr) << ":" << ntohs(client_addr.sin_port) << std::endl;
    }

    unsigned char buffer[BUFFER_SIZE];
    while (true) {
        ssize_t bytes_read = recv(client_fd, buffer, BUFFER_SIZE, 0);
        if (bytes_read > 0) {
            {
                std::lock_guard<std::mutex> lock(print_mutex);
                std::cout << "Received data from client (socket fd: " << client_fd << "): ";
                for (ssize_t i = 0; i < bytes_read; ++i) {
                    std::cout << "0x" << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(buffer[i]) << " ";
                }
                std::cout << std::endl;
            }

            std::vector<uint8_t> msg;
            msg.insert(msg.begin(), buffer, buffer+bytes_read);
            std::vector<uint8_t> resp = doipResp(msg);
            sleep(1);
            ssize_t bytes_sent = send(client_fd, resp.data(), resp.size(), 0);
            if (bytes_sent == -1) {
                std::lock_guard<std::mutex> lock(print_mutex);
                std::cerr << "Failed to send data to client (socket fd: " << client_fd << ")" << std::endl;
                break;
            }
        } else if (bytes_read == 0) {
            // 客户端关闭连接
            break;
        } else {
            // 接收数据出错
            std::lock_guard<std::mutex> lock(print_mutex);
            std::cerr << "Error receiving data from client (socket fd: " << client_fd << ")" << std::endl;
            break;
        }
    }

    close(client_fd);
    {
        std::lock_guard<std::mutex> lock(print_mutex);
        std::cout << "Closed client connection with socket fd: " << client_fd << std::endl;
    }
}

int main() {
    int listen_fd = socket(AF_INET, SOCK_STREAM, 0);
    if (listen_fd == -1) {
        std::cerr << "Failed to create socket" << std::endl;
        return 1;
    }
    std::cout << "Server created listen socket with fd: " << listen_fd << std::endl;

    int opt = 1;
    if (setsockopt(listen_fd, SOL_SOCKET, SO_REUSEADDR, &opt, sizeof(opt)) == -1) {
        std::cerr << "Failed to set socket option" << std::endl;
        close(listen_fd);
        return 1;
    }

    sockaddr_in server_addr{};
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(PORT);
    if (bind(listen_fd, reinterpret_cast<sockaddr*>(&server_addr), sizeof(server_addr)) == -1) {
        std::cerr << "Failed to bind socket" << std::endl;
        close(listen_fd);
        return 1;
    }

    if (listen(listen_fd, 5) == -1) {
        std::cerr << "Failed to listen on socket" << std::endl;
        close(listen_fd);
        return 1;
    }

    std::cout << "Server is listening on port " << PORT << " using socket fd: " << listen_fd << std::endl;

    while (true) {
        sockaddr_in client_addr{};
        socklen_t client_addr_len = sizeof(client_addr);
        int client_fd = accept(listen_fd, reinterpret_cast<sockaddr*>(&client_addr), &client_addr_len);
        if (client_fd == -1) {
            std::cerr << "Failed to accept connection" << std::endl;
            continue;
        }

        std::thread(handle_client, client_fd, client_addr).detach();
    }

    close(listen_fd);
    std::cout << "Closed listen socket with fd: " << listen_fd << std::endl;
    return 0;
}