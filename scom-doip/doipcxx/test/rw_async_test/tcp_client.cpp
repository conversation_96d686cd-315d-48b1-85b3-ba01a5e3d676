#include "DoipFactory.hpp"
#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <thread>
#include <mutex>
#include <condition_variable>
#include "DoipException.hpp"
#include <iomanip> //debug

std::condition_variable cv;
std::mutex mtx;
bool responded = false;
bool success = false;

void listener(ListenerEventType mask, ListenerEventInfo msg)
{
    switch (mask)
    {
        case ListenerEventType::Disconnection :
            std::cout << msg.ip << " : disconnet" << std::endl;
            /* code */
            break;
        case ListenerEventType::InvalidDoipMsg :
            std::cout << "Invalid doip msg" << std::endl;
            /* code */
            break;    
        
        default:
            break;
    }
}

//接收来自mcu的消息
void ota1ReadCallback(const std::vector<uint8_t>& data) {
    uint8_t sid = data[0];
    std::cout << "usd msg :" ;
    for(auto byte : data)
    {
        std::cout << " 0x" << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(byte);
    }
    std::cout << std::endl;
    switch (sid)
    {
        case 0x76:
            //收到0x36的正响应，通知写线程继续发送下一块数据
            success = true;
            responded = true;
            cv.notify_one();
            break;
        case 0x7f:
            if(data[1] == 0x36)
            //收到来自0x36的负响应，终止发送
            success = false;
            responded = true;
            cv.notify_one();
            break;
        default:
            break;
    }
}

void uds_handle(const OtaDataInfo& data) {
    std::cout << "recv uds msg:";

    for (unsigned char byte : data.uds_resp) {
        std::cout << " 0x" << std::hex << std::setfill('0') << std::setw(2) << static_cast<int>(byte);
    }
    std::cout << std::endl;
}

//固件包发送函数
void ota_fw_4k_test1(std::unique_ptr<IDoipChannel<OtaDataInfo>>& ota_channel, std::vector<uint8_t>& fw)
{
    OtaDataInfo msg;
    msg.ip = "127.0.0.1";
    msg.TA = 0x0064;

    // 分块处理
    size_t total_size = fw.size();
    size_t block_size = 4096;
    size_t num_blocks = (total_size + block_size - 1) / block_size;
    uint8_t current_block = 0;
    
    while(current_block < num_blocks)
    {
        size_t offset = current_block * block_size;
        size_t length = std::min(block_size, total_size - offset);
        msg.uds_req.assign(fw.begin() + offset, fw.begin() + offset + length);
        
        // 添加count
        msg.uds_req.insert(msg.uds_req.begin(), current_block);
        // 添加服务头0x36
        msg.uds_req.insert(msg.uds_req.begin(), 0x36);

        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        {
            std::unique_lock<std::mutex> lock(mtx);
            if (cv.wait_for(lock, std::chrono::seconds(5), [] { return responded; })) {
                if (!success) {
                    std::cerr << "收到负响应，终止发送" << std::endl;
                    break;
                }
                // 成功，继续下一块
                responded = false;
                current_block++;
            } else {
                std::cerr << "等待响应超时，终止发送" << std::endl;
                break;
            }
        }
    }
}




std::vector<unsigned char> readBinaryFile(const std::string& filePath) {
    std::ifstream file(filePath, std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
        throw DoipException("无法打开文件: " + filePath);
    }

    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    std::vector<unsigned char> buffer(size);
    if (file.read(reinterpret_cast<char*>(buffer.data()), size)) {
        return buffer;
    } else {
        throw DoipException("读取文件失败: " + filePath);
    }
}



void ota_async_test() {
    auto ota_channel = DoipFactory::createChannel<OtaDataInfo>(0x000B,13400);
#if 0    
    const std::string ip1 = "************";
    const std::string ip2 = "************";
#else
    const std::string ip1 = "127.0.0.1";
    const std::string ip2 = "*********";
#endif
    ota_channel->registerCallback(ota1ReadCallback);
    ota_channel->registerListener(listener);
    ota_channel->registerListenerEvent(ListenerEventType::InvalidDoipMsg | ListenerEventType::Disconnection);

    OtaDataInfo msg1;
    msg1.ip = ip1;
    msg1.TA = 0x0064;
    // msg1.uds_req.assign({0x22, 0xf1, 0x11});

    std::vector<uint8_t> fw;
    try{
        fw = readBinaryFile("./big_file");
    }catch(const DoipException& e){
        std::cerr << e.what() << std::endl;
    }
   
    /* 异步通信 */
    try {
        ota_channel->connectAsync(ip1);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
        return;
    }

    std::thread t1(ota_fw_4k_test1, std::ref(ota_channel), std::ref(fw));
    t1.join();

    /* connect 2 */
    try {
        ota_channel->connectAsync(ip2);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    OtaDataInfo msg2;
    msg2.ip = ip2;
    msg2.TA = 0x0064;
    msg2.uds_req.assign({0x22, 0xf1, 0x22});

    // 创建线程来执行 writeAsync
    
    // std::thread t2(writeAsyncWrapper, std::ref(ota_channel), std::ref(msg2));
    // t2.join();
    // 等待线程完成
    
    // while (true) {
    //     // std::cout << "ota run" << std::endl;
    //     sleep(1);
    // }
}

#define time_ms (500*1000)
void ota_async_wtire_test() {
    auto ota_channel = DoipFactory::createChannel<OtaDataInfo>(0x0E00,13400);
#if 1    
    const std::string ip1 = "************";
    const std::string ip2 = "************";
#else
    const std::string ip1 = "127.0.0.1";
    const std::string ip2 = "*********";
#endif
    ota_channel->registerCallback(ota1ReadCallback);
    ota_channel->registerListener(listener);
    ota_channel->registerListenerEvent(ListenerEventType::InvalidDoipMsg | ListenerEventType::Disconnection);

    OtaDataInfo msg;
    msg.ip = ip1;
    msg.TA = 0x0064;
    

    std::vector<uint8_t> fw;
    try{
        fw = readBinaryFile("./big_file");
    }catch(const DoipException& e){
        std::cerr << e.what() << std::endl;
    }
   
    /* 异步通信 */
    try {
        ota_channel->connectAsync(ip1);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
        return;
    }
    
    msg.uds_req.assign({0x10, 0x01});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x10, 0x03});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x31, 0x01, 0x02, 0x03});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x85, 0x02});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x28, 0x03, 0x03});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x10, 0x02});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    } 

    usleep(time_ms);
    msg.uds_req.assign({0x27, 0x09});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

     usleep(time_ms);
    msg.uds_req.assign({0x27, 0x0a, 
            // 0x01, 0x02, 0x03, 0x04, 0x01, 0x02, 0x03, 0x04,
            0x01, 0x02, 0x03, 0x04});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    usleep(time_ms);
    msg.uds_req.assign({0x2e, 0xf1, 0x84,
                0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00,});
    try {
        ota_channel->writeAsync(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    /* 刷flashDriver */
    {
        std::cout << "刷flashDriver" << std::endl;
        usleep(time_ms);
        msg.uds_req.assign({0x34, 0x00, 0x44,
                        0x70, 0x10, 0x00, 0x00,
                        0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        usleep(time_ms);
        // 分块处理
        // size_t total_size = fw.size();
        size_t total_size = 0x010000;
        size_t block_size = 0x1000 - 2;
        size_t num_blocks = (total_size + block_size - 1) / block_size;
        uint8_t current_block = 0;
        
        while(current_block < num_blocks)
        {
            size_t offset = current_block * block_size;
            size_t length = std::min(block_size, total_size - offset);
            msg.uds_req.assign(fw.begin() + offset, fw.begin() + offset + length);
            
            // 添加count
            msg.uds_req.insert(msg.uds_req.begin(), current_block + 1);
            // 添加服务头0x36
            msg.uds_req.insert(msg.uds_req.begin(), 0x36);

           
            try {
                ota_channel->writeAsync(msg);
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }

            {
                std::unique_lock<std::mutex> lock(mtx);
                if (cv.wait_for(lock, std::chrono::seconds(5), [] { return responded; })) {
                    if (!success) {
                        std::cerr << "收到负响应，终止发送" << std::endl;
                        break;
                    }
                    // 成功，继续下一块
                    responded = false;
                    current_block++;
                } else {
                    std::cerr << "等待响应超时，终止发送" << std::endl;
                    break;
                }
            }
        }

        usleep(time_ms);
        msg.uds_req.assign({0x37});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        usleep(time_ms);
        msg.uds_req.assign({0x31, 0x01, 0xdd, 0x02,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
    }
    

    /* 刷应用程序 */
    {
        std::cout << "刷应用程序" << std::endl;
        usleep(time_ms);
        msg.uds_req.assign({0x31, 0x01, 0xff, 0x00,
                            0x80, 0x00 ,0x00, 0x00,
                            0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        usleep(time_ms + time_ms);
        msg.uds_req.assign({0x34, 0x00, 0x44,
                        0x80, 0x00, 0x00, 0x00,
                        0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        usleep(time_ms);
        // 分块处理
        // size_t total_size = fw.size();
        size_t total_size = 0x010000;
        size_t block_size = 0x1000 - 2;
        size_t num_blocks = (total_size + block_size - 1) / block_size;
        uint8_t current_block = 0;
        
        while(current_block < num_blocks)
        {
            size_t offset = current_block * block_size;
            size_t length = std::min(block_size, total_size - offset);
            msg.uds_req.assign(fw.begin() + offset, fw.begin() + offset + length);
            
            // 添加count
            msg.uds_req.insert(msg.uds_req.begin(), current_block + 1);
            // 添加服务头0x36
            msg.uds_req.insert(msg.uds_req.begin(), 0x36);

            
            try {
                ota_channel->writeAsync(msg);
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }

            {
                std::unique_lock<std::mutex> lock(mtx);
                if (cv.wait_for(lock, std::chrono::seconds(5), [] { return responded; })) {
                    if (!success) {
                        std::cerr << "收到负响应，终止发送" << std::endl;
                        break;
                    }
                    // 成功，继续下一块
                    responded = false;
                    current_block++;
                } else {
                    std::cerr << "等待响应超时，终止发送" << std::endl;
                    break;
                }
            }
        }

        usleep(time_ms);
        msg.uds_req.assign({0x37});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        usleep(time_ms);
        msg.uds_req.assign({0x31, 0x01, 0xdd, 0x02,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,});
        try {
            ota_channel->writeAsync(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
    }
    

    sleep(5);
}

void ota_sync_write_test() {
    auto ota_channel = DoipFactory::createChannel<OtaDataInfo>(0x0E00,13400);
#if 1    
    const std::string ip1 = "************";
    const std::string ip2 = "************";
#else
    const std::string ip1 = "127.0.0.1";
    const std::string ip2 = "*********";
#endif
    // ota_channel->RegisterListener(listener);
    // ota_channel->RegisterListenerEvent(ListenerEventType::INVALID_MSG | ListenerEventType::TCP_DISCONNECTED);

    OtaDataInfo msg;
    msg.ip = ip1;
    msg.TA = 0x0064;
    

    std::vector<uint8_t> fw;
    try{
        fw = readBinaryFile("./big_file");
    }catch(const DoipException& e){
        std::cerr << e.what() << std::endl;
    }
   
    /* 异步通信 */
    try {
        ota_channel->connect(ip1);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
        return;
    }
    
    msg.uds_req.assign({0x10, 0x01});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    msg.uds_req.assign({0x10, 0x03});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    msg.uds_req.assign({0x31, 0x01, 0x02, 0x03});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    msg.uds_req.assign({0x85, 0x02});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    msg.uds_req.assign({0x28, 0x03, 0x03});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    
    msg.uds_req.assign({0x10, 0x02});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    
    msg.uds_req.assign({0x27, 0x09});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

     
    msg.uds_req.assign({0x27, 0x0a, 
            // 0x01, 0x02, 0x03, 0x04, 0x01, 0x02, 0x03, 0x04,
            0x01, 0x02, 0x03, 0x04});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    
    msg.uds_req.assign({0x2e, 0xf1, 0x84,
                0x00, 0x00, 0x00, 0x00, 0x00,
                0x00, 0x00, 0x00, 0x00, 0x00,});
    try {
        ota_channel->write(msg);
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }
    try {
        ota_channel->read(msg);
        uds_handle(msg); 
        msg.uds_resp.clear();      
    } catch (const DoipException& e) {
        std::cerr << e.what() << std::endl;
    }

    /* 刷flashDriver */
    {
        std::cout << "刷flashDriver" << std::endl;
        
        msg.uds_req.assign({0x34, 0x00, 0x44,
                        0x70, 0x10, 0x00, 0x00,
                        0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        
        // 分块处理
        // size_t total_size = fw.size();
        size_t total_size = 0x010000;
        size_t block_size = 0x1000 - 2;
        size_t num_blocks = (total_size + block_size - 1) / block_size;
        uint8_t current_block = 0;
        
        while(current_block < num_blocks)
        {
            size_t offset = current_block * block_size;
            size_t length = std::min(block_size, total_size - offset);
            msg.uds_req.assign(fw.begin() + offset, fw.begin() + offset + length);
            
            // 添加count
            msg.uds_req.insert(msg.uds_req.begin(), current_block + 1);
            // 添加服务头0x36
            msg.uds_req.insert(msg.uds_req.begin(), 0x36);

           
            try {
                ota_channel->write(msg);
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }
            try {
                ota_channel->read(msg);
                uds_handle(msg); 
                switch (msg.uds_resp[0])
                {
                    case 0x76:
                        //收到0x36的正响应，通知写线程继续发送下一块数据
                        success = true;
                        responded = true;
                        cv.notify_one();
                        break;
                    case 0x7f:
                        if(msg.uds_resp[1] == 0x36)
                        //收到来自0x36的负响应，终止发送
                        success = false;
                        responded = true;
                        cv.notify_one();
                        break;
                    default:
                        break;
                }
                msg.uds_resp.clear();      
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }

            

            {
                std::unique_lock<std::mutex> lock(mtx);
                if (cv.wait_for(lock, std::chrono::seconds(5), [] { return responded; })) {
                    if (!success) {
                        std::cerr << "收到负响应，终止发送" << std::endl;
                        break;
                    }
                    // 成功，继续下一块
                    responded = false;
                    current_block++;
                } else {
                    std::cerr << "等待响应超时，终止发送" << std::endl;
                    break;
                }
            }
        }

        
        msg.uds_req.assign({0x37});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        
        msg.uds_req.assign({0x31, 0x01, 0xdd, 0x02,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
    }
    

    /* 刷应用程序 */
    {
        std::cout << "刷应用程序" << std::endl;
        
        msg.uds_req.assign({0x31, 0x01, 0xff, 0x00,
                            0x80, 0x00 ,0x00, 0x00,
                            0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        
        msg.uds_req.assign({0x34, 0x00, 0x44,
                        0x80, 0x00 ,0x00, 0x00,
                        0x00, 0x01, 0x00, 0x00});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        
        // 分块处理
        // size_t total_size = fw.size();
        size_t total_size = 0x010000;
        size_t block_size = 0x1000 - 2;
        size_t num_blocks = (total_size + block_size - 1) / block_size;
        uint8_t current_block = 0;
        
        while(current_block < num_blocks)
        {
            size_t offset = current_block * block_size;
            size_t length = std::min(block_size, total_size - offset);
            msg.uds_req.assign(fw.begin() + offset, fw.begin() + offset + length);
            
            // 添加count
            msg.uds_req.insert(msg.uds_req.begin(), current_block + 1);
            // 添加服务头0x36
            msg.uds_req.insert(msg.uds_req.begin(), 0x36);

            
            try {
                ota_channel->write(msg);
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }
            try {
                ota_channel->read(msg);
                uds_handle(msg); 
                switch (msg.uds_resp[0])
                {
                    case 0x76:
                        //收到0x36的正响应，通知写线程继续发送下一块数据
                        success = true;
                        responded = true;
                        cv.notify_one();
                        break;
                    case 0x7f:
                        if(msg.uds_resp[1] == 0x36)
                        //收到来自0x36的负响应，终止发送
                        success = false;
                        responded = true;
                        cv.notify_one();
                        break;
                    default:
                        break;
                }
                msg.uds_resp.clear();      
            } catch (const DoipException& e) {
                std::cerr << e.what() << std::endl;
            }

            {
                std::unique_lock<std::mutex> lock(mtx);
                if (cv.wait_for(lock, std::chrono::seconds(5), [] { return responded; })) {
                    if (!success) {
                        std::cerr << "收到负响应，终止发送" << std::endl;
                        break;
                    }
                    // 成功，继续下一块
                    responded = false;
                    current_block++;
                } else {
                    std::cerr << "等待响应超时，终止发送" << std::endl;
                    break;
                }
            }
        }

        
        msg.uds_req.assign({0x37});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }

        
        msg.uds_req.assign({0x31, 0x01, 0xdd, 0x02,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,
                        0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01,
                        0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
                        0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03,
                        0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09,
                        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05,});
        try {
            ota_channel->write(msg);
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
        try {
            ota_channel->read(msg);
            uds_handle(msg); 
            msg.uds_resp.clear();      
        } catch (const DoipException& e) {
            std::cerr << e.what() << std::endl;
        }
    }
    

    sleep(5);
}

int main() {
    // 启动四个客户端线程
    // std::thread client1(ota_async_test);
    // std::thread client2(ota_sync_write_test);
    std::thread client3(ota_async_wtire_test);
    // std::thread client4(startClient4);

    // 等待线程结束
    // client1.join();
    // client2.join();
    client3.join();
    // client4.join();

    return 0;
}