# 设置 CMake 最低版本要求
cmake_minimum_required(VERSION 3.10)

# 设置项目名称
project(MyProject)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)


set(CMAKE_LIBRARY_PATH  ${CMAKE_CURRENT_SOURCE_DIR}/../../install/lib)

find_library(DOIP_CXX_LIB doipcxx)
if(NOT DOIP_CXX_LIB)
    message(FATAL_ERROR "doipcxx library not found!")
endif()

set(INCLUDE_DIRS ${CMAKE_CURRENT_SOURCE_DIR}/../../install/include)

# 添加可执行文件
add_executable(client 
    ${CMAKE_CURRENT_SOURCE_DIR}/tcp_client.cpp
)
target_include_directories(client PRIVATE ${INCLUDE_DIRS})
# 若需要使用库文件，链接相应的库
target_link_libraries(client PRIVATE ${DOIP_CXX_LIB})

add_executable(server 
    ${CMAKE_CURRENT_SOURCE_DIR}/tcp_server.cpp
)

