#!/bin/bash

set -e

SCRIPT_DIR=$(pwd)
echo ${SCRIPT_DIR}

YOCTO_SOS_BUILD_DIR=${SCRIPT_DIR}/../yocto
YOCTO_UOS_BUILD_DIR=${SCRIPT_DIR}/../yocto
ANDROID_BUILD_DIR=${SCRIPT_DIR}/../aosp

OTA_PACKAGE_OUT_DIR=${SCRIPT_DIR}/build_out_ota
TARGET_FILES_HYPERVISOR_ZIP=hypervisor_target_files.zip
OTA_PACKAGE_ZIP=otapackage_full.zip

help_flag=false

options=$(getopt -o ho: -l help,out:,yocto-sos-dir:,yocto-uos-dir:,android-dir: -- "$@")
if [ $? != 0 ]; then
    echo "getopt error"
    exit 1
fi
eval set -- "$options"
while true; do
    case "$1" in
        -h | --help)
            help_flag=true
            shift
            ;;
        -o | --out)
            OTA_PACKAGE_OUT_DIR=$2
            shift 2
            ;;
        --yocto-sos-dir)
            YOCTO_SOS_BUILD_DIR=$(cd $2;pwd)
            shift 2
            ;;
        --yocto-uos-dir)
            YOCTO_UOS_BUILD_DIR=$(cd $2;pwd)
            shift 2
            ;;
        --android-dir)
            ANDROID_BUILD_DIR=$(cd $2;pwd)
            shift 2
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            help_flag=true
            ;;
    esac
done


# ---------- functions ---------
show_helps() {
    echo "Usage: $0 [-h]"
    echo "  -h, --help              Display this help message"
    echo "  -o, --out               Output path for ota packages"
    echo "  --yocto-sos-dir         Set Yocto sos/host build root dir"
    echo "  --yocto-uos-dir         Set Yocto uos/guest(tbox) build root dir"
    echo "  --android-dir           Set Android build root dir"
}

clean_and_mkdir() {
    if [ -d $1 ]; then
        rm -rf $1
    fi
    mkdir -vp $1
}

build_target_files(){
    TARGET_FILES_BUILD_SCRIPT=${ANDROID_BUILD_DIR}/device/mediateksample/auto8678p1_64_bsp_vm_uos/hypervisor_multi_os_merge_targetfiles.py
    TARGET_FILES_YOCTO_SOS_ZIP=${YOCTO_SOS_BUILD_DIR}/build/tmp/deploy/images/auto8678p1_64_sos/yocto_target_files.zip
    TARGET_FILES_YOCTO_UOS_ZIP=${YOCTO_UOS_BUILD_DIR}/build/tmp/deploy/images/auto8678p1_64_uos_tbox/uos_tbox_target_files.zip
    TARGET_FILES_ANDROID_OS_ZIP=${ANDROID_BUILD_DIR}/out/target/product/auto8678p1_64_bsp_vm_uos/merged/target_files.zip

    cd ${OTA_PACKAGE_OUT_DIR}
    python3 ${TARGET_FILES_BUILD_SCRIPT} \
            ${TARGET_FILES_YOCTO_SOS_ZIP} \
            ${TARGET_FILES_YOCTO_UOS_ZIP} \
            ${TARGET_FILES_ANDROID_OS_ZIP}
    cd ${SCRIPT_DIR}
}

build_ota_package() {
    # check target out zip exists or not
    if [ -f ${OTA_PACKAGE_OUT_DIR}/${TARGET_FILES_HYPERVISOR_ZIP} ];then
        echo "found target files zip: ${TARGET_FILES_HYPERVISOR_ZIP}"
    else
        echo "Can not find target files zip: ${TARGET_FILES_HYPERVISOR_ZIP}!"
        exit 1
    fi

    cd ${ANDROID_BUILD_DIR}
    LUNCH_TARGET_PROJECT=sys_mssi_auto_64_cn_armv82_vm_uos-userdebug
    OTA_BUILD_DIR=out_sys/host/linux-x86
    OTA_BUILD_BIN=${OTA_BUILD_DIR}/bin/ota_from_target_files
    OTA_KEY_PATH=build/make/target/product/security/testkey

    source build/envsetup.sh
    lunch ${LUNCH_TARGET_PROJECT}

    ${OTA_BUILD_BIN} -v -block --skip_postinstall \
                     -p ${OTA_BUILD_DIR} \
                     -k ${OTA_KEY_PATH} \
                     ${OTA_PACKAGE_OUT_DIR}/${TARGET_FILES_HYPERVISOR_ZIP} \
                     ${OTA_PACKAGE_OUT_DIR}/${OTA_PACKAGE_ZIP}
    cd ${SCRIPT_DIR}
}

# -------- main ----------
if [ ${help_flag} = true ]; then
    show_helps
    exit 0
fi

clean_and_mkdir ${OTA_PACKAGE_OUT_DIR}
OTA_PACKAGE_OUT_DIR=$(cd ${OTA_PACKAGE_OUT_DIR};pwd)

build_target_files
build_ota_package
