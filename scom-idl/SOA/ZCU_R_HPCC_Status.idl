/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_R_HPCC_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_R_HPCC_Status.idl
*/

module Seres {
    module ZCU_R_HPCC {

        typedef uint8 notifyNormalizedStatus; // 前遮阳帘初始化状态

        typedef uint8 notifyLearnedStatus; // 学习状态

        typedef uint8 notifySunshadeActuateStatus; // 运行状态定义

        typedef uint8 notifyPosition; // 开度

        typedef uint8 notifyAntiPinch; // 是否防夹

        typedef uint16 notifyPositionCount; // 上报前遮阳帘位置计数

        typedef uint8 BCM_Legacy_OtherFltFlag; // 其他故障标志

        typedef uint8 BCM_Legacy_IGRelayFlag; // LIN状态故障标志

        typedef uint8 BCM_Legacy_ECUFlag; // 传感器故障标志

        typedef uint8 BCM_Legacy_LINFlag; // 电机过温故障标志

        typedef uint8 BCM_Legacy_HallSensorFlag; // 欠压故障标志

        typedef uint8 BCM_Legacy_MotorOverTempFlag; // 过压故障标志

        typedef uint8 BCM_Legacy_UnderVoltFlag; // 欠压故障标志

        typedef uint8 BCM_Legacy_OverVoltFlag; // 过压故障标志

        @nested
        struct notifySunshadeFltSt { // 天窗遮阳帘故障状态
            BCM_Legacy_OverVoltFlag overvoltflag; // 过压故障标志
            BCM_Legacy_UnderVoltFlag undervoltflag; // 欠压故障标志
            BCM_Legacy_MotorOverTempFlag motorovertempflag; // 电机过温故障标志
            BCM_Legacy_HallSensorFlag hallsensorflag; // 传感器故障标志
            BCM_Legacy_LINFlag linflag; // LIN状态故障标志
            BCM_Legacy_ECUFlag ecuflag; // ECU状态故障标志
            BCM_Legacy_IGRelayFlag igrelayflag; // 继电器故障标志
            BCM_Legacy_OtherFltFlag otherfltflag; // 其他故障标志
        };

        typedef uint8 RearDefrostStatus; // 后除霜加热状态

        typedef float TMS_Temperature; // TMS温度信息

        typedef boolean wipingStatus; // 雨刮状态

        typedef uint8 ReturnSigStatus; // 雨刮停靠位状态

        typedef uint8 ACoutletId; // 出风口编号

        @nested
        struct ACoutletTempvalue { // 出风口温度值
            ACoutletId acoutletid; // 出风口编号
            TMS_Temperature acoutlettemp; // 出风口温度
        };

        typedef sequence<ACoutletTempvalue, 30> ACoutletTempSt; // 出风口温度状态

        typedef uint8 TMS_DoorPosn; // TMS电机位置

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        @nested
        struct TMS_TempDoorPosnValue { // 温区风门电机位置状态
            ACZoneId zoneid; // 温区编号
            TMS_DoorPosn tms_tempdoorposn; // 温区风门电机位置
        };

        typedef sequence<TMS_TempDoorPosnValue, 20> TMS_TempDoorPosnst; // 温区风门电机位置状态

        @nested
        struct TMS_AirMode { // 空调出风模式
            TMS_DoorPosn face; // 吹面电机
            TMS_DoorPosn foot; // 吹脚电机
            TMS_DoorPosn defrost; // 除霜电机
        };

        @nested
        struct TMS_WindModeSt { // 温区吹风模式信息
            ACZoneId zoneid; // 温区编号
            TMS_AirMode mode; // 温区吹风模式
        };

        typedef uint8 ACBlwLevel; // 风速档位

        @nested
        struct TMS_WindLevelValue { // 温区风速档位值
            ACZoneId zoneid; // 温区
            ACBlwLevel level; // 风量
        };

        typedef sequence<TMS_WindLevelValue, 20> TMS_WindLevelSt; // 温区风速档位信息

        @final
        struct ZCU_R_HPCC_Status{
            // 上报温区的风速档位
            @optional TMS_WindLevelSt TMS_VentilationSt_notifyLevel_ZCUR;

            // 上报温区的出风模式
            @optional TMS_WindModeSt TMS_VentilationSt_notifyMode_ZCUR;

            // 上报温区风门电机位置
            @optional TMS_TempDoorPosnst TMS_VentilationSt_notifyTemp_ZCUR;

            // 上报出风口温度
            @optional ACoutletTempSt TMS_VentilationSt_notifyAirOutletTemp_ZCUR;

            // 上报雨刮停靠位状态
            @optional ReturnSigStatus BCM_WiperWash_R_notifyReturnSigStatus;

            // 上报雨刮的开启状态
            @optional wipingStatus BCM_WiperWash_R_notifyWipingStatus;

            // 上报后蒸发器温度
            @optional TMS_Temperature TMS_VentilationSt_notifyREvpTemp_ZCUR;

            // 上报后HAVC进风口温度
            @optional TMS_Temperature TMS_VentilationSt_notifyRHAVCInteriorTemp_ZCUR;

            // 后挡风玻璃加热状态
            @optional RearDefrostStatus TMS_Demister_Re_RearDefrostStatus;

            // 上报后天窗遮阳帘故障状态
            @optional notifySunshadeFltSt BCM_Sunshade_R_notifySunshadeFltSt;

            // 上报后遮阳帘位置计数
            @optional notifyPositionCount BCM_Sunshade_R_notifyPositionCount;

            // 上报后天窗遮阳帘防夹状态
            @optional notifyAntiPinch BCM_Sunshade_R_notifyAntiPinch;

            // 上报后天窗遮阳帘动态位置
            @optional notifyPosition BCM_Sunshade_R_notifyPosition;

            // 上报后天窗遮阳帘状态
            @optional notifySunshadeActuateStatus BCM_Sunshade_R_notifySunshadeActuateStatus;

            // 上报后天窗遮阳帘的学习状态
            @optional notifyLearnedStatus BCM_Sunshade_R_notifyLearnedStatus;

            // 上报后遮阳帘初始化状态
            @optional notifyNormalizedStatus BCM_Sunshade_R_notifyNormalizedStatus;

        };
    };
};
