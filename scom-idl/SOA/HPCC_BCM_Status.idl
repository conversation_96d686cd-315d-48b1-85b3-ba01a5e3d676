/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/HPCC_BCM_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/HPCC_BCM_Status.idl
*/

module Seres {
    module HPCC_BCM {

        enum ServiceAction { // 业务行为
            @value(0x0) ServiceAction_NO_REQUEST,
            @value(0x1) ServiceAction_REQUEST,
            @value(0x2) ServiceAction_RELEASE,
            @value(0x3) ServiceAction_RESERVED
        };

        typedef uint16 ServiceName; // 业务名称

        @nested
        struct SysModeServPara { // 系统模式业务并发请求和释放参数
            ServiceName servicename; // 业务名称
            ServiceAction serviceaction; // 业务行为
        };

        typedef uint32 HPCC_RGB; // 灯颜色

        typedef uint8 HCPP_AmbZoneID; // 氛围灯分区

        @nested
        struct HPCC_AmbLightRGBInsStatus { // 单个区域氛围灯颜色状态
            HCPP_AmbZoneID ambzoneid; // 氛围灯区域
            HPCC_RGB rgb; // 颜色
        };

        typedef sequence<HPCC_AmbLightRGBInsStatus, 3> HPCC_AmbLightRGBStatus; // 氛围灯颜色状态

        typedef uint8 HPCC_Brightness; // 灯亮度

        @nested
        struct HPCC_AmbLightBriInsSts { // 单个区域氛围灯亮度状态
            HCPP_AmbZoneID ambzoneid; // 氛围灯区域
            HPCC_Brightness bright; // 亮度
        };

        typedef sequence<HPCC_AmbLightBriInsSts, 3> HPCC_AmbLightBrightnessStauts; // 氛围灯亮度状态

        typedef uint8 HPCC_AmbientLightTheme; // 氛围灯主题

        typedef uint8 HPCC_SunRoofSts; // 遮阳帘状态

        typedef uint8 HPCC_SunRoofID; // 遮阳帘id

        @nested
        struct HPCC_SunRoofInstanceSts { // 单个遮阳帘状态
            HPCC_SunRoofID sunroofid; // 遮阳帘id
            HPCC_SunRoofSts actstatus; // 遮阳帘状态
        };

        typedef sequence<HPCC_SunRoofInstanceSts, 2> HPCC_SunRoofStatus; // 遮阳帘状态

        typedef uint8 HPCC_ChildVentilationsts; // 座椅通风状态_child

        typedef uint8 HPCC_SeatID; // 座椅ID

        @nested
        struct HPCC_SeatChildVentilationStatus { // 单个儿童座椅通风状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_ChildVentilationsts childventilationsts; // 座椅通风状态_child
        };

        typedef sequence<HPCC_SeatChildVentilationStatus, 6> HPCC_SeatChildVentilationStatusAll; // 儿童座椅通风状态

        typedef uint8 HPCC_OnOffCmd; // 开关设置

        @nested
        struct HPCC_childheatstsAll { // 单个儿童座椅加热状态
            HPCC_SeatID seatid; // 座椅ID
            HPCC_OnOffCmd childheatsts; // 加热状态
        };

        typedef uint8 HPCC_MassageStrength; // 座椅按摩等级

        @nested
        struct HPCC_SeatMassageInstanveSts { // 单个座椅按摩状态
            HPCC_SeatID seatid; // 座椅ID
            HPCC_MassageStrength massagests; // 按摩状态
        };

        typedef sequence<HPCC_SeatMassageInstanveSts, 6> HPCC_SeatMassageStatus; // 座椅按摩状态

        typedef uint8 HPCC_VentilationLevel; // 座椅通风等级

        @nested
        struct HPCC_SeatVenInstanceSts { // 单个座椅通风状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_VentilationLevel ventilationlevel; // 通风等级
        };

        typedef sequence<HPCC_SeatVenInstanceSts, 6> HPCC_SeatVentilationStatus; // 座椅通风等级状态

        typedef uint8 HPCC_HeatLevel; // 座椅加热等级

        @nested
        struct HPCC_SeatHeatLevelInstanceSts { // 单个座椅加热等级状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_HeatLevel heatlevel; // 加热等级
        };

        typedef sequence<HPCC_SeatHeatLevelInstanceSts, 6> HPCC_SeatHeatLevelStatus; // 座椅加热等级状态

        typedef uint8 HPCC_SeatFoldSts; // 座椅展开状态

        @nested
        struct HPCC_SeatFoldInstanceSts { // 单个座椅折叠状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_SeatFoldSts seatfoldsts; // 展开状态
        };

        typedef sequence<HPCC_SeatFoldInstanceSts, 6> HPCC_SeatFoldStatus; // 座椅折叠状态

        @nested
        struct HPCC_ZeroGravityClildInstanceSts { // 单个零重力座椅儿童锁状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_OnOffCmd onoffcmd; // 开关状态
        };

        typedef sequence<HPCC_ZeroGravityClildInstanceSts, 2> HPCC_ZeroGravityChildStatus; // 零重力座椅儿童锁状态

        @nested
        struct HPCC_ZeroGravityInstanceSts { // 单个零重力座椅状态
            HPCC_SeatID seat; // 座椅ID
            HPCC_SeatFoldSts seatfoldsts; // 展开状态
        };

        typedef sequence<HPCC_ZeroGravityInstanceSts, 2> HPCC_ZeroGravityStatus; // 零重力座椅状态

        typedef uint8 HPCC_LrmLVStatus; // 负载状态

        typedef uint8 HPCC_HighVoltStatus; // 低压下电状态

        typedef uint8 HPCC_RecircleMode; // 空调循环模式

        typedef uint8 HPCC_ClimateMode_Op; // 空调风量模式

        typedef uint8 HPCC_ClimateID; // 空调ID

        @nested
        struct HPCC_ClimateInstanceModeStatus { // 单个空调模式状态
            HPCC_ClimateID climateid; // 空调编号
            HPCC_ClimateMode_Op climatemode; // 模式
            HPCC_OnOffCmd onoffcmd; // 开关状态
        };

        typedef sequence<HPCC_ClimateInstanceModeStatus, 3> HPCC_ClimateModeStatus; // 空调模式状态

        typedef uint8 ACBlwLevel; // 风速档位

        @nested
        struct HPCC_ClimateInstanceLevelStatus { // 单个空调挡位状态
            HPCC_ClimateID climateid; // 空调编号
            ACBlwLevel climatelevel; // 挡位
        };

        typedef sequence<HPCC_ClimateInstanceLevelStatus, 3> HPCC_ClimateLevelStatus; // 空调风量挡位状态

        typedef float HPCC_Temperature; // 温度设置

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        @nested
        struct HPCC_ClimateInstanceTemStatus { // 单个温区温度状态
            ACZoneId zoneid; // 空调温区编号
            HPCC_Temperature temperature; // 温度值
        };

        typedef sequence<HPCC_ClimateInstanceTemStatus, 6> HPCC_ClimateTemStatus; // 空调温度状态

        @nested
        struct HPCC_ClimateInstanceAutoStatus { // 单个空调AUTO模式状态
            HPCC_ClimateID climateid; // 空调编号
            HPCC_OnOffCmd onoffcmd; // 开关状态
        };

        typedef sequence<HPCC_ClimateInstanceAutoStatus, 3> HPCC_ClimateAutoStatus; // 空调AUTO开关状态

        @nested
        struct HPCC_ClimateInstanceStatus { // 单个空调状态
            HPCC_ClimateID climateid; // 空调编号
            HPCC_OnOffCmd onoffcmd; // 开关状态
        };

        typedef sequence<HPCC_ClimateInstanceStatus, 3> HPCC_ClimateStatus; // 空调开关状态

        typedef uint8 HPCC_OpenSpeed; // 车门开启关闭速度

        typedef uint8 HPCC_TargetPosition; // 尾门最大开度状态

        typedef uint8 HPCC_DoorID; // 侧门ID

        @nested
        struct HPCC_DoorMaxPosStatusInstance { // 单个门最大开启角度状态
            HPCC_DoorID doorid; // 门ID
            HPCC_TargetPosition position; // 门最大开启角度
        };

        typedef sequence<HPCC_DoorMaxPosStatusInstance, 4> HPCC_DoorMaxPosStatus; // 门锁状态

        typedef uint8 HPCC_DoorOpSts; // 充电/加油口盖状态

        @nested
        struct HPCC_DoorInstanceSts { // 单个车门状态
            HPCC_DoorID doorid; // 门ID
            HPCC_DoorOpSts doorinstancests; // 门状态
        };

        typedef sequence<HPCC_DoorInstanceSts, 4> HPCC_DoorSts; // 车门状态

        typedef uint8 HPCC_LockUnLock; // 解闭锁状态

        typedef uint8 HPCC_TailGateSts; // 尾门状态

        @nested
        struct HPCC_DoorLockStatusInstance { // 单个门锁状态
            HPCC_DoorID doorid; // 门ID
            HPCC_LockUnLock lockstatus; // 门锁状态
        };

        typedef sequence<HPCC_DoorLockStatusInstance, 4> HPCC_DoorLockStatus; // 门锁状态

        typedef uint8 HPCC_WindowPosition; // 车窗开度

        typedef uint8 HPCC_WindowID; // 车窗ID

        @nested
        struct HPCC_WindowPosInstance { // 单个车窗开度状态
            HPCC_WindowID windowid; // 窗户id
            HPCC_WindowPosition position; // 开度
        };

        typedef sequence<HPCC_WindowPosInstance, 4> HPCC_WindowPositionStatus; // 车窗开度状态

        @nested
        struct HPCC_ChildLocksts { // 儿童锁状态
            HPCC_LockUnLock lockstatus; // 儿童锁开关状态（解闭锁状态）
            HPCC_DoorID doorid; // 门ID
            HPCC_WindowID windowid; // 窗户id
        };

        @final
        struct HPCC_BCM_Status{
            // 主驾单独解锁状态
            @optional HPCC_OnOffCmd drvlocksts;

            // 蓝牙钥匙远离闭锁状态
            @optional HPCC_OnOffCmd awaylockswt;

            // 蓝牙钥匙靠近解锁状态
            @optional HPCC_OnOffCmd approachunlockswt;

            // 童锁状态
            @optional HPCC_ChildLocksts childlocksts;

            // 车窗开度状态
            @optional HPCC_WindowPositionStatus windowpossts;

            // 车门锁状态
            @optional HPCC_DoorLockStatus doorlocksts;

            // 尾门状态
            @optional HPCC_TailGateSts tailgatests;

            // 尾门开度状态
            @optional HPCC_TargetPosition targetposition;

            // 前舱盖状态
            @optional HPCC_LockUnLock frnthatchsts;

            // 充电口盖状态
            @optional HPCC_DoorOpSts chrgportsts;

            // 加油口盖状态
            @optional HPCC_DoorOpSts fuelfillersts;

            // 车门状态
            @optional HPCC_DoorSts doorsts;

            // 电动车门手动模式
            @optional HPCC_OnOffCmd doormanuamode;

            // 电动车门最大开启角度
            @optional HPCC_DoorMaxPosStatus doormaxpossts;

            // 电动车门开启关闭速度
            @optional HPCC_OpenSpeed openspeed;

            // 踩刹车自动关闭主驾门
            @optional HPCC_OnOffCmd brakepadclosedoor;

            // 空调开闭状态
            @optional HPCC_ClimateStatus climatests;

            // 空调Auto控制状态
            @optional HPCC_ClimateAutoStatus climateautosts;

            // 空调温度状态
            @optional HPCC_ClimateTemStatus climatetemstatus;

            // 温度一键同步状态
            @optional HPCC_OnOffCmd temperaturesyncsts;

            // 空调风量状态
            @optional HPCC_ClimateLevelStatus climatelevelsts;

            // 空调风量模式状态
            @optional HPCC_ClimateModeStatus climatemodestatus;

            // 最大制冷状态
            @optional HPCC_OnOffCmd maxacsts;

            // 最大制热状态
            @optional HPCC_OnOffCmd maxheatsts;

            // AC控制状态
            @optional HPCC_OnOffCmd acsts;

            // 前除霜状态
            @optional HPCC_OnOffCmd frntdemiststs;

            // 后除霜状态
            @optional HPCC_OnOffCmd reardemiststs;

            // 自动除霜状态
            @optional HPCC_OnOffCmd autodemiststs;

            // 一键空气净化状态
            @optional HPCC_OnOffCmd airpurifysts;

            // 二排空调锁屏状态
            @optional HPCC_OnOffCmd rearaclocksts;

            // 驻车自动通风状态
            @optional HPCC_OnOffCmd parkautoventilationsts;

            // 空调循环模式
            @optional HPCC_RecircleMode recirclemodests;

            // 智能温区状态
            @optional HPCC_OnOffCmd smartzones;

            // 低压下电状态
            @optional HPCC_HighVoltStatus highvoltsts;

            // 负载状态
            @optional HPCC_LrmLVStatus lrmlvsts;

            // 车载电源口功能状态
            @optional HPCC_OnOffCmd voltagePortsts;

            // 零重力座椅状态
            @optional HPCC_ZeroGravityStatus zerogravitysts;

            // 零重力座椅儿童锁状态
            @optional HPCC_ZeroGravityChildStatus zerogravitychildlocksts;

            // 座椅折叠状态
            @optional HPCC_SeatFoldStatus seatfoldsts;

            // 座椅加热等级状态
            @optional HPCC_SeatHeatLevelStatus seatheatlevelsts;

            // 座椅通风等级状态
            @optional HPCC_SeatVentilationStatus seatventilationsts;

            // 座椅按摩强度/模式状态
            @optional HPCC_SeatMassageStatus seatmassagests;

            // 儿童座椅加热状态
            @optional HPCC_childheatstsAll childheatstsall;

            // 儿童座椅通风状态
            @optional HPCC_SeatChildVentilationStatusAll childventilationstsall;

            // 儿童遗落提醒状态
            @optional HPCC_OnOffCmd childleftbehindsts;

            // 遮阳帘状态
            @optional HPCC_SunRoofStatus sunroofststus;

            // 闭锁升窗设置状态
            @optional HPCC_OnOffCmd windowlockupsts;

            // 闭锁自动关闭遮阳帘状态
            @optional HPCC_OnOffCmd lockclosesunroofsts;

            // 顶灯开启关闭状态
            @optional HPCC_OnOffCmd rooflightsts;

            // 氛围灯主题状态
            @optional HPCC_AmbientLightTheme amblightthemests;

            // 氛围灯亮度状态
            @optional HPCC_AmbLightBrightnessStauts amblightbrightnesssts;

            // 氛围灯颜色状态
            @optional HPCC_AmbLightRGBStatus amblightrgbsts;

            // 颜色&亮度一键同步开关状态
            @optional HPCC_OnOffCmd rgbbrightnessstatus;

            // 迎宾灯语开关状态
            @optional HPCC_OnOffCmd wellightstatus;

            // 小憩模式开关状态
            @optional HPCC_OnOffCmd napstatus;

            // 后方来车预警（DOW）& 转向盲点提示（BSD）灯语功能状态
            @optional HPCC_OnOffCmd dowbsdlightstatus;

            // 语音交互灯语功能状态
            @optional HPCC_OnOffCmd voicelightstatus;

            // 系统模式请求执行结果
            @optional SysModeServPara SysModeServPara;

        };
    };
};
