/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_FL_HPCC_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_FL_HPCC_Status.idl
*/

module Seres {
    module ZCU_FL_HPCC {

        typedef uint8 notifyNormalizedStatus; // 前遮阳帘初始化状态

        typedef uint8 notifyLearnedStatus; // 学习状态

        typedef uint8 notifySunshadeActuateStatus; // 运行状态定义

        typedef uint8 notifyPosition; // 开度

        typedef uint8 notifyAntiPinch; // 是否防夹

        typedef uint16 notifyPositionCount; // 上报前遮阳帘位置计数

        typedef uint8 BCM_Legacy_OtherFltFlag; // 其他故障标志

        typedef uint8 BCM_Legacy_IGRelayFlag; // LIN状态故障标志

        typedef uint8 BCM_Legacy_ECUFlag; // 传感器故障标志

        typedef uint8 BCM_Legacy_LINFlag; // 电机过温故障标志

        typedef uint8 BCM_Legacy_HallSensorFlag; // 欠压故障标志

        typedef uint8 BCM_Legacy_MotorOverTempFlag; // 过压故障标志

        typedef uint8 BCM_Legacy_UnderVoltFlag; // 欠压故障标志

        typedef uint8 BCM_Legacy_OverVoltFlag; // 过压故障标志

        @nested
        struct notifySunshadeFltSt { // 天窗遮阳帘故障状态
            BCM_Legacy_OverVoltFlag overvoltflag; // 过压故障标志
            BCM_Legacy_UnderVoltFlag undervoltflag; // 欠压故障标志
            BCM_Legacy_MotorOverTempFlag motorovertempflag; // 电机过温故障标志
            BCM_Legacy_HallSensorFlag hallsensorflag; // 传感器故障标志
            BCM_Legacy_LINFlag linflag; // LIN状态故障标志
            BCM_Legacy_ECUFlag ecuflag; // ECU状态故障标志
            BCM_Legacy_IGRelayFlag igrelayflag; // 继电器故障标志
            BCM_Legacy_OtherFltFlag otherfltflag; // 其他故障标志
        };

        typedef uint8 HMI_FaultStatus; // 开关故障状态

        typedef uint8 HMI_KeySwtMode; // 按键开关指令

        typedef uint8 notifyRLSWindowCtrlReq; // 下雨车窗控制请求

        typedef uint8 WinSwtntfFltSt; // 开关故障状态

        typedef uint8 WinSwtntfSt; // 车窗升降开关指令

        enum notifyLockStatus { // 车窗锁的状态
            @value(0x0) BCM_LockStatus_Window_LOCKED,
            @value(0x1) BCM_LockStatus_Window_UNLOCKED,
            @value(0xFF) BCM_LockStatus_Window_UNKNOWN
        };

        typedef uint8 WinnotifyPosition; // 车窗开度

        enum WinnotifyActuateStatus { // 车窗执行状态
            @value(0x0) BCM_ActuateStatus_IDLE,
            @value(0x1) BCM_ActuateStatus_CLOSING_BACKWARD_FOLDING_DOWNWARD_DECREASING,
            @value(0x2) BCM_ActuateStatus_OPENING_FORWARD_UNFOLDING_UPWARD_INCREASING,
            @value(0x3) BCM_ActuateStatus_STALL,
            @value(0x4) BCM_ActuateStatus_IDLE_MOTOR_FAULT,
            @value(0x5) BCM_ActuateStatus_IDLE_ANTIPINCH_RESTRAIN,
            @value(0x6) BCM_ActuateStatus_IDLE_HEAT_PROTECTION,
            @value(0xFF) BCM_ActuateStatus_UNKNOWN
        };

        typedef boolean WinnotifyAntiPinch; // 车窗是否防夹

        enum WinnotifyLearnedStatus { // 车窗学习状态
            @value(0x0) BCM_LearnedStatus_UNFINISHED,
            @value(0x1) BCM_LearnedStatus_FINISHED,
            @value(0xFF) BCM_LearnedStatus_UNKNOWN
        };

        typedef uint8 RearDefrostStatus; // 后除霜加热状态

        typedef uint8 FoldUnfoldStatus; // 后视镜折叠状态

        typedef uint8 Percent; // 百分比

        typedef uint8 LeRiAdjSwtMode; // 外后视镜左右调节开关状态

        typedef uint8 UpDwnAdjSwtMode; // 外后视镜上下调节开关状态

        typedef uint8 ThermalReq; // 电池热管理请求

        typedef boolean runningStatus; // 喇叭的运行状态

        enum CS_SteerWhlFaultStatus { // 方向盘故障定义
            @value(0x0) CS_SteerWhlFaultStatus_NORMAL,
            @value(0x1) CS_SteerWhlFaultStatus_FAULT,
            @value(0xFF) CS_SteerWhlFaultStatus_INVALID
        };

        @nested
        struct SteerWhl_FaultStatus { // 方向盘故障状态
            CS_SteerWhlFaultStatus heatingsysfault; // 方向盘加热系统故障状态
            CS_SteerWhlFaultStatus adjustingsysfault; // 方向盘调节系统故障状态
        };

        typedef uint8 SteerWhl_HeatrStatus; // 方向盘加热状态

        typedef uint8 SteerWhl_Height; // 高度

        typedef uint8 SteerWhl_Tilt; // 倾角

        typedef uint8 SteerWhl_LearnedStatus; // 自学习完成状态

        typedef uint8 Zpos_HallMotStatus; // Z方向电机堵转

        typedef uint8 Xpos_HallMotStatus; // X方向电机堵转

        typedef uint16 RSM_SolarInten; // 太阳辐射强度

        typedef uint8 ReWashSwtSt; // 后洗涤开关状态

        typedef uint8 ReWiprSwtSt; // 后雨刮开关状态

        typedef uint8 FrntWashSwtSt; // 前洗涤开关状态

        typedef uint8 FrntWiprMistSwtSt; // 前雨刮点刮开关状态

        typedef uint8 FrntWiprModSwtSt; // 前雨刮开关状态

        typedef uint8 Wpr_ActvSt; // 雨刮激活状态

        typedef uint8 Wpr_SpclPosn; // 雨刮刮臂服务位置

        typedef uint8 Wpr_TurnPosnFb; // 翻转点反馈

        typedef uint8 Wpr_ParkAr; // 停靠位反馈

        typedef uint8 Wpr_CrkAg; // 曲柄角度反馈

        typedef uint8 Wpr_Spd; // 刮刷速度反馈

        typedef uint8 Wpr_MovgDir; // 运动方向反馈

        typedef uint8 Wpr_PlantM; // 工厂模式状态反馈

        typedef uint8 BCM_Legacy_ComFltFlag; // 通信故障标志

        typedef uint8 BCM_Legacy_WindingOpenFlag; // 绕组开路故障标志

        typedef uint8 BCM_Legacy_WindingShortFlag; // 绕组短路故障标志

        typedef uint8 BCM_Legacy_DrvrOpenLoadFlag; // 驱动开路故障标志

        typedef uint8 BCM_Legacy_DrvrShortToPwrFlag; // 驱动对电源短路故障标志

        typedef uint8 BCM_Legacy_DrvrShortToGndFlag; // 驱动对地短路故障标志

        typedef uint8 BCM_Legacy_NoLoadFlag; // 空转故障标志

        typedef uint8 BCM_Legacy_StallFlag; // 堵转故障标志

        typedef uint8 BCM_Legacy_MotOverTempFlag; // 电机过温故障标志

        typedef uint8 BCM_Legacy_EcuOverTempFlag; // 控制器过温故障标志

        typedef uint8 BCM_Legacy_OverCurrFlag; // 过流故障标志

        @nested
        struct BCM_Legacy_MotFltSt { // 电机故障状态
            BCM_Legacy_OverCurrFlag overcurrflag; // 过流故障标志
            BCM_Legacy_OverVoltFlag overvoltflag; // 过压故障标志
            BCM_Legacy_UnderVoltFlag undervoltflag; // 欠压故障标志
            BCM_Legacy_EcuOverTempFlag ecuovertempflag; // 控制器过温故障标志
            BCM_Legacy_MotOverTempFlag motovertempflag; // 电机过温故障标志
            BCM_Legacy_StallFlag stallflag; // 堵转故障标志
            BCM_Legacy_NoLoadFlag noloadflag; // 空转故障标志
            BCM_Legacy_DrvrShortToGndFlag drvrshorttogndflag; // 驱动对地短路故障标志
            BCM_Legacy_DrvrShortToPwrFlag drvrshorttopwrflag; // 驱动对电源短路故障标志
            BCM_Legacy_DrvrOpenLoadFlag drvropenloadflag; // 驱动开路故障标志
            BCM_Legacy_WindingShortFlag windingshortflag; // 绕组短路故障标志
            BCM_Legacy_WindingOpenFlag windingopenflag; // 绕组开路故障标志
            BCM_Legacy_ComFltFlag comfltflag; // 通信故障标志
            BCM_Legacy_OtherFltFlag otherfltflag; // 其他故障标志
        };

        typedef float TMS_Temperature; // TMS温度信息

        @final
        struct ZCU_FL_HPCC_Status{
            // 上报前排车内温度传感器
            @optional TMS_Temperature TMS_EnvMonitorst_notifyFInteriorTemperature;

            // 上报FWM故障标志位
            @optional BCM_Legacy_MotFltSt BCM_Wiper_F_ntfFltSt;

            // 工厂模式状态反馈
            @optional Wpr_PlantM BCM_Wiper_F_notifyWipingPlantMode;

            // 运动方向反馈
            @optional Wpr_MovgDir BCM_Wiper_F_notifyWipingMovingDir;

            // 刮刷速度反馈
            @optional Wpr_Spd BCM_Wiper_F_notifyWipingSpeed;

            // 曲柄角度反馈
            @optional Wpr_CrkAg BCM_Wiper_F_notifyWipingCrankAngle;

            // 停靠位反馈
            @optional Wpr_ParkAr BCM_Wiper_F_notifyWipingParkingStatus;

            // 翻转点反馈
            @optional Wpr_TurnPosnFb BCM_Wiper_F_notifyWipingTurnPosition;

            // 上报雨刮刮臂服务位置
            @optional Wpr_SpclPosn BCM_Wiper_F_notifyWprSpclPosn;

            // 上报雨刮激活状态
            @optional Wpr_ActvSt BCM_Wiper_F_notifyWprActvSt;

            // 上报车外环境温度
            @optional TMS_Temperature TMS_EnvMonitorst_notifyExteriorTemperature;

            // 上报前雨刮模式开关状态
            @optional FrntWiprModSwtSt BCM_WiprCombSwt_ntfFrntWiprModSwtSt;

            // 上报前雨刮点刮开关状态
            @optional FrntWiprMistSwtSt BCM_WiprCombSwt_ntfFrntWiprMistSwtSt;

            // 上报前洗涤开关状态
            @optional FrntWashSwtSt BCM_WiprCombSwt_ntfFrntWashSwtSt;

            // 上报后雨刮开关状态
            @optional ReWiprSwtSt BCM_WiprCombSwt_ntfReWiprSwtSt;

            // 上报后洗涤开关状态
            @optional ReWashSwtSt BCM_WiprCombSwt_ntfReWashSwtSt;

            // 上报左太阳光强度
            @optional RSM_SolarInten TMS_EnvMonitorst_notifyExteriorRSM_LeSolarInten;

            // 上报右太阳光强度
            @optional RSM_SolarInten TMS_EnvMonitorst_notifyExteriorRSM_RiSolarInten;

            // 上报X方向电机堵转
            @optional Xpos_HallMotStatus BCM_SteerWheelXpos_notifyHallMotStatus;

            // 上报Z方向电机堵转
            @optional Zpos_HallMotStatus BCM_SteerWheelZpos_notifyHallMotStatus;

            // 上报X前后倾角调整电机的自学习完成状态
            @optional SteerWhl_LearnedStatus BCM_SteerWheel_notifyXmotorLeanedStat;

            // 上报Z上下高度调整电机的自学习完成状态
            @optional SteerWhl_LearnedStatus BCM_SteerWheel_notifyZmotorLeanedStat;

            // 上报方向盘前后（X）位置
            @optional SteerWhl_Tilt BCM_SteerWheel_notifyTilt;

            // 上报方向盘高度（上下，Z）位置
            @optional SteerWhl_Height BCM_SteerWheel_notifyHeight;

            // 上报方向盘加热状态
            @optional SteerWhl_HeatrStatus BCM_SteerWheel_Heatr_notifySteerWheelHeatSts;

            // 上报方向盘故障状态
            @optional SteerWhl_FaultStatus BCM_SteerWheel_FaultStatus;

            // 上报鸣笛的当前状态
            @optional runningStatus BCM_Horn_notifyStatus;

            // 上报电池热管理请求
            @optional ThermalReq BMS_ThermalReq_ntfReq;

            // 左侧滚轮上下调节状态
            @optional UpDwnAdjSwtMode HMI_ExtrMirrSwt_LeRollrUpDwnAdjSt;

            // 左侧滚轮左右调节状态
            @optional LeRiAdjSwtMode HMI_ExtrMirrSwt_LeRollrLeRiAdjSt;

            // 左侧后视镜上下调节位置
            @optional Percent BCM_RearView_L_LmirrorUDSetFB;

            // 左侧后视镜左右调节位置
            @optional Percent BCM_RearView_L_LmirrorLRSetFB;

            // 左后视镜折叠状态
            @optional FoldUnfoldStatus BCM_RearView_L_FoldUnfoldStatus;

            // 左后视镜加热状态
            @optional RearDefrostStatus BCM_RearView_L_RearDefrostStatus;

            // 上报左前车窗学习状态
            @optional WinnotifyLearnedStatus BCM_Window_FL_LearnedStatus;

            // 上报左前车窗防夹状态
            @optional WinnotifyAntiPinch BCM_Window_FL_notifyAntiPinch;

            // 上报左前车窗执行状态
            @optional WinnotifyActuateStatus BCM_Window_FL_notifyActuateStatus;

            // 上报左前车窗开度
            @optional WinnotifyPosition BCM_Window_FL_notifyPosition;

            // 上报左后车窗的锁状态
            @optional notifyLockStatus BCM_Window_RL_notifyLockStatus;

            // 上报左后车窗学习状态
            @optional WinnotifyLearnedStatus BCM_Window_RL_LearnedStatus;

            // 上报左后车窗防夹状态
            @optional WinnotifyAntiPinch BCM_Window_RL_notifyAntiPinch;

            // 上报左后车窗执行状态
            @optional WinnotifyActuateStatus BCM_Window_RL_notifyActuateStatus;

            // 上报左后车窗开度
            @optional WinnotifyPosition BCM_Window_RL_notifyPosition;

            // 上报主驾左前车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_Drvr_FL_ntfSt;

            // 上报主驾左前车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_Drvr_FL_ntfFltSt;

            // 上报主驾右前车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_Drvr_FR_ntfSt;

            // 上报主驾右前车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_Drvr_FR_ntfFltSt;

            // 上报主驾左后车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_Drvr_RL_ntfSt;

            // 上报主驾左后车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_Drvr_RL_ntfFltSt;

            // 上报主驾右后车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_Drvr_RR_ntfSt;

            // 上报主驾右后车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_Drvr_RR_ntfFltSt;

            // 上报左后车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_RL_ntfSt;

            // 上报左后车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_RL_ntfFltSt;

            // 上报下雨车窗控制请求
            @optional notifyRLSWindowCtrlReq BCM_RLS_Info_notifyRLSWindowCtrlReq;

            // 上报左前电动门内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorInsd_FL_ntfSt;

            // 上报左前电动门内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorInsd_FL_ntfFltSt;

            // 上报左后电动门内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorInsd_RL_ntfSt;

            // 上报左后电动门内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorInsd_RL_ntfFltSt;

            // 上报左前门把手外开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutsd_Outer_FL_ntfSt;

            // 上报左前门把手外开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutsd_Outer_FL_ntfFltSt;

            // 上报左后门把手外开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutsd_Outer_RL_ntfSt;

            // 上报左后门把手外开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutsd_Outer_RL_ntfFltSt;

            // 上报左前门把手内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutd_FL_ntfSt;

            // 上报左前门把手内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutd_FL_ntfFltSt;

            // 上报左后门把手内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutd_RL_ntfSt;

            // 上报左后门把手内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutd_RL_ntfFltSt;

            // 上报前天窗遮阳帘故障状态
            @optional notifySunshadeFltSt BCM_Sunshade_F_notifySunshadeFltSt;

            // 上报前遮阳帘位置计数
            @optional notifyPositionCount BCM_Sunshade_F_notifyPositionCount;

            // 上报前天窗遮阳帘防夹状态
            @optional notifyAntiPinch BCM_Sunshade_F_notifyAntiPinch;

            // 上报前天窗遮阳帘动态位置
            @optional notifyPosition BCM_Sunshade_F_notifyPosition;

            // 上报前天窗遮阳帘状态
            @optional notifySunshadeActuateStatus BCM_Sunshade_F_notifySunshadeActuateStatus;

            // 上报前天窗遮阳帘的学习状态
            @optional notifyLearnedStatus BCM_Sunshade_F_notifyLearnedStatus;

            // 上报前遮阳帘初始化状态
            @optional notifyNormalizedStatus BCM_Sunshade_F_notifyNormalizedStatus;

        };
    };
};
