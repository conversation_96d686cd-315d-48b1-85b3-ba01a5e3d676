/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCM_FL_Control.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCM_FL_Control.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCM_FL {
        enum PwrSplyIdCmd { // 供电指令
            @value(0x0) PwrSplyIdCmd_OFF,
            @value(0x1) PwrSplyIdCmd_ON,
            @value(0x2) PwrSplyIdCmd_No_action
        };

        typedef uint8 PwrSplyID; // 供电口编号

        @nested
        struct PwrSplyOutput { // 电源供电输出
            PwrSplyID pwrsplyid; // 供电口编号
            PwrSplyIdCmd pwrsplyidcmd; // 供电指令
        };

        typedef sequence<PwrSplyOutput, 100> PwrSplyOutputCmd; // 电源供电输出命令数组，即使只调用其中一个口也需要整个数据同时调用，其它接口用no action填充

        enum CS_ImobLearnSts { // 防盗学习状态
            @value(0x0) CS_ImobLearnSts_INITIAL,
            @value(0x1) CS_ImobLearnSts_NOT_LEARNED,
            @value(0x2) CS_ImobLearnSts_LEARNED
        };

        enum CS_ImobAuthResult { // 防盗认证结果
            @value(0x0) CS_ImobAuthRes_INITIAL,
            @value(0x1) CS_ImobAuthRes_LOCK,
            @value(0x2) CS_ImobAuthRes_RELEASE
        };

        enum CS_ImobAuthSts { // 防盗认证状态
            @value(0x0) CS_ImobAuthSts_INITIAL,
            @value(0x1) CS_ImobAuthSts_NORMAL,
            @value(0x2) CS_ImobAuthSts_FAILED
        };

        @nested
        struct ImobAuthStsInfo { // 动力防盗状态
            CS_ImobAuthSts imobauthsts; // 防盗认证状态
            CS_ImobAuthResult imobauthresult; // 防盗认证结果
            CS_ImobLearnSts imoblearnsts; // 防盗学习状态
        };

        enum VehAntithftStatus { // 车身防盗状态
            @value(0x0) CS_AntithftSts_UNSET,
            @value(0x1) CS_AntithftSts_SET,
            @value(0x2) CS_AntithftSts_PART_SET,
            @value(0x3) CS_AntithftSts_ALARM,
            @value(0x4) CS_AntithftSts_PRE_SET,
            @value(0xFE) INITIAL,
            @value(0xFF) CS_AntithftSts_UNKNOWN
        };

        typedef uint8 ExtrLightEffectDisplaySetting; // 外灯效果显示设置

        typedef uint8 ExtrLightEffectNum; // 位置灯效果选择编号

        typedef uint8 ExtrLightEffectCtrlCmd; // 外灯效果控制命令

        @nested
        struct LampEffectCtrl { // 迎宾效果控制
            ExtrLightEffectCtrlCmd extrlighteffectctrlcmd; // 外灯效果控制命令
            ExtrLightEffectNum extrlighteffectnum; // 外灯效果
            ExtrLightEffectDisplaySetting extrlighteffectdisplaysetting; // 外灯效果显示设置
        };

        typedef uint8 EnableStatusCmd; // 使能设置

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        typedef uint8 LampId; // 灯ID

        @nested
        struct PosnLampControl { // 位置灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
        };

        typedef sequence<PosnLampControl, 4> PosnLampCmd; // 位置灯功能控制

        typedef uint8 TurnLampOccupySts; // 转向灯占位状态上报

        typedef uint8 LampFlow; // 流水状态

        @nested
        struct TurnLampControl { // 转向灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
            LampFlow lampflow; // 流水状态
        };

        typedef sequence<TurnLampControl, 2> TurnLampCmd; // 转向灯状态控制

        typedef uint8 BCM_Lockctrl; // 解闭锁控制

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 UserClass; // 用户的账户类型

        typedef uint8 UserId; // 用户的账户标识

        @nested
        struct UserInfo { // 用户信息
            UserId userid; // 用户的账户标识
            UserClass userclass; // 用户的账户类型
        };

        @nested
        struct IVI_UserInfoNotify_NTF_UserInfo_In {
            UserInfo userinfo;
        };

        @nested
        struct BCM_VIUCenLockStatus_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct BCM_LoBeamCmd_FL_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_TurnLampCmd_FL_In {
            TurnLampCmd turnlampcmd;
        };

        @nested
        struct BCM_TurnLampOccupySts_FL_In {
            TurnLampOccupySts turnlampoccupysts;
        };

        @nested
        struct BCM_PosnLampCmd_FL_In {
            PosnLampCmd posnlampcmd;
        };

        @nested
        struct BCM_HiBeamCmd_FL_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_AdpvHiBeamFctOnCmd_FL_In {
            EnableStatusCmd enablestatuscmd;
        };

        @nested
        struct BCM_AFSEnaCmd_FL_In {
            EnableStatusCmd enablestatuscmd;
        };

        @nested
        struct BCM_PosnLampEffectCtrl_FL_In {
            LampEffectCtrl lampeffectctrl;
        };

        @nested
        struct BCM_LowLampEffectCtrl_FL_In {
            LampEffectCtrl lampeffectctrl;
        };

        @nested
        struct BCM_VehAntithft_ZCUFL_In {
            VehAntithftStatus vehantithftstatus;
        };

        @nested
        struct EMS_PwrImobAuth_ZCUFL_In {
            ImobAuthStsInfo imobauthstsinfo;
        };

        @nested
        struct BCM_BLE_ImobAuth_ZCUFL_In {
            ImobAuthStsInfo bleauthstsinfo;
        };

        @nested
        struct BCM_Door_RL_ChildLockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct BCM_Door_CenLockStatus_In {
            BCM_Lockctrl bcm_cenlockstatus;
        };

        @nested
        struct BCM_Door_FL_LockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct BCM_Door_RL_LockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct VMM_PowerMode_setPwrSplyOutput_ZCUFL_In {
            PwrSplyOutputCmd pwrsplyoutputcmdarg;
        };

        @final
        union HPCM_FL_Control switch(int32) {
            // 通知其它部件当前的用户信息
            case 459389727:
                IVI_UserInfoNotify_NTF_UserInfo_In IVI_UserInfoNotify_NTF_UserInfo;

            // 中控锁状态回传
            case -2081777529:
                BCM_VIUCenLockStatus_In BCM_VIUCenLockStatus;

            // 左近光灯控制
            case -229850654:
                BCM_LoBeamCmd_FL_In BCM_LoBeamCmd_FL;

            // 左转向灯控制
            case 1282394964:
                BCM_TurnLampCmd_FL_In BCM_TurnLampCmd_FL;

            // 左域转向灯占位设置
            case 2058802618:
                BCM_TurnLampOccupySts_FL_In BCM_TurnLampOccupySts_FL;

            // 左位置灯控制
            case -834334753:
                BCM_PosnLampCmd_FL_In BCM_PosnLampCmd_FL;

            // 左远光灯控制
            case 852691580:
                BCM_HiBeamCmd_FL_In BCM_HiBeamCmd_FL;

            // 左自适应远光灯功能设置
            case 1017629470:
                BCM_AdpvHiBeamFctOnCmd_FL_In BCM_AdpvHiBeamFctOnCmd_FL;

            // 左自适应近光灯功能设置
            case 852726501:
                BCM_AFSEnaCmd_FL_In BCM_AFSEnaCmd_FL;

            // 左前位置灯迎宾控制
            case 994942035:
                BCM_PosnLampEffectCtrl_FL_In BCM_PosnLampEffectCtrl_FL;

            // 左近光灯迎宾控制
            case 230140927:
                BCM_LowLampEffectCtrl_FL_In BCM_LowLampEffectCtrl_FL;

            // 车身防盗状态
            case 666106546:
                BCM_VehAntithft_ZCUFL_In BCM_VehAntithft_ZCUFL;

            // 动力防盗状态
            case 293468238:
                EMS_PwrImobAuth_ZCUFL_In EMS_PwrImobAuth_ZCUFL;

            // 蓝牙防盗状态
            case -1852359964:
                BCM_BLE_ImobAuth_ZCUFL_In BCM_BLE_ImobAuth_ZCUFL;

            // 左后儿童锁解闭锁控制
            case -1054381629:
                BCM_Door_RL_ChildLockUnlock_Ctrl_In BCM_Door_RL_ChildLockUnlock_Ctrl;

            // 中控锁状态
            case -618264161:
                BCM_Door_CenLockStatus_In BCM_Door_CenLockStatus;

            // 左前门锁解闭锁控制
            case -1423324409:
                BCM_Door_FL_LockUnlock_Ctrl_In BCM_Door_FL_LockUnlock_Ctrl;

            // 左后门锁解闭锁控制
            case 435918842:
                BCM_Door_RL_LockUnlock_Ctrl_In BCM_Door_RL_LockUnlock_Ctrl;

            // 设置ZCUFL电源供电命令
            case -255848359:
                VMM_PowerMode_setPwrSplyOutput_ZCUFL_In VMM_PowerMode_setPwrSplyOutput_ZCUFL;
        };
    };
};
