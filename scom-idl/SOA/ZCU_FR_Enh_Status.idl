/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_FR_Enh_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_FR_Enh_Status.idl
*/

module Seres {
    module ZCU_FR_Enh {

        enum RepairModeStatus { // 维修模式状态反馈
            @value(0x0) RepairModeStatus_OFF,
            @value(0x1) RepairModeStatus_ON,
            @value(0xFF) RepairModeStatus_INVALID
        };

        typedef uint8 BCM_lockStatus; // 锁的状态

        typedef uint8 BCM_OpenStatus; // 车门开启状态

        typedef uint8 BCM_ChildlockStatus; // 儿童锁状态

        @final
        struct ZCU_FR_Enh_Status{
            // 上报右后儿童锁解闭锁状态
            @optional BCM_ChildlockStatus BCM_Door_RR_notifyChildLockUnlockStatus;

            // 上报右前门开关状态
            @optional BCM_OpenStatus BCM_Door_FR_notifyOpenStatus;

            // 上报右后门开关状态
            @optional BCM_OpenStatus BCM_Door_RR_notifyOpenStatus;

            // 上报右前门解闭锁状态
            @optional BCM_lockStatus BCM_Door_FR_notifyLockUnlockStatus;

            // 上报右后门解闭锁状态
            @optional BCM_lockStatus BCM_Door_RR_notifyLockUnlockStatus;

            // 维修模式状态上报
            @optional RepairModeStatus RepairMode_repairModeStatus;

        };
    };
};
