/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCM_R_Control.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCM_R_Control.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCM_R {
        enum PwrSplyIdCmd { // 供电指令
            @value(0x0) PwrSplyIdCmd_OFF,
            @value(0x1) PwrSplyIdCmd_ON,
            @value(0x2) PwrSplyIdCmd_No_action
        };

        typedef uint8 PwrSplyID; // 供电口编号

        @nested
        struct PwrSplyOutput { // 电源供电输出
            PwrSplyID pwrsplyid; // 供电口编号
            PwrSplyIdCmd pwrsplyidcmd; // 供电指令
        };

        typedef sequence<PwrSplyOutput, 100> PwrSplyOutputCmd; // 电源供电输出命令数组，即使只调用其中一个口也需要整个数据同时调用，其它接口用no action填充

        enum CS_ImobLearnSts { // 防盗学习状态
            @value(0x0) CS_ImobLearnSts_INITIAL,
            @value(0x1) CS_ImobLearnSts_NOT_LEARNED,
            @value(0x2) CS_ImobLearnSts_LEARNED
        };

        enum CS_ImobAuthResult { // 防盗认证结果
            @value(0x0) CS_ImobAuthRes_INITIAL,
            @value(0x1) CS_ImobAuthRes_LOCK,
            @value(0x2) CS_ImobAuthRes_RELEASE
        };

        enum CS_ImobAuthSts { // 防盗认证状态
            @value(0x0) CS_ImobAuthSts_INITIAL,
            @value(0x1) CS_ImobAuthSts_NORMAL,
            @value(0x2) CS_ImobAuthSts_FAILED
        };

        @nested
        struct ImobAuthStsInfo { // 动力防盗状态
            CS_ImobAuthSts imobauthsts; // 防盗认证状态
            CS_ImobAuthResult imobauthresult; // 防盗认证结果
            CS_ImobLearnSts imoblearnsts; // 防盗学习状态
        };

        enum VehAntithftStatus { // 车身防盗状态
            @value(0x0) CS_AntithftSts_UNSET,
            @value(0x1) CS_AntithftSts_SET,
            @value(0x2) CS_AntithftSts_PART_SET,
            @value(0x3) CS_AntithftSts_ALARM,
            @value(0x4) CS_AntithftSts_PRE_SET,
            @value(0xFE) INITIAL,
            @value(0xFF) CS_AntithftSts_UNKNOWN
        };

        typedef uint8 StatusSet; // 位置灯状态设置

        typedef uint8 ISDEffectModSet; // ISD效果模式设置

        typedef uint8 ISDEffectSel; // ISD显示效果选择

        typedef uint8 ISDSceneSel; // ISD显示场景选择

        typedef uint8 ISDEffectCtrlCmd; // ISD效果控制命令

        typedef uint8 ExtrLightEffectNum; // 位置灯效果选择编号

        @nested
        struct ISDEffectCtrl { // ISD灯效控制
            ExtrLightEffectNum extrlighteffectnum; // 位置灯效果选择编号
            ISDEffectCtrlCmd isdeffectctrlcmd; // ISD效果控制命令
            ISDSceneSel isdscenesel; // ISD显示场景选择
            ISDEffectSel isdeffectsel; // ISD显示效果选择
            ISDEffectModSet isdeffectmodset; // ISD效果模式设置
        };

        typedef uint8 ExtrLightEffectDisplaySetting; // 外灯效果显示设置

        typedef uint8 ExtrLightEffectCtrlCmd; // 外灯效果控制命令

        @nested
        struct LampEffectCtrl { // 迎宾效果控制
            ExtrLightEffectCtrlCmd extrlighteffectctrlcmd; // 外灯效果控制命令
            ExtrLightEffectNum extrlighteffectnum; // 外灯效果
            ExtrLightEffectDisplaySetting extrlighteffectdisplaysetting; // 外灯效果显示设置
        };

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        typedef uint8 LampId; // 灯ID

        @nested
        struct PosnLampControl { // 位置灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
        };

        typedef sequence<PosnLampControl, 4> PosnLampCmd; // 位置灯功能控制

        typedef uint8 TurnLampOccupySts; // 转向灯占位状态上报

        typedef uint8 LampFlow; // 流水状态

        @nested
        struct TurnLampControl { // 转向灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
            LampFlow lampflow; // 流水状态
        };

        typedef sequence<TurnLampControl, 2> TurnLampCmd; // 转向灯状态控制

        @nested
        struct BrkLampControl { // 制动灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
        };

        typedef sequence<BrkLampControl, 6> BrkLampCmd; // 制动灯状态控制

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 UserClass; // 用户的账户类型

        typedef uint8 UserId; // 用户的账户标识

        @nested
        struct UserInfo { // 用户信息
            UserId userid; // 用户的账户标识
            UserClass userclass; // 用户的账户类型
        };

        @nested
        struct IVI_UserInfoNotify_NTF_UserInfo_In {
            UserInfo userinfo;
        };

        @nested
        struct BCM_BrkLampCmd_In {
            BrkLampCmd brklampcmd;
        };

        @nested
        struct BCM_ReFogLampCmd_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_RvsLampCmd_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_TurnLampCmd_R_In {
            TurnLampCmd turnlampcmd;
        };

        @nested
        struct BCM_TurnLampOccupySts_R_In {
            TurnLampOccupySts turnlampoccupysts;
        };

        @nested
        struct BCM_PosnLampCmd_R_In {
            PosnLampCmd posnlampcmd;
        };

        @nested
        struct BCM_PosnLampEffectCtrl_R_In {
            LampEffectCtrl lampeffectctrl;
        };

        @nested
        struct BCM_ISDEffectCtrl_R_In {
            ISDEffectCtrl isdeffectctrl;
        };

        @nested
        struct BCM_PosnLamStatusSet_R_In {
            StatusSet statusset;
        };

        @nested
        struct BCM_LicensePlateLightSet_R_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_VehAntithft_ZCUR_In {
            VehAntithftStatus vehantithftstatus;
        };

        @nested
        struct EMS_PwrImobAuth_ZCUR_In {
            ImobAuthStsInfo imobauthstsinfo;
        };

        @nested
        struct BCM_BLE_ImobAuth_ZCUR_In {
            ImobAuthStsInfo bleauthstsinfo;
        };

        @nested
        struct VMM_PowerMode_setPwrSplyOutput_ZCUR_In {
            PwrSplyOutputCmd pwrsplyoutputcmdarg;
        };

        @final
        union HPCM_R_Control switch(int32) {
            // 通知其它部件当前的用户信息
            case 459389727:
                IVI_UserInfoNotify_NTF_UserInfo_In IVI_UserInfoNotify_NTF_UserInfo;

            // 制动灯控制
            case 508650239:
                BCM_BrkLampCmd_In BCM_BrkLampCmd;

            // 后雾灯控制
            case **********:
                BCM_ReFogLampCmd_In BCM_ReFogLampCmd;

            // 倒车灯控制
            case 654692144:
                BCM_RvsLampCmd_In BCM_RvsLampCmd;

            // 后转向灯控制
            case -1923680533:
                BCM_TurnLampCmd_R_In BCM_TurnLampCmd_R;

            // 后域转向灯占位设置
            case -146752651:
                BCM_TurnLampOccupySts_R_In BCM_TurnLampOccupySts_R;

            // 后位置灯控制
            case -895653109:
                BCM_PosnLampCmd_R_In BCM_PosnLampCmd_R;

            // 后位置灯迎宾控制
            case **********:
                BCM_PosnLampEffectCtrl_R_In BCM_PosnLampEffectCtrl_R;

            // 后ISD灯效控制
            case **********:
                BCM_ISDEffectCtrl_R_In BCM_ISDEffectCtrl_R;

            // 后位置灯状态设置
            case -**********:
                BCM_PosnLamStatusSet_R_In BCM_PosnLamStatusSet_R;

            // 后牌照灯设置
            case **********:
                BCM_LicensePlateLightSet_R_In BCM_LicensePlateLightSet_R;

            // 车身防盗状态
            case -**********:
                BCM_VehAntithft_ZCUR_In BCM_VehAntithft_ZCUR;

            // 动力防盗状态
            case 265973009:
                EMS_PwrImobAuth_ZCUR_In EMS_PwrImobAuth_ZCUR;

            // 蓝牙防盗状态
            case **********:
                BCM_BLE_ImobAuth_ZCUR_In BCM_BLE_ImobAuth_ZCUR;

            // 设置ZCUR电源供电命令
            case -**********:
                VMM_PowerMode_setPwrSplyOutput_ZCUR_In VMM_PowerMode_setPwrSplyOutput_ZCUR;
        };
    };
};
