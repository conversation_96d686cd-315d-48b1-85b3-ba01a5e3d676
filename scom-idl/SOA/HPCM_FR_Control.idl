/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCM_FR_Control.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCM_FR_Control.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCM_FR {
        enum PwrSplyIdCmd { // 供电指令
            @value(0x0) PwrSplyIdCmd_OFF,
            @value(0x1) PwrSplyIdCmd_ON,
            @value(0x2) PwrSplyIdCmd_No_action
        };

        typedef uint8 PwrSplyID; // 供电口编号

        @nested
        struct PwrSplyOutput { // 电源供电输出
            PwrSplyID pwrsplyid; // 供电口编号
            PwrSplyIdCmd pwrsplyidcmd; // 供电指令
        };

        typedef sequence<PwrSplyOutput, 100> PwrSplyOutputCmd; // 电源供电输出命令数组，即使只调用其中一个口也需要整个数据同时调用，其它接口用no action填充

        typedef uint8 BCM_Lockctrl; // 解闭锁控制

        enum CS_ImobLearnSts { // 防盗学习状态
            @value(0x0) CS_ImobLearnSts_INITIAL,
            @value(0x1) CS_ImobLearnSts_NOT_LEARNED,
            @value(0x2) CS_ImobLearnSts_LEARNED
        };

        enum CS_ImobAuthResult { // 防盗认证结果
            @value(0x0) CS_ImobAuthRes_INITIAL,
            @value(0x1) CS_ImobAuthRes_LOCK,
            @value(0x2) CS_ImobAuthRes_RELEASE
        };

        enum CS_ImobAuthSts { // 防盗认证状态
            @value(0x0) CS_ImobAuthSts_INITIAL,
            @value(0x1) CS_ImobAuthSts_NORMAL,
            @value(0x2) CS_ImobAuthSts_FAILED
        };

        @nested
        struct ImobAuthStsInfo { // 动力防盗状态
            CS_ImobAuthSts imobauthsts; // 防盗认证状态
            CS_ImobAuthResult imobauthresult; // 防盗认证结果
            CS_ImobLearnSts imoblearnsts; // 防盗学习状态
        };

        enum VehAntithftStatus { // 车身防盗状态
            @value(0x0) CS_AntithftSts_UNSET,
            @value(0x1) CS_AntithftSts_SET,
            @value(0x2) CS_AntithftSts_PART_SET,
            @value(0x3) CS_AntithftSts_ALARM,
            @value(0x4) CS_AntithftSts_PRE_SET,
            @value(0xFE) INITIAL,
            @value(0xFF) CS_AntithftSts_UNKNOWN
        };

        typedef uint8 ExtrLightEffectDisplaySetting; // 外灯效果显示设置

        typedef uint8 ExtrLightEffectNum; // 位置灯效果选择编号

        typedef uint8 ExtrLightEffectCtrlCmd; // 外灯效果控制命令

        @nested
        struct LampEffectCtrl { // 迎宾效果控制
            ExtrLightEffectCtrlCmd extrlighteffectctrlcmd; // 外灯效果控制命令
            ExtrLightEffectNum extrlighteffectnum; // 外灯效果
            ExtrLightEffectDisplaySetting extrlighteffectdisplaysetting; // 外灯效果显示设置
        };

        typedef uint8 TurnLampBuzReq; // 请求转向灯蜂鸣器

        typedef uint8 IndicatorLight; // 指示灯设置

        @nested
        struct TurnLampindcrReq { // 转向灯指示灯设置
            IndicatorLight turnlampindcrreq_l; // 左转向灯设置
            IndicatorLight turnlampindcrreq_r; // 左转向灯设置
        };

        typedef uint8 EnableStatusCmd; // 使能设置

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        typedef uint8 LampId; // 灯ID

        @nested
        struct PosnLampControl { // 位置灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
        };

        typedef sequence<PosnLampControl, 4> PosnLampCmd; // 位置灯功能控制

        typedef uint8 TurnLampOccupySts; // 转向灯占位状态上报

        typedef uint8 LampFlow; // 流水状态

        @nested
        struct TurnLampControl { // 转向灯控制
            LampId lampid; // 灯ID
            LampCmd lampcmd; // 灯光控制
            DutyRat dutyrat; // 亮度控制占空比
            LampFlow lampflow; // 流水状态
        };

        typedef sequence<TurnLampControl, 2> TurnLampCmd; // 转向灯状态控制

        typedef uint16 ServiceName; // 业务名称

        typedef uint8 ReturnCode; // 调用返回值

        @nested
        struct SysMode_ReturnCode { // 系统模式业务并发结果
            ServiceName requestservicename; // 请求业务的名称
            ReturnCode returncode; // 业务并发结果
            ServiceName conflictservicename; // 互斥业务的名称
        };

        typedef uint8 UserClass; // 用户的账户类型

        typedef uint8 UserId; // 用户的账户标识

        @nested
        struct UserInfo { // 用户信息
            UserId userid; // 用户的账户标识
            UserClass userclass; // 用户的账户类型
        };

        @nested
        struct IVI_UserInfoNotify_NTF_UserInfo_In {
            UserInfo userinfo;
        };

        @nested
        struct VEH_SysMode_sysModServReq_In {
            SysMode_ReturnCode sysmode_returncode;
        };

        @nested
        struct BCM_LoBeamCmd_FR_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_TurnLampCmd_FR_In {
            TurnLampCmd turnlampcmd;
        };

        @nested
        struct BCM_TurnLampOccupySts_FR_In {
            TurnLampOccupySts turnlampoccupysts;
        };

        @nested
        struct BCM_PosnLampCmd_FR_In {
            PosnLampCmd posnlampcmd;
        };

        @nested
        struct BCM_HiBeamCmd_FR_In {
            LampCmd lampcmd;
            DutyRat dutyrat;
        };

        @nested
        struct BCM_AdpvHiBeamFctOnCmd_FR_In {
            EnableStatusCmd enablestatuscmd;
        };

        @nested
        struct BCM_AFSEnaCmd_FR_In {
            EnableStatusCmd enablestatuscmd;
        };

        @nested
        struct BCM_PosnLampindcrReq_In {
            IndicatorLight indicatorlight;
        };

        @nested
        struct BCM_LoBeamindcrReq_In {
            IndicatorLight indicatorlight;
        };

        @nested
        struct BCM_HiBeamindcrReq_In {
            IndicatorLight indicatorlight;
        };

        @nested
        struct BCM_TurnLampindcrReq_In {
            TurnLampindcrReq turnlampindcrreq;
        };

        @nested
        struct BCM_AFSFltIndcrReq_In {
            IndicatorLight indicatorlight;
        };

        @nested
        struct BCM_TurnLampBuzReq_In {
            TurnLampBuzReq turnlampbuzreq;
        };

        @nested
        struct BCM_ReFogLampindcrReq_In {
            IndicatorLight indicatorlight;
        };

        @nested
        struct BCM_PosnLampEffectCtrl_FR_In {
            LampEffectCtrl lampeffectctrl;
        };

        @nested
        struct BCM_LowLampEffectCtrl_FR_In {
            LampEffectCtrl lampeffectctrl;
        };

        @nested
        struct BCM_VehAntithft_ZCUFR_In {
            VehAntithftStatus vehantithftstatus;
        };

        @nested
        struct EMS_PwrImobAuth_ZCUFR_In {
            ImobAuthStsInfo imobauthstsinfo;
        };

        @nested
        struct BCM_BLE_ImobAuth_ZCUFR_In {
            ImobAuthStsInfo bleauthstsinfo;
        };

        @nested
        struct BCM_Door_RR_ChildLockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct BCM_Door_FR_LockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct BCM_Door_RR_LockUnlock_Ctrl_In {
            BCM_Lockctrl bcm_lockctrl;
        };

        @nested
        struct VMM_PowerMode_setPwrSplyOutput_ZCUFR_In {
            PwrSplyOutputCmd pwrsplyoutputcmdarg;
        };

        @final
        union HPCM_FR_Control switch(int32) {
            // 通知其它部件当前的用户信息
            case 459389727:
                IVI_UserInfoNotify_NTF_UserInfo_In IVI_UserInfoNotify_NTF_UserInfo;

            // 通知其它部件模式仲裁结果
            case 410273072:
                VEH_SysMode_sysModServReq_In VEH_SysMode_sysModServReq;

            // 右近光灯控制
            case 1642680714:
                BCM_LoBeamCmd_FR_In BCM_LoBeamCmd_FR;

            // 右转向灯控制
            case -1628323327:
                BCM_TurnLampCmd_FR_In BCM_TurnLampCmd_FR;

            // 右域转向灯占位设置
            case -1244362800:
                BCM_TurnLampOccupySts_FR_In BCM_TurnLampOccupySts_FR;

            // 右位置灯控制
            case 631002219:
                BCM_PosnLampCmd_FR_In BCM_PosnLampCmd_FR;

            // 右远光灯控制
            case -724934415:
                BCM_HiBeamCmd_FR_In BCM_HiBeamCmd_FR;

            // 右自适应远光灯功能设置
            case 758080221:
                BCM_AdpvHiBeamFctOnCmd_FR_In BCM_AdpvHiBeamFctOnCmd_FR;

            // 右自适应近光灯功能设置
            case -244591055:
                BCM_AFSEnaCmd_FR_In BCM_AFSEnaCmd_FR;

            // 位置灯指示灯请求
            case -1802943129:
                BCM_PosnLampindcrReq_In BCM_PosnLampindcrReq;

            // 近光灯指示灯请求
            case -1833904118:
                BCM_LoBeamindcrReq_In BCM_LoBeamindcrReq;

            // 远光灯指示灯请求
            case -192765343:
                BCM_HiBeamindcrReq_In BCM_HiBeamindcrReq;

            // 转向灯指示灯请求
            case -563286091:
                BCM_TurnLampindcrReq_In BCM_TurnLampindcrReq;

            // AFS故障指示灯请求
            case 471915076:
                BCM_AFSFltIndcrReq_In BCM_AFSFltIndcrReq;

            // 转向灯蜂鸣器请求
            case -377530235:
                BCM_TurnLampBuzReq_In BCM_TurnLampBuzReq;

            // 后雾灯指示灯请求
            case 238343956:
                BCM_ReFogLampindcrReq_In BCM_ReFogLampindcrReq;

            // 右前位置灯迎宾控制
            case 6012830:
                BCM_PosnLampEffectCtrl_FR_In BCM_PosnLampEffectCtrl_FR;

            // 右近光灯迎宾控制
            case -1243089151:
                BCM_LowLampEffectCtrl_FR_In BCM_LowLampEffectCtrl_FR;

            // 车身防盗状态
            case -1666656485:
                BCM_VehAntithft_ZCUFR_In BCM_VehAntithft_ZCUFR;

            // 动力防盗状态
            case -2085380925:
                EMS_PwrImobAuth_ZCUFR_In EMS_PwrImobAuth_ZCUFR;

            // 蓝牙防盗状态
            case 2016700041:
                BCM_BLE_ImobAuth_ZCUFR_In BCM_BLE_ImobAuth_ZCUFR;

            // 右后儿童锁解闭锁控制
            case -566057108:
                BCM_Door_RR_ChildLockUnlock_Ctrl_In BCM_Door_RR_ChildLockUnlock_Ctrl;

            // 右前门锁解闭锁控制
            case -1057982713:
                BCM_Door_FR_LockUnlock_Ctrl_In BCM_Door_FR_LockUnlock_Ctrl;

            // 右后门锁解闭锁控制
            case -1920068766:
                BCM_Door_RR_LockUnlock_Ctrl_In BCM_Door_RR_LockUnlock_Ctrl;

            // 设置ZCUFR电源供电命令
            case 1320293226:
                VMM_PowerMode_setPwrSplyOutput_ZCUFR_In VMM_PowerMode_setPwrSplyOutput_ZCUFR;
        };
    };
};
