/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_FL_HPCM_Control_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_FL_HPCM_Control_Status.idl
*/

module Seres {
    module ZCU_FL_HPCM_Control {

        typedef uint8 BCM_Lockctrl; // 解闭锁控制

        typedef uint8 BCM_lockStatus; // 锁的状态

        typedef uint8 BCM_OpenStatus; // 车门开启状态

        typedef uint8 BCM_ChildlockStatus; // 儿童锁状态

        typedef uint8 ALSFltReporting; // 近光灯高度调节故障状态

        typedef uint8 BCM_Legacy_E2E_Result; // E2E校验结果

        typedef uint8 BCM_Legacy_HMI_FaultStatus; // HMI故障状态

        @nested
        struct LightCombSwtFltSts { // 车灯组合开关故障状态
            BCM_Legacy_HMI_FaultStatus steertlightswtfltsts; // 转向灯开关故障状态
            BCM_Legacy_HMI_FaultStatus highbeamswtfltsts; // 远光灯开关故障状态
        };

        typedef uint8 HMI_HighBeamMode; // 远光灯开关状态

        typedef uint8 HMI_SteerLightMode; // 转向灯开关状态

        @nested
        struct HMI_LiCombSwt { // 灯光开关状态
            HMI_SteerLightMode hmi_steerlightmode; // 转向灯开关状态
            HMI_HighBeamMode hmi_highbeammode; // 远光灯开关状态
            LightCombSwtFltSts lightcombswtfltsts; // 车灯组合开关故障状态
            BCM_Legacy_E2E_Result bcm_legacy_e2e_result; // E2E校验状态
        };

        typedef uint8 SwtFltSt; // 开关故障状态

        typedef uint8 IoAbsSwtSt; // 开关按压状态

        @nested
        struct KeySwt_ntfSt { // 危险报警灯开关状态
            IoAbsSwtSt ioabsswtst; // 开关按压状态
            SwtFltSt swtfltst; // 开关故障状态
        };

        typedef uint8 AFSFltReporting; // AFS故障状态

        typedef uint8 AFSOnStReporting; // AFS点亮状态

        typedef uint8 AFSFctStReporting; // AFS功能设置状态

        typedef uint8 AdpvHiBeamFltStReporting; // 自适应远光灯功能故障状态

        typedef uint8 AdpvHiBeamFctStReporting; // 自适应远光灯功能开启状态

        typedef uint8 LampFlow; // 流水状态

        typedef uint8 NtfFltSt; // 灯故障状态

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        @nested
        struct NtfList { // 车灯状态
            LampCmd bemalightstatus; // 车灯开关状态
            DutyRat brightnessstatus; // 车灯亮度
        };

        @nested
        struct TurnLampStatusReporting { // 转向灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
            LampFlow lampflow; // 流水状态
        };

        @nested
        struct GeneralLampStatusReporting { // 通用灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
        };

        @final
        struct ZCU_FL_HPCM_Control_Status{
            // 上报左前位置灯状态
            @optional GeneralLampStatusReporting BCM_PosnLamp_FL_notify;

            // 上报左前转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_FL_notify;

            // 上报左侧转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RS_notify;

            // 上报左近光灯状态
            @optional GeneralLampStatusReporting BCM_LoBeam_FL_notify;

            // 上报左远光灯状态
            @optional GeneralLampStatusReporting BCM_HiBeam_FL_notify;

            // 上报左自适应远光灯功能开启状态
            @optional AdpvHiBeamFctStReporting BCM_AdpvHiBeamFctSt_FL_notify;

            // 上报左自适应远光灯功能故障状态
            @optional AdpvHiBeamFltStReporting BCM_AdpvHiBeamFltSt_FL_notify;

            // 上报自适应近光功能设置状态
            @optional AFSFctStReporting BCM_AFSFctSt_FL_notify;

            // 上报自适应近光打开状态
            @optional AFSOnStReporting BCM_AFSOnSt_FL_notify;

            // 上报自适应近光故障状态
            @optional AFSFltReporting BCM_AFSFlt_FL_notify;

            // 上报危险报警开关状态
            @optional KeySwt_ntfSt BCM_Snsr_KeySwt_HzrdWarn_notify;

            // 上报灯光组合开关状态
            @optional HMI_LiCombSwt BCM_HMI_LiCombSwt;

            // 上报近光灯高度调节故障状态
            @optional ALSFltReporting BCM_ALSFlt_FL_notify;

            // 上报左后儿童锁解闭锁状态
            @optional BCM_ChildlockStatus BCM_Door_RL_notifyChildLockUnlockStatus;

            // 上报左前门开关状态
            @optional BCM_OpenStatus BCM_Door_FL_notifyOpenStatus;

            // 上报左后门开关状态
            @optional BCM_OpenStatus BCM_Door_RL_notifyOpenStatus;

            // 上报左前门解闭锁状态
            @optional BCM_lockStatus BCM_Door_FL_notifyLockUnlockStatus;

            // 上报左后门解闭锁状态
            @optional BCM_lockStatus BCM_Door_RL_notifyLockUnlockStatus;

            // 中控开关解闭锁请求
            @optional BCM_Lockctrl BCM_Door_CenLock_Ctrl;

        };
    };
};
