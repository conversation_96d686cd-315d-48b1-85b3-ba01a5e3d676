/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCC_ZCU_FR_Service_eSrv.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCC_ZCU_FR_Service_eSrv.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCC_ZCU_FR_eSrv {
        typedef uint8 ReturnCode; // 调用返回值

        enum RepairModeStatus { // 维修模式状态反馈
            @value(0x0) RepairModeStatus_OFF,
            @value(0x1) RepairModeStatus_ON,
            @value(0xFF) RepairModeStatus_INVALID
        };

        @nested
        struct RepairMode_setRepairMode_In {
            RepairModeStatus repairmodestatus;
        };

        @final
        union HPCC_ZCU_FR_Service_eSrv switch(int32) {
            // 通知其它部件当前的维修模式状态
            case 349695082:
                RepairMode_setRepairMode_In RepairMode_setRepairMode;
        };
    };
};
