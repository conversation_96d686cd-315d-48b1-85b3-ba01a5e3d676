/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_R_Enh_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_R_Enh_Status.idl
*/

module Seres {
    module ZCU_R_Enh {

        typedef uint8 APP_Reserved; // 预留

        typedef uint8 CS_TireSize; // 轮胎规格状态

        typedef uint8 CS_TireWarn; // 轮胎故障状态

        typedef uint8 CS_TireTemp; // 轮胎温度

        typedef uint16 CS_TirePressure; // 轮胎压力值

        typedef uint8 CS_TireId; // 轮胎标识

        @nested
        struct TireInstance { // 轮胎实例
            CS_TireId tireid; // 轮胎标识
            CS_TirePressure tirepressure; // 轮胎压力值
            CS_TireTemp tiretemperature; // 轮胎温度
            CS_TireWarn tirewarn; // 轮胎故障状态
            CS_TireSize tyresize; // 轮胎规格状态
            APP_Reserved reserved; // 预留
            APP_Reserved reserved; // 预留
            APP_Reserved reserved; // 预留
        };

        typedef sequence<TireInstance, 4> tireInfo; // 轮胎状态信息

        @final
        struct ZCU_R_Enh_Status{
            // 上报轮胎状态信息
            @optional tireInfo BCM_TPMS_notifytireInfo;

        };
    };
};
