/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/HPCT_STATUS.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/HPCT_STATUS.idl
*/

module Seres {
    module HPCT_STATUS {

        typedef string UpdatePhoneNumber;

        enum UpdateCallMode {
            @value(0x1) DATA_MODE,
            @value(0x2) VOICE_MODE
        };

        enum UpdateCallState {
            @value(0x0) IDLE,
            @value(0x1) DIAGLING,
            @value(0x2) INCOMING,
            @value(0x3) CONNECTED,
            @value(0x4) WAITING,
            @value(0x5) ORTHER_PARTY_HUNG_UP_CONNECTED_CALL,
            @value(0x6) TBOX_HUNG_UP_CONNECTED_CALL,
            @value(0x7) HUNG_UP_ABNORMALLY,
            @value(0x8) CONNECTING_CALL_HUNG_UP_BECAUSE_NO_ANSWER,
            @value(0x9) TBOX_HUNG_UP_CONNECTING,
            @value(0xA) TRIGGER_DIALING
        };

        enum UpdateXCALLType {
            @value(0x0) NONE,
            @value(0x1) ECALL,
            @value(0x2) BCALL,
            @value(0x3) ICALL
        };

        typedef uint32 XCALLSequenceNum;

        @nested
        struct UpdateXCALLStatus { // 上报XCALL状态
            XCALLSequenceNum xcallsequencenum; // XCALL序列号
            UpdateXCALLType updatexcalltype; // XCALL类型
            UpdateCallState updatecallstate; // 通话状态
            UpdateCallMode updatecallmode; // 通话模式
            UpdatePhoneNumber updatephonenumber; // 电话号码
        };

        @final
        struct HPCT_STATUS{
            // 通知XCALL状态
            @optional UpdateXCALLStatus INI_XCALL_updateXCALLStatus;

        };
    };
};
