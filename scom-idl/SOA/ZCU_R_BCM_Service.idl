/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/ZCU_R_BCM_Service.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/ZCU_R_BCM_Service.idl
*/

#include "rpcCommon.idl"

module Seres {
    module ZCU_R_BCM {
        typedef uint8 FoldFunSet; // 座椅折叠设置

        typedef uint8 MassageStrength; // 按摩强度

        typedef uint8 MassagMode; // 按摩模式

        typedef uint8 VentilationLevel; // 通风等级

        typedef uint8 HeatingLevel; // 加热等级

        typedef uint8 Percent; // 百分比

        typedef uint8 BCM_trunkCmd; // 设置后备箱12V电源

        typedef uint8 SunshadeOperation; // 遮阳帘工作

        typedef uint8 SunshadeEnable; // 天窗遮阳帘使能

        typedef uint8 RearDefrostSw; // 后除霜加热按键

        typedef uint8 RwiperModeStatus; // 后雨刮维修模式状态反馈

        typedef uint8 BCM_ChrgPosnTar; // 充电口盖运行目标位置

        typedef uint8 TMS_DoorPosn; // TMS电机位置

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        @nested
        struct TMS_TempDoorPosnValue { // 温区风门电机位置状态
            ACZoneId zoneid; // 温区编号
            TMS_DoorPosn tms_tempdoorposn; // 温区风门电机位置
        };

        typedef sequence<TMS_TempDoorPosnValue, 20> TMS_TempDoorPosn; // 温区风门电机位置状态

        @nested
        struct TMS_AirMode { // 空调出风模式
            TMS_DoorPosn face; // 吹面电机
            TMS_DoorPosn foot; // 吹脚电机
            TMS_DoorPosn defrost; // 除霜电机
        };

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 ACBlwLevel; // 风速档位

        @nested
        struct TMS_Ventilation_setLevel_ZCUR_In {
            ACZoneId aczoneid;
            ACBlwLevel acblvlevel;
        };

        @nested
        struct TMS_Ventilation_setMode_ZCUR_In {
            ACZoneId aczoneid;
            TMS_AirMode tms_airmode;
        };

        @nested
        struct TMS_Ventilation_setTemp_ZCUR_In {
            ACZoneId aczoneid;
            TMS_TempDoorPosn tms_tempdoorposn;
        };

        @nested
        struct BCM_ChargePortDoor_chargePortOperStr_In {
            BCM_ChrgPosnTar bcm_chrgposntar;
        };

        @nested
        struct BCM_WiperWash_R_stopWiping_In {
            uint8 _default;
        };

        @nested
        struct BCM_WiperWash_R_startWiping_In {
            uint8 _default;
        };

        @nested
        struct BCM_WiperWash_R_IntermittentWiping_In {
            uint8 _default;
        };

        @nested
        struct BCM_WiperWash_R_MaintenanceMode_In {
            RwiperModeStatus rwipermodestatus;
        };

        @nested
        struct TMS_Demister_Re_In {
            RearDefrostSw reardefrostsw;
        };

        @nested
        struct BCM_Sunshade_R_setSunshadeEnable_In {
            SunshadeEnable sunshadeenable;
        };

        @nested
        struct BCM_Sunshade_R_setSunshadeOperationSingle_In {
            SunshadeOperation sunshadeoperation;
        };

        @nested
        struct BCM_Sunshade_R_setSunshadeOperationRAC_In {
            SunshadeOperation sunshadeoperation;
        };

        @nested
        struct BCM_LvPowerPort_trunk_In {
            BCM_trunkCmd bcm_trunkcmd;
        };

        @nested
        struct BCM_Seat_RL_MainXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RL_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RL_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_RL_VentilationLevel_In {
            VentilationLevel ventilationlevel;
        };

        @nested
        struct BCM_Seat_RL_MassagMode_In {
            MassagMode massagmode;
        };

        @nested
        struct BCM_Seat_RL_MassageStrength_In {
            MassageStrength massagestrength;
        };

        @nested
        struct BCM_Seat_RR_RRFoldFunSet_In {
            FoldFunSet foldfunset;
        };

        @nested
        struct BCM_Seat_RR_MainXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RR_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RR_legRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RR_LegRestXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_RR_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_RR_VentilationLevel_In {
            VentilationLevel ventilationlevel;
        };

        @nested
        struct BCM_Seat_RR_MassagMode_In {
            MassagMode massagmode;
        };

        @nested
        struct BCM_Seat_RR_MassageStrength_In {
            MassageStrength massagestrength;
        };

        @nested
        struct BCM_Seat_ThirdL_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_ThirdL_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_ThirdL_ThirdLFoldFunSet_In {
            FoldFunSet foldfunset;
        };

        @nested
        struct BCM_Seat_ThirdR_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_ThirdR_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_ThirdR_ThirdRFoldFunSet_In {
            FoldFunSet foldfunset;
        };

        @final
        union ZCU_R_BCM_Service switch(int32) {
            // 设置指定温区风速档位
            case 419533131:
                TMS_Ventilation_setLevel_ZCUR_In TMS_Ventilation_setLevel_ZCUR;

            // 设置指定温区的吹风模式
            case -1064827133:
                TMS_Ventilation_setMode_ZCUR_In TMS_Ventilation_setMode_ZCUR;

            // 设置指定温区温度
            case -1442929615:
                TMS_Ventilation_setTemp_ZCUR_In TMS_Ventilation_setTemp_ZCUR;

            // 控制充电口盖
            case 576523965:
                BCM_ChargePortDoor_chargePortOperStr_In BCM_ChargePortDoor_chargePortOperStr;

            // 后雨刮停止控制
            case -18889181:
                BCM_WiperWash_R_stopWiping_In BCM_WiperWash_R_stopWiping;

            // 后雨刮开启控制
            case 1552431351:
                BCM_WiperWash_R_startWiping_In BCM_WiperWash_R_startWiping;

            // 后雨刮间隙控制
            case 1370640768:
                BCM_WiperWash_R_IntermittentWiping_In BCM_WiperWash_R_IntermittentWiping;

            // 后雨刮维修模式控制
            case -1646217475:
                BCM_WiperWash_R_MaintenanceMode_In BCM_WiperWash_R_MaintenanceMode;

            // 后除霜加热按键
            case -1417389504:
                TMS_Demister_Re_In TMS_Demister_Re;

            // 后天窗遮阳帘使能
            case -1207023225:
                BCM_Sunshade_R_setSunshadeEnable_In BCM_Sunshade_R_setSunshadeEnable;

            // 后遮阳帘工作
            case -962785155:
                BCM_Sunshade_R_setSunshadeOperationSingle_In BCM_Sunshade_R_setSunshadeOperationSingle;

            // 后排屏控后遮阳帘工作
            case 796277474:
                BCM_Sunshade_R_setSunshadeOperationRAC_In BCM_Sunshade_R_setSunshadeOperationRAC;

            // 设置后备箱12V电源
            case 502169756:
                BCM_LvPowerPort_trunk_In BCM_LvPowerPort_trunk;

            // 二排左侧座椅前后位置设置
            case -1768979667:
                BCM_Seat_RL_MainXDir_In BCM_Seat_RL_MainXDir;

            // 二排左侧座椅靠背角度位置设置
            case 1006586607:
                BCM_Seat_RL_BackRestAngle_In BCM_Seat_RL_BackRestAngle;

            // 二排左侧座椅加热功能请求
            case 679027193:
                BCM_Seat_RL_HeatingLevel_In BCM_Seat_RL_HeatingLevel;

            // 二排左侧座椅通风功能请求
            case -466134858:
                BCM_Seat_RL_VentilationLevel_In BCM_Seat_RL_VentilationLevel;

            // 二排左侧座椅按摩模式请求
            case 1548324756:
                BCM_Seat_RL_MassagMode_In BCM_Seat_RL_MassagMode;

            // 二排左侧座椅按摩强度请求
            case 1395947961:
                BCM_Seat_RL_MassageStrength_In BCM_Seat_RL_MassageStrength;

            // 二排右侧座椅折叠功能设置
            case -115817105:
                BCM_Seat_RR_RRFoldFunSet_In BCM_Seat_RR_RRFoldFunSet;

            // 二排右侧座椅前后位置设置
            case -846631896:
                BCM_Seat_RR_MainXDir_In BCM_Seat_RR_MainXDir;

            // 二排右侧座椅靠背角度位置设置
            case -1371081154:
                BCM_Seat_RR_BackRestAngle_In BCM_Seat_RR_BackRestAngle;

            // 二排右侧座椅腿托角度
            case -1255238347:
                BCM_Seat_RR_legRestAngle_In BCM_Seat_RR_legRestAngle;

            // 二排右侧座椅腿托长度
            case 2032530487:
                BCM_Seat_RR_LegRestXDir_In BCM_Seat_RR_LegRestXDir;

            // 二排右侧座椅加热功能请求
            case -901325860:
                BCM_Seat_RR_HeatingLevel_In BCM_Seat_RR_HeatingLevel;

            // 二排右侧座椅通风功能请求
            case 906740385:
                BCM_Seat_RR_VentilationLevel_In BCM_Seat_RR_VentilationLevel;

            // 二排右侧座椅按摩模式请求
            case -79843534:
                BCM_Seat_RR_MassagMode_In BCM_Seat_RR_MassagMode;

            // 二排右侧座椅按摩强度请求
            case -1235121777:
                BCM_Seat_RR_MassageStrength_In BCM_Seat_RR_MassageStrength;

            // 三排左侧座椅靠背角度位置设置
            case 873405632:
                BCM_Seat_ThirdL_BackRestAngle_In BCM_Seat_ThirdL_BackRestAngle;

            // 三排左侧座椅加热功能请求
            case 933108302:
                BCM_Seat_ThirdL_HeatingLevel_In BCM_Seat_ThirdL_HeatingLevel;

            // 三排左侧座椅折叠功能设置
            case -677983682:
                BCM_Seat_ThirdL_ThirdLFoldFunSet_In BCM_Seat_ThirdL_ThirdLFoldFunSet;

            // 三排右侧座椅靠背角度位置设置
            case 884675155:
                BCM_Seat_ThirdR_BackRestAngle_In BCM_Seat_ThirdR_BackRestAngle;

            // 三排右侧座椅加热功能请求
            case 12402247:
                BCM_Seat_ThirdR_HeatingLevel_In BCM_Seat_ThirdR_HeatingLevel;

            // 三排右侧座椅折叠功能设置
            case 1434451459:
                BCM_Seat_ThirdR_ThirdRFoldFunSet_In BCM_Seat_ThirdR_ThirdRFoldFunSet;
        };
    };
};
