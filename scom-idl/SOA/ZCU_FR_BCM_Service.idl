/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/ZCU_FR_BCM_Service.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/ZCU_FR_BCM_Service.idl
*/

#include "rpcCommon.idl"

module Seres {
    module ZCU_FR_BCM {
        typedef uint8 LinkageSet; // 联动状态

        typedef uint8 FoldFunSet; // 座椅折叠设置

        typedef uint8 MassageStrength; // 按摩强度

        typedef uint8 MassagMode; // 按摩模式

        typedef uint8 VentilationLevel; // 通风等级

        typedef uint8 HeatingLevel; // 加热等级

        typedef uint8 ElecSideStep_motorLearnCMD; // 设置左侧电动踏板自学习指令

        typedef uint8 BCM_ElecSideStepCmd; // 控制电动踏板

        typedef uint8 BCM_ArmrestCmd; // 设置扶手箱12V电源

        typedef uint8 AdjustPosition; // 车窗开度信息

        typedef uint8 BCM_CHBDoorOnOffReq; // 车载冷暖箱电动门弹开指令（电动开关）

        typedef int8 BCM_CHBTempReq; // 车载冷暖箱温度调节指令

        typedef uint8 BCM_CHBTempModeReq; // 车载冷暖箱温控模式调节指令

        typedef uint8 BCM_CHBOnOffReq; // 车载冷暖箱开关指令

        @nested
        struct BCM_CHBCmd { // 设置车载冷暖箱运行指令
            BCM_CHBOnOffReq bcm_chbonoffreq; // 车载冷暖箱开关指令
            BCM_CHBTempModeReq bcm_chbtempmodereq; // 车载冷暖箱温控模式调节指令
            BCM_CHBTempReq bcm_chbtempreq; // 车载冷暖箱温度调节指令
            BCM_CHBDoorOnOffReq bcm_chbdooronoffreq; // 车载冷暖箱电动门弹开指令（电动开关）
        };

        typedef uint8 RearDefrostSw; // 后除霜加热按键

        typedef uint8 RearView_FoldUnfold; // 后视镜折叠控制

        typedef uint8 Percent; // 百分比

        typedef uint8 LRStepSet; // 左右步进调节

        typedef uint8 UDStepSet; // 上下步进调节

        typedef uint16 CFM_SpeedCmd; // 转速设置

        enum CS_WiperSensitivityLevel { // 雨刮自动档灵敏度
            @value(0x0) CS_WiperSensitivityLevel_NO_REQUEST,
            @value(0x1) CS_WiperSensitivityLevel_LOW,
            @value(0x2) CS_WiperSensitivityLevel_MEDIUM,
            @value(0x3) CS_WiperSensitivityLevel_HIGH
        };

        typedef uint8 UVCswitch; // 设置UVC开关

        typedef uint8 IONswitch; // 设置负离子开关

        typedef uint8 TMS_DoorPosn; // TMS电机位置

        typedef uint8 TMS_CyclingMode; // 通风循环模式电机位置

        @nested
        struct TMS_AirMode { // 空调出风模式
            TMS_DoorPosn face; // 吹面电机
            TMS_DoorPosn foot; // 吹脚电机
            TMS_DoorPosn defrost; // 除霜电机
        };

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 ACBlwLevel; // 风速档位

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        @nested
        struct TMS_Ventilation_setLevel_ZCUFR_In {
            ACZoneId aczoneid;
            ACBlwLevel acblvlevel;
        };

        @nested
        struct TMS_Ventilation_setMode_ZCUFR_In {
            ACZoneId aczoneid;
            TMS_AirMode tms_airmode;
        };

        @nested
        struct TMS_Ventilation_setCyclingMode_ZCUFR_In {
            TMS_CyclingMode tms_cyclingmode;
        };

        @nested
        struct TMS_Ventilation_setDouble_layerCycling_ZCUFR_In {
            TMS_CyclingMode tms_doublelayercyclingdoorposn;
        };

        @nested
        struct TMS_Ventilation_setTemp_ZCUFR_In {
            ACZoneId aczoneid;
            TMS_DoorPosn tms_tempdoorposn;
        };

        @nested
        struct TMS_Ventilation_setDefostPriority_ZCUFR_In {
            TMS_DoorPosn tms_defostprioritydoorposn;
        };

        @nested
        struct TMS_EnvMonitor_setIONswitch_In {
            IONswitch ionswitch;
        };

        @nested
        struct TMS_EnvMonitor_setUVCswitch_In {
            UVCswitch uvcswitch;
        };

        @nested
        struct BCM_Wiper_CS_In {
            CS_WiperSensitivityLevel cs_wipersensitivitylevel;
        };

        @nested
        struct TMS_Cfm_setSpeed_ZCUFR_In {
            CFM_SpeedCmd cfm_speedcmd;
        };

        @nested
        struct BCM_WiperWash_stopSprayWashing_In {
            uint8 _default;
        };

        @nested
        struct BCM_WiperWash_R_startSprayWashing_In {
            uint8 _default;
        };

        @nested
        struct BCM_WiperWash_F_startSprayWashing_In {
            uint8 _default;
        };

        @nested
        struct BCM_RearView_R_RmirrorUDStepSet_In {
            UDStepSet udstepset;
        };

        @nested
        struct BCM_RearView_R_RmirrorLRStepSet_In {
            LRStepSet lrstepset;
        };

        @nested
        struct BCM_RearView_R_RmirrorUDSet_In {
            Percent percent;
        };

        @nested
        struct BCM_RearView_R_RmirrorLRSet_In {
            Percent percent;
        };

        @nested
        struct BCM_RearView_R_FoldUnfold_In {
            RearView_FoldUnfold foldunfold;
        };

        @nested
        struct BCM_RearView_R__RearDefrostSw_In {
            RearDefrostSw reardefrostsw;
        };

        @nested
        struct BCM_CoolgHeatgBox_setCHBCmd_In {
            BCM_CHBCmd setchbcmd;
        };

        @nested
        struct BCM_Window_FR_adjustPosition_In {
            AdjustPosition adjustposition;
        };

        @nested
        struct BCM_Window_FR_open_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_FR_close_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_FR_stop_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RR_lock_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RR_unlock_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RR_adjustPosition_In {
            AdjustPosition adjustposition;
        };

        @nested
        struct BCM_Window_RR_open_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RR_close_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RR_stop_In {
            uint8 _default;
        };

        @nested
        struct BCM_LvPowerPort_Armrest_In {
            BCM_ArmrestCmd bcm_armrestcmd;
        };

        @nested
        struct BCM_ElecSideStep_R_In {
            BCM_ElecSideStepCmd bcm_elecsidestepcmd;
        };

        @nested
        struct BCM_ElecSideStep_R_SetmotorLearnCMD_In {
            ElecSideStep_motorLearnCMD elecsidestep_motorlearncmd;
        };

        @nested
        struct BCM_Seat_FR_MainXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FR_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FR_legRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FR_LegRestXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FR_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_FR_VentilationLevel_In {
            VentilationLevel ventilationlevel;
        };

        @nested
        struct BCM_Seat_FR_MassagMode_In {
            MassagMode massagmode;
        };

        @nested
        struct BCM_Seat_FR_MassageStrength_In {
            MassageStrength massagestrength;
        };

        @nested
        struct BCM_Seat_FR_PassSeatFoldFunSet_In {
            FoldFunSet foldfunset;
        };

        @nested
        struct BCM_Seat_FR_PassSeatLinkageSet_In {
            LinkageSet linkageset;
        };

        @final
        union ZCU_FR_BCM_Service switch(int32) {
            // 设置指定温区风速档位
            case -51368312:
                TMS_Ventilation_setLevel_ZCUFR_In TMS_Ventilation_setLevel_ZCUFR;

            // 设置指定温区的吹风模式
            case -1825960984:
                TMS_Ventilation_setMode_ZCUFR_In TMS_Ventilation_setMode_ZCUFR;

            // 设置通风循环工作模式
            case -1465963898:
                TMS_Ventilation_setCyclingMode_ZCUFR_In TMS_Ventilation_setCyclingMode_ZCUFR;

            // 设置宽域双层流电机通风循环工作模式
            case 999123276:
                TMS_Ventilation_setDouble_layerCycling_ZCUFR_In TMS_Ventilation_setDouble_layerCycling_ZCUFR;

            // 设置指定温区温度
            case 1375045763:
                TMS_Ventilation_setTemp_ZCUFR_In TMS_Ventilation_setTemp_ZCUFR;

            // 设定除霜优先风门
            case -2115753563:
                TMS_Ventilation_setDefostPriority_ZCUFR_In TMS_Ventilation_setDefostPriority_ZCUFR;

            // 设置负离子开关
            case 1030705788:
                TMS_EnvMonitor_setIONswitch_In TMS_EnvMonitor_setIONswitch;

            // 设置UVC开关
            case 712501446:
                TMS_EnvMonitor_setUVCswitch_In TMS_EnvMonitor_setUVCswitch;

            // 设置雨刮灵敏度
            case -422810259:
                BCM_Wiper_CS_In BCM_Wiper_CS;

            // 设置冷却风扇目标转速
            case 1620076108:
                TMS_Cfm_setSpeed_ZCUFR_In TMS_Cfm_setSpeed_ZCUFR;

            // 雨刮洗涤控制
            case 1741616264:
                BCM_WiperWash_stopSprayWashing_In BCM_WiperWash_stopSprayWashing;

            // 雨刮洗涤控制
            case 1994611795:
                BCM_WiperWash_R_startSprayWashing_In BCM_WiperWash_R_startSprayWashing;

            // 雨刮洗涤控制
            case -1758767577:
                BCM_WiperWash_F_startSprayWashing_In BCM_WiperWash_F_startSprayWashing;

            // 右侧后视镜上下步进调节
            case -742169719:
                BCM_RearView_R_RmirrorUDStepSet_In BCM_RearView_R_RmirrorUDStepSet;

            // 右侧后视镜左右步进调节
            case 1185530226:
                BCM_RearView_R_RmirrorLRStepSet_In BCM_RearView_R_RmirrorLRStepSet;

            // 右侧后视镜上下调节设置
            case -2051461752:
                BCM_RearView_R_RmirrorUDSet_In BCM_RearView_R_RmirrorUDSet;

            // 右侧后视镜左右调节设置
            case 987521704:
                BCM_RearView_R_RmirrorLRSet_In BCM_RearView_R_RmirrorLRSet;

            // 右后视镜折叠控制
            case 561710256:
                BCM_RearView_R_FoldUnfold_In BCM_RearView_R_FoldUnfold;

            // 右后视镜加热按键
            case -608343922:
                BCM_RearView_R__RearDefrostSw_In BCM_RearView_R__RearDefrostSw;

            // 设置车载冷暖箱运行指令
            case 949072697:
                BCM_CoolgHeatgBox_setCHBCmd_In BCM_CoolgHeatgBox_setCHBCmd;

            // 调整右前车窗的车窗开度
            case -1672444208:
                BCM_Window_FR_adjustPosition_In BCM_Window_FR_adjustPosition;

            // 打开右前车窗
            case -2014704031:
                BCM_Window_FR_open_In BCM_Window_FR_open;

            // 关闭右前车窗
            case -871462018:
                BCM_Window_FR_close_In BCM_Window_FR_close;

            // 停止开启或关闭右前车窗
            case 1783574256:
                BCM_Window_FR_stop_In BCM_Window_FR_stop;

            // 闭锁右后车窗的窗锁
            case 1479910760:
                BCM_Window_RR_lock_In BCM_Window_RR_lock;

            // 解锁右后车窗的窗锁
            case -1316532368:
                BCM_Window_RR_unlock_In BCM_Window_RR_unlock;

            // 调整车窗的右后车窗开度
            case -1101229713:
                BCM_Window_RR_adjustPosition_In BCM_Window_RR_adjustPosition;

            // 打开右后车窗
            case 1097737413:
                BCM_Window_RR_open_In BCM_Window_RR_open;

            // 关闭右后车窗
            case 1358314213:
                BCM_Window_RR_close_In BCM_Window_RR_close;

            // 停止开启或关闭右后车窗
            case 1151135048:
                BCM_Window_RR_stop_In BCM_Window_RR_stop;

            // 设置扶手箱12V电源
            case 1711188557:
                BCM_LvPowerPort_Armrest_In BCM_LvPowerPort_Armrest;

            // 右侧电动踏板
            case 669470802:
                BCM_ElecSideStep_R_In BCM_ElecSideStep_R;

            // 设置右侧电动踏板自学习指令
            case -1813730968:
                BCM_ElecSideStep_R_SetmotorLearnCMD_In BCM_ElecSideStep_R_SetmotorLearnCMD;

            // 副驾座椅前后位置设置
            case 1501232048:
                BCM_Seat_FR_MainXDir_In BCM_Seat_FR_MainXDir;

            // 副驾座椅靠背角度位置设置
            case 77468417:
                BCM_Seat_FR_BackRestAngle_In BCM_Seat_FR_BackRestAngle;

            // 副驾座椅腿托角度
            case -1369312023:
                BCM_Seat_FR_legRestAngle_In BCM_Seat_FR_legRestAngle;

            // 副驾座椅腿托长度
            case 2029704758:
                BCM_Seat_FR_LegRestXDir_In BCM_Seat_FR_LegRestXDir;

            // 副驾座椅加热功能请求
            case -794468465:
                BCM_Seat_FR_HeatingLevel_In BCM_Seat_FR_HeatingLevel;

            // 副驾座椅通风功能请求
            case -1213601197:
                BCM_Seat_FR_VentilationLevel_In BCM_Seat_FR_VentilationLevel;

            // 副驾座椅按摩模式请求
            case 539272095:
                BCM_Seat_FR_MassagMode_In BCM_Seat_FR_MassagMode;

            // 副驾座椅按摩强度请求
            case -586634955:
                BCM_Seat_FR_MassageStrength_In BCM_Seat_FR_MassageStrength;

            // 副驾座椅折叠功能设置
            case -1366158431:
                BCM_Seat_FR_PassSeatFoldFunSet_In BCM_Seat_FR_PassSeatFoldFunSet;

            // 副驾座椅联动设置
            case -1517283227:
                BCM_Seat_FR_PassSeatLinkageSet_In BCM_Seat_FR_PassSeatLinkageSet;
        };
    };
};
