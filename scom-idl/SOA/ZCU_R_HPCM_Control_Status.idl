/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_R_HPCM_Control_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_R_HPCM_Control_Status.idl
*/

module Seres {
    module ZCU_R_HPCM_Control {

        typedef uint8 ISDFltSt; // ISD故障反馈

        typedef uint8 ISDEffectFbSt; // ISD显示效果反馈

        typedef uint8 ISDSceneFbSt; // ISD图片显示场景反馈

        typedef uint8 ISDShowFbSt; // ISD显示反馈

        @nested
        struct ISDStatusReporting { // ISD状态上报
            ISDShowFbSt isdshowfbst; // ISD显示反馈
            ISDSceneFbSt isdscenefbst; // ISD图片显示场景反馈
            ISDEffectFbSt isdeffectfbst; // ISD显示效果反馈
            ISDFltSt isdfltst; // ISD故障反馈
        };

        typedef uint8 LampFlow; // 流水状态

        typedef uint8 NtfFltSt; // 灯故障状态

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        @nested
        struct NtfList { // 车灯状态
            LampCmd bemalightstatus; // 车灯开关状态
            DutyRat brightnessstatus; // 车灯亮度
        };

        @nested
        struct TurnLampStatusReporting { // 转向灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
            LampFlow lampflow; // 流水状态
        };

        @nested
        struct GeneralLampStatusReporting { // 通用灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
        };

        @final
        struct ZCU_R_HPCM_Control_Status{
            // 上报右后制动灯状态
            @optional GeneralLampStatusReporting BCM_BrkLamp_RR_notify;

            // 上报左后制动灯状态
            @optional GeneralLampStatusReporting BCM_BrkLamp_RL_notify;

            // 上报中后制动灯状态
            @optional GeneralLampStatusReporting BCM_BrkLamp_RM_notify;

            // 上报高位制动灯状态
            @optional GeneralLampStatusReporting BCM_BrkLamp_Hi_notify;

            // 上报中后倒车灯状态
            @optional GeneralLampStatusReporting BCM_RvsLamp_RM_notify;

            // 上报后雾灯状态
            @optional GeneralLampStatusReporting BCM_FogLamp_Re_notify;

            // 上报后牌照状态
            @optional GeneralLampStatusReporting BCM_LicensePlateLamp_Re_notify;

            // 上报左后位置灯状态
            @optional GeneralLampStatusReporting BCM_PosnLamp_RL_notify;

            // 上报右后位置灯状态
            @optional GeneralLampStatusReporting BCM_PosnLamp_RR_notify;

            // 上报中后位置灯状态
            @optional GeneralLampStatusReporting BCM_PosnLamp_RM_notify;

            // 上报左后转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RL_notify;

            // 上报右后转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RR_notify;

            // 上报中左后转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RML_notify;

            // 上报中右后转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RMR_notify;

            // 左后ISD状态反馈
            @optional ISDStatusReporting BCM_ISDStatusReporting_RL_notify;

            // 右后ISD状态反馈
            @optional ISDStatusReporting BCM_ISDStatusReporting_RR_notify;

            // 中后ISD状态反馈
            @optional ISDStatusReporting BCM_ISDStatusReporting_RM_notify;

        };
    };
};
