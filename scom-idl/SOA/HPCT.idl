/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCT.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCT.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCT_Space {
        typedef uint8 ReturnCode; // 调用返回值

        @nested
        struct INI_XCALL_XCALLOperation_In {
            uint8 _default;
        };

        @nested
        struct INI_XCALL_XCALLStatusReport_In {
            uint8 _default;
        };

        @final
        union HPCT switch(int32) {
            // XCALL操作
            case 1187122377:
                INI_XCALL_XCALLOperation_In INI_XCALL_XCALLOperation;

            // 确认收到XCALL状态
            case -855168239:
                INI_XCALL_XCALLStatusReport_In INI_XCALL_XCALLStatusReport;
        };
    };
};
