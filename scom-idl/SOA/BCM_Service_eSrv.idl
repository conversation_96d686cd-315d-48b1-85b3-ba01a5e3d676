/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/BCM_Service_eSrv.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/BCM_Service_eSrv.idl
*/

#include "rpcCommon.idl"

module Seres {
    module BCM_eSrv {
        typedef uint32 HPCC_VoiceLightMode; // 语音交互灯效

        typedef uint32 HPCC_RGB; // 灯颜色

        typedef uint8 HPCC_Brightness; // 灯亮度

        typedef uint8 HCPP_AmbZoneID; // 氛围灯分区

        @nested
        struct HPCC_SchroederLightCtrl { // 施罗德灯设置
            HPCC_Brightness brightness; // 亮度
            HPCC_RGB rgb; // 颜色
        };

        @nested
        struct HPCC_AmbRgbBrightnessCtrl { // 光导氛围灯设置
            HCPP_AmbZoneID ambzoneid; // 氛围灯区域
            HPCC_Brightness brightness; // 亮度
            HPCC_RGB rgb; // 颜色
        };

        typedef uint8 HPCC_OnOffCmd; // 开关设置

        @nested
        struct HPCC_MusicAmbCtrl { // 音乐律动氛围灯区域设置
            HCPP_AmbZoneID ambzoneid; // 氛围灯区域
            HPCC_OnOffCmd onoffcmd; // 开关设置
        };

        @nested
        struct HPCC_MusicRhythmOp { // 音乐律动设置
            HPCC_MusicAmbCtrl ambctrl; // 氛围灯开关设置
            HPCC_AmbRgbBrightnessCtrl ambset; // 光导氛围灯颜色/亮度设置
            HPCC_SchroederLightCtrl schroederset; // 单株氛围灯颜色/亮度设置
        };

        typedef uint8 HPCC_AmbientLightTheme; // 氛围灯主题

        typedef uint8 HPCC_SunRoofOp; // 遮阳帘控制

        typedef uint8 HPCC_SunRoofID; // 遮阳帘id

        typedef uint8 HPCC_WindowPosition; // 车窗开度

        typedef uint8 HPCC_WindowID; // 车窗ID

        typedef uint8 HPCC_VentilationMode; // 通风模式

        typedef uint8 HPCC_MassageStrength; // 座椅按摩等级

        typedef uint8 HPCC_VentilationLevel; // 座椅通风等级

        typedef uint8 HPCC_HeatLevel; // 座椅加热等级

        typedef uint8 HPCC_FoldPara; // 座椅折叠控制

        typedef uint8 HPCC_ZeroGravityPara; // 零重力座椅控制

        typedef uint8 HPCC_SeatID; // 座椅ID

        typedef uint8 HPCC_RecircleMode; // 空调循环模式

        typedef uint8 HPCC_F_Or_R; // 除霜/除雾区域

        typedef uint8 HPCC_MaxHeatStatus; // 最大制热状态

        typedef uint8 HPCC_MaxACStatus; // 最大制冷状态

        typedef uint8 HPCC_ClimateMode_Op; // 空调风量模式

        typedef uint8 ACBlwLevel; // 风速档位

        typedef float HPCC_Temperature; // 温度设置

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        typedef uint8 HPCC_ClimateID; // 空调ID

        typedef uint8 HPCC_OpenSpeed; // 车门开启关闭速度

        typedef uint8 HPCC_SideDoor_Op; // 侧门控制

        typedef uint8 HPCC_DoorID; // 侧门ID

        typedef uint8 HPCC_LockUnLockPara; // 解锁闭锁行为设置

        typedef uint8 HPCC_MaxTargetPosition; // 最大开度设置

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 HPCC_TailGate_Op; // 尾门控制

        typedef uint16 BCM_CallerID; // 调用识别方

        interface BCM_Service_eSrv {
            // 电动尾门开启/关闭/暂停控制增强服务
            ReturnCode BCM_TailGate_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_TailGate_Op tailgate_op);

            // 电动尾门最大开度调节增强服务
            ReturnCode BCM_TailGate_SetMaxTargetPosition_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_MaxTargetPosition maxtargetposition);

            // 前舱盖开启/关闭/暂停控制增强服务
            ReturnCode BCM_FrntHatch_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_LockUnLockPara frnhatch_op);

            // 充电口盖开启/关闭控制增强服务
            ReturnCode BCM_ChrgPort_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 加油口盖开启/关闭控制增强服务
            ReturnCode BCM_FuelFiller_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 电动车门的开启/关闭/停止控制增强服务
            ReturnCode BCM_SideDoor_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_DoorID doorid, in HPCC_SideDoor_Op sidedoor_op);

            // 电动门设置为普通手动模式/非普通手动模式增强服务
            ReturnCode BCM_SideDoor_ManuaMode_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 电动门最大开启角度设置
            ReturnCode BCM_SideDoor_MaxTargetPos_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_DoorID doorid, in HPCC_MaxTargetPosition maxtargetposition);

            // 电动门开启速度设置
            ReturnCode BCM_SideDoor_OpenSpeed_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OpenSpeed openspeed);

            // 踩刹车主动关闭主驾门
            ReturnCode BCM_BrakePadCloseDoorMode_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 控制前排/二排/三排空调的开启/关闭增强服务
            ReturnCode TMS_Climate_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_ClimateID climateid, in HPCC_OnOffCmd onoffcmd);

            // 控制前排/二排/三排空调的AUTO开启/关闭增强服务
            ReturnCode TMS_Climate_Auto_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_ClimateID climateid, in HPCC_OnOffCmd onoffcmd);

            // 调节主驾/副驾/二排/三排的空调温度增强服务
            ReturnCode TMS_Climate_Temperature_Ctrl_Enh(in BCM_CallerID bcm_callerid, in ACZoneId zoneid, in HPCC_Temperature temperature);

            // 空调温区一键同步主驾增强服务
            ReturnCode TMS_Climate_Temperature_Sync_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 前排/二排/三排风量调节增强服务
            ReturnCode TMS_Climate_Level_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_ClimateID climateid, in ACBlwLevel acblwlevel);

            // 前排/二排/三排风量模式控制增强服务
            ReturnCode TMS_Climate_Mode_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_ClimateID climateid, in HPCC_ClimateMode_Op climatemode_op, in HPCC_OnOffCmd onoffcmd);

            // 最大制冷制热控制增强服务
            ReturnCode TMS_MaxAsHeat_Status_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_MaxACStatus maxacstatus, in HPCC_MaxHeatStatus maxheatstatus);

            // AC控制增强服务
            ReturnCode TMS_ACSwitch_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 除霜/除雾增强服务
            ReturnCode TMS_Demist_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_F_Or_R f_or_r, in HPCC_OnOffCmd onoffcmd);

            // 自动除霜/除雾开关增强服务
            ReturnCode TMS_Auto_Demist_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 一键空气净化增强服务
            ReturnCode TMS_AirPurify_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 二排空调锁屏增强服务
            ReturnCode TMS_AC_SetBox_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 驻车自动通风增强服务
            ReturnCode BCM_Auto_Ventilation_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 设置循环模式：内循环、外循环、自动循环
            ReturnCode EMS_RecircleMode_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_RecircleMode recirclemode);

            // 智能温区模式增强服务
            ReturnCode EMS_SmartZones_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 控制低压下电增强服务
            ReturnCode EMS_LowVoltage_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 控制低压抛负载增强服务
            ReturnCode EMS_LowVoltage_Energy_Ctrl_Enh(in BCM_CallerID bcm_callerid);

            // 控制车载电源口功能使能项开启/关闭增强服务
            ReturnCode EMS_12V_PowerPort_Ctrl_Enh(in BCM_CallerID bcm_callerid, in HPCC_OnOffCmd onoffcmd);

            // 零重力座椅一键展开/收回/暂停
            ReturnCode BCM_Seat_ZeroGravity_Ctrl_Enh(in HPCC_SeatID seatid, in HPCC_ZeroGravityPara zerogravityparam);

            // 零重力座椅儿童锁功能使能
            ReturnCode BCM_Seat_ZeroGravity_ChildLock_Ctrl_Enh(in HPCC_SeatID seatid, in HPCC_OnOffCmd onoffcmd);

            // 座椅一键折叠/恢复
            ReturnCode BCM_Seat_Fold_Ctrl_Enh(in HPCC_SeatID seatid, in HPCC_FoldPara foldpara);

            // 座椅加热档位调节
            ReturnCode BCM_Seat_HeatLevel_Enh(in HPCC_SeatID seatid, in HPCC_HeatLevel heatlevel);

            // 座椅通风档位调节
            ReturnCode BCM_Seat_VentilationLevel_Enh(in HPCC_SeatID seatid, in HPCC_VentilationLevel ventilationlevel);

            // 座椅按摩强度/模式调节
            ReturnCode BCM_Seat_Massage_Ctrl_Enh(in HPCC_SeatID seatid, in HPCC_MassageStrength onoffcmd);

            // 儿童座椅加热/通风
            ReturnCode BCM_Seat_ChildHeatVentilation_Enh(in HPCC_SeatID seatid, in HPCC_OnOffCmd heatonoff, in HPCC_VentilationMode ventilationmode);

            // 儿童遗落提醒设置
            ReturnCode BCM_Seat_ChildLeftBehind_Enh(in HPCC_OnOffCmd onoffcmd);

            // 闭锁升窗设置
            ReturnCode BCM_Window_LockUp_Enh(in HPCC_OnOffCmd onoffcmd);

            // 车窗控制
            ReturnCode BCM_Window_Ctrl_Enh(in HPCC_WindowID windowid, in HPCC_WindowPosition windowpos);

            // 天窗遮阳帘控制
            ReturnCode BCM_SunRoof_Ctrl_Enh(in HPCC_SunRoofID sunroofid, in HPCC_SunRoofOp sunroofop);

            // 闭锁自动关闭天窗遮阳帘设置
            ReturnCode BCM_SunRoof_AUTOLock_Enh(in HPCC_OnOffCmd onoffcmd);

            // 设置顶灯的开启/关闭
            ReturnCode BCM_RoofLight_Ctrl_Enh(in HPCC_OnOffCmd onoffcmd);

            // 氛围灯主题选择
            ReturnCode BCM_AmbientLight_Theme_Enh(in HPCC_AmbientLightTheme theme);

            // 音律随动主题控制
            ReturnCode BCM_MusicRhyLight_Ctrl_Enh(in HPCC_MusicRhythmOp musicrhythmop);

            // 氛围灯亮度调节
            ReturnCode BCM_AmbientLight_Brightness_Ctrl_Enh(in HCPP_AmbZoneID ambzoneid, in HPCC_Brightness brightness);

            // 自定义模式下颜色调节
            ReturnCode BCM_AmbientLight_RGB_Ctrl_Enh(in HCPP_AmbZoneID ambzoneid, in HPCC_RGB rgb);

            // 颜色&亮度一键同步
            ReturnCode BCM_AmbientLight_Sync_Enh(in HPCC_OnOffCmd onoffcmd);

            // 迎宾灯语功能设置
            ReturnCode BCM_WelLight_Ctrl_Enh(in HPCC_OnOffCmd onoffcmd);

            // 小憩模式灯语的开启/关闭
            ReturnCode BCM_NapMode_Ctrl_Enh(in HPCC_OnOffCmd onoffcmd);

            // 后方来车预警（DOW）& 转向盲点提示（BSD）灯语功能设置
            ReturnCode BCM_DOW_BSD_Ctrl_Enh(in HPCC_OnOffCmd onoffcmd);

            // 语音交互灯语功能设置
            ReturnCode BCM_VoiceInteractionLight_Ctrl_Enh(in HPCC_OnOffCmd onoffcmd, in HPCC_VoiceLightMode voicelightmode);
        };
    };
};
