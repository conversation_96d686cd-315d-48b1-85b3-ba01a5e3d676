/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/HPCM_BCM_Service_eSrv.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/HPCM_BCM_Service_eSrv.idl
*/

#include "rpcCommon.idl"

module Seres {
    module HPCM_BCM_eSrv {
        typedef uint8 HPCC_WindowID; // 车窗ID

        typedef uint8 HPCC_DoorID; // 侧门ID

        typedef uint8 HPCC_LockUnLockPara; // 解锁闭锁行为设置

        typedef uint8 HPCC_IntelSigDisp_Op; // 位置灯个性皮肤设置

        typedef uint8 HPCC_CfgPara; // 伴我回家设置

        typedef uint8 HPCC_Zangle; // 大灯高度

        typedef uint8 HPCC_OnOffCmd; // 开关设置

        typedef uint8 HPCC_BeamMode; // 灯光模式

        typedef uint16 BCM_CallerID; // 调用识别方

        enum PowerMode { // 电源模式
            @value(0x0) POWERMODE_OFF,
            @value(0x1) POWERMODE_ON,
            @value(0x2) POWERMODE_RESERVED,
            @value(0x3) POWERMODE_INVALID
        };

        typedef uint8 CarMode; // 整车模式

        typedef uint16 ExitDelayTi; // 退出延时时间

        typedef uint8 ReturnCode; // 调用返回值

        enum UsageMode { // 用户模式
            @value(0x0) USAGEMODE_ABANDONED,
            @value(0x1) USAGEMODE_INACTIVE,
            @value(0x2) USAGEMODE_CONVENIENCE,
            @value(0x3) USAGEMODE_DRIVING,
            @value(0x4) USAGEMODE_OTAUPDATING,
            @value(0x5) USAGEMODE_REMOTE,
            @value(0x6) USAGEMODE_REMOTEDRIVING,
            @value(0x7) USAGEMODE_RESERVED,
            @value(0xF) USAGEMODE_INVALID
        };

        typedef uint16 AppSrv_CallerId; // 服务调用者标识

        @nested
        struct VMM_UsageMode_EnterUsageMode_In {
            AppSrv_CallerId appsrv_calleridarg;
            UsageMode usagemodearg;
        };

        @nested
        struct VMM_UsageMode_SetExitDelayTi_In {
            AppSrv_CallerId appsrv_calleridarg;
            ExitDelayTi exitdelaytiarg;
        };

        @nested
        struct VMM_CarMode_EnterCarMode_In {
            AppSrv_CallerId appsrv_callerid;
            CarMode carmode;
        };

        @nested
        struct VMM_PowerMode_setPowerMode_In {
            AppSrv_CallerId appsrv_calleridarg;
            PowerMode powermodearg;
        };

        @nested
        struct BCM_Light_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_BeamMode onoffcmd;
        };

        @nested
        struct BCM_ADBLight_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_PosnLampEnhance_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_Light_BeamPosition_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_Zangle zangle;
        };

        @nested
        struct BCM_Light_WidthGuidance_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_Light_LowBeamEnhance_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_Light_AFS_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_Fog_Light_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_Extrlight_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_CfgPara cfgpara;
        };

        @nested
        struct BCM_PosnLampDispPattern_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_IntelSigDisp_Op intelsigdisp_op;
        };

        @nested
        struct BCM_PosnLampStateDispPattern_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_IntelSigDisp_Op intelsigdisp_op;
        };

        @nested
        struct BCM_BatteryDispPattern_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_IntelSigDisp_Op intelsigdisp_op;
        };

        @nested
        struct BCM_ThanksDisp_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_GraleLampDisp_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_ADASLampDisp_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_IntelSigDisp_Op intelsigdisp_op;
        };

        @nested
        struct BCM_Window_ChildLock_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_LockUnLockPara childlock_op;
            HPCC_DoorID reardoorcmd;
            HPCC_WindowID rearwindowcmd;
        };

        @nested
        struct BCM_CentralLock_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_LockUnLockPara centrallock_op;
        };

        @nested
        struct BCM_DrvLock_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_OnOffCmd onoffcmd;
        };

        @nested
        struct BCM_AwayLock_Ctrl_Enh_In {
            BCM_CallerID bcm_callerid;
            HPCC_LockUnLockPara lockunlockpara;
            HPCC_OnOffCmd onoffcmd;
        };

        @final
        union HPCM_BCM_Service_eSrv switch(int32) {
            // 进入特定的用户模式
            case 1656882926:
                VMM_UsageMode_EnterUsageMode_In VMM_UsageMode_EnterUsageMode;

            // 设置退出延时
            case -920425368:
                VMM_UsageMode_SetExitDelayTi_In VMM_UsageMode_SetExitDelayTi;

            // 进入特定的车辆模式
            case 1822396041:
                VMM_CarMode_EnterCarMode_In VMM_CarMode_EnterCarMode;

            // 设置电源模式
            case 1856911191:
                VMM_PowerMode_setPowerMode_In VMM_PowerMode_setPowerMode;

            // 控制自动大灯、位置灯、近光灯的开启/关闭增强服务
            case 66238767:
                BCM_Light_Ctrl_Enh_In BCM_Light_Ctrl_Enh;

            // 自适应远光灯开启/关闭增强服务
            case 655960075:
                BCM_ADBLight_Ctrl_Enh_In BCM_ADBLight_Ctrl_Enh;

            // 位置等提亮使能增强服务
            case 505398926:
                BCM_PosnLampEnhance_Ctrl_Enh_In BCM_PosnLampEnhance_Ctrl_Enh;

            // 大灯高度手动调节增强服务
            case 207009957:
                BCM_Light_BeamPosition_Ctrl_Enh_In BCM_Light_BeamPosition_Ctrl_Enh;

            // 示宽引导开启/关闭增强服务
            case -763709823:
                BCM_Light_WidthGuidance_Ctrl_Enh_In BCM_Light_WidthGuidance_Ctrl_Enh;

            // 近光增强开启/关闭增强服务
            case -1478517379:
                BCM_Light_LowBeamEnhance_Ctrl_Enh_In BCM_Light_LowBeamEnhance_Ctrl_Enh;

            // 转向辅助照明开启/关闭增强服务
            case -950422755:
                BCM_Light_AFS_Ctrl_Enh_In BCM_Light_AFS_Ctrl_Enh;

            // 后雾灯开启/关闭增强服务
            case -1403693521:
                BCM_Fog_Light_Ctrl_Enh_In BCM_Fog_Light_Ctrl_Enh;

            // 伴我回家生效时间设置增强服务
            case 100069557:
                BCM_Extrlight_Ctrl_Enh_In BCM_Extrlight_Ctrl_Enh;

            // 位置灯个性皮肤样式选择设置
            case -1750758886:
                BCM_PosnLampDispPattern_Ctrl_Enh_In BCM_PosnLampDispPattern_Ctrl_Enh;

            // 位置灯状态表达样式选择
            case 1305593815:
                BCM_PosnLampStateDispPattern_Ctrl_Enh_In BCM_PosnLampStateDispPattern_Ctrl_Enh;

            // 电量显示灯语选择
            case -1694013410:
                BCM_BatteryDispPattern_Ctrl_Enh_In BCM_BatteryDispPattern_Ctrl_Enh;

            // 感谢灯语打开指令
            case -1484169858:
                BCM_ThanksDisp_Ctrl_Enh_In BCM_ThanksDisp_Ctrl_Enh;

            // 炫光提示灯语打开指令
            case 1826448851:
                BCM_GraleLampDisp_Ctrl_Enh_In BCM_GraleLampDisp_Ctrl_Enh;

            // 智驾提示灯语设置开关
            case -986204298:
                BCM_ADASLampDisp_Ctrl_Enh_In BCM_ADASLampDisp_Ctrl_Enh;

            // 一键开启/关闭后排车窗/车门的儿童锁增强服务
            case -898087379:
                BCM_Window_ChildLock_Ctrl_Enh_In BCM_Window_ChildLock_Ctrl_Enh;

            // 一键解闭锁增强服务
            case -1803624607:
                BCM_CentralLock_Ctrl_Enh_In BCM_CentralLock_Ctrl_Enh;

            // 主驾单独解锁增强服务
            case -834664438:
                BCM_DrvLock_Ctrl_Enh_In BCM_DrvLock_Ctrl_Enh;

            // 蓝牙钥匙远离车辆闭锁/靠近车辆解锁增强服务
            case 1417955318:
                BCM_AwayLock_Ctrl_Enh_In BCM_AwayLock_Ctrl_Enh;
        };
    };
};
