/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/service/ZCU_FL_BCM_Service.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/service/ZCU_FL_BCM_Service.idl
*/

#include "rpcCommon.idl"

module Seres {
    module ZCU_FL_BCM {
        typedef uint8 MassageStrength; // 按摩强度

        typedef uint8 MassagMode; // 按摩模式

        typedef uint8 VentilationLevel; // 通风等级

        typedef uint8 HeatingLevel; // 加热等级

        typedef uint8 ElecSideStep_motorLearnCMD; // 设置左侧电动踏板自学习指令

        typedef uint8 BCM_ElecSideStepCmd; // 控制电动踏板

        typedef uint8 AntiGlareenable; // 防眩目使能

        typedef uint8 InsideRearView_power; // 内后视镜电源

        typedef uint8 SunshadeOperation; // 遮阳帘工作

        typedef uint8 SunshadeEnable; // 天窗遮阳帘使能

        typedef uint8 AdjustPosition; // 车窗开度信息

        typedef uint8 RearDefrostSw; // 后除霜加热按键

        typedef uint8 RearView_FoldUnfold; // 后视镜折叠控制

        typedef uint8 Percent; // 百分比

        typedef uint8 LRStepSet; // 左右步进调节

        typedef uint8 UDStepSet; // 上下步进调节

        typedef uint8 Steerwhl_HeatSt; // 设置方向盘加热器运行指令

        typedef uint8 SteerWhl_Height; // 高度

        typedef uint8 SteerWhl_Tilt; // 倾角

        typedef uint8 SteerWhl_ZmotorLearnCMD; // 设置方向盘高度自学习指令

        typedef uint8 SteerWhl_XmotorLearnCMD; // 设置方向盘前后自学习指令

        typedef uint8 BCM_Wpr_PlantM; // 工厂模式设置

        typedef uint8 BCM_Wpr_EN; // 雨刮使能

        typedef uint8 BCM_Block_OverWrite; // 堵转复位请求

        typedef uint8 BCM_Wpr_Posn_Rq; // 特殊位置请求

        typedef uint8 BCM_Wpr_Stop_ImmRq; // 立即停止

        typedef uint8 BCM_Wpr_Splash; // 溅水请求

        typedef uint8 ReturnCode; // 调用返回值

        typedef uint8 BCM_Wpr_Rq; // 刮刷请求

        @nested
        struct BCM_Wiper_F_setBCM_Wpr_Rq_In {
            BCM_Wpr_Rq bcm_wpr_rq;
        };

        @nested
        struct BCM_Wiper_F_setBCM_Wpr_Splash_In {
            BCM_Wpr_Splash bcm_wpr_splash;
        };

        @nested
        struct BCM_Wiper_F_setBCM_Wpr_Stop_ImmRq_In {
            BCM_Wpr_Stop_ImmRq bcm_wpr_stop_immrq;
        };

        @nested
        struct BCM_Wiper_F_setBCM_Wpr_Posn_Rq_In {
            BCM_Wpr_Posn_Rq bcm_wpr_posn_rq;
        };

        @nested
        struct BCM_Wiper_F_setBCM_Block_OverWrite_In {
            BCM_Block_OverWrite bcm_block_overwrite;
        };

        @nested
        struct BCM_Wiper_F_setBCM_Wpr_EN_In {
            BCM_Wpr_EN bcm_wpr_en;
        };

        @nested
        struct BCM_Wiper_F_setWipingPlantMode_In {
            BCM_Wpr_PlantM bcm_wpr_plantm;
        };

        @nested
        struct BCM_SteerWheel_StartAdjustTilt_In {
            uint8 _default;
        };

        @nested
        struct BCM_SteerWheel_StopAdjustTilt_In {
            uint8 _default;
        };

        @nested
        struct BCM_SteerWheel_StartAdjustHeight_In {
            uint8 _default;
        };

        @nested
        struct BCM_SteerWheel_StopAdjustHeight_In {
            uint8 _default;
        };

        @nested
        struct BCM_SteerWheel_SetXmotorLearnCMD_In {
            SteerWhl_XmotorLearnCMD steerwhl_xmotorlearncmd;
        };

        @nested
        struct BCM_SteerWheel_SetZmotorLearnCMD_In {
            SteerWhl_ZmotorLearnCMD steerwhl_zmotorlearncmd;
        };

        @nested
        struct BCM_SteerWheel_AdjustTilt_In {
            SteerWhl_Tilt steerwhl_tilt;
        };

        @nested
        struct BCM_SteerWheel_AdjustHeight_In {
            SteerWhl_Height steerwhl_height;
        };

        @nested
        struct BCM_SteerWhl_Heatr_setHeatCmd_In {
            Steerwhl_HeatSt steerwhl_heatst;
        };

        @nested
        struct BCM_Horn_stop_In {
            uint8 _default;
        };

        @nested
        struct BCM_Horn_start_In {
            uint8 _default;
        };

        @nested
        struct BCM_Horn_alert_In {
            uint8 _default;
        };

        @nested
        struct BCM_Horn_isRunning_In {
            uint8 _default;
        };

        @nested
        struct BCM_RearView_L_LmirrorUDStepSet_In {
            UDStepSet udstepset;
        };

        @nested
        struct BCM_RearView_L_LmirrorLRStepSet_In {
            LRStepSet lrstepset;
        };

        @nested
        struct BCM_RearView_L_LmirrorUDSet_In {
            Percent percent;
        };

        @nested
        struct BCM_RearView_L_LmirrorLRSet_In {
            Percent percent;
        };

        @nested
        struct BCM_RearView_L_FoldUnfold_In {
            RearView_FoldUnfold foldunfold;
        };

        @nested
        struct BCM_RearView_L_RearDefrostSw_In {
            RearDefrostSw reardefrostsw;
        };

        @nested
        struct BCM_Window_FL_adjustPosition_In {
            AdjustPosition adjustposition;
        };

        @nested
        struct BCM_Window_FL_open_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_FL_close_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_FL_stop_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RL_lock_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RL_unlock_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RL_adjustPosition_In {
            AdjustPosition adjustposition;
        };

        @nested
        struct BCM_Window_RL_open_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RL_close_In {
            uint8 _default;
        };

        @nested
        struct BCM_Window_RL_stop_In {
            uint8 _default;
        };

        @nested
        struct BCM_Sunshade_F_setSunshadeEnable_In {
            SunshadeEnable sunshadeenable;
        };

        @nested
        struct BCM_Sunshade_F_setSunshadeOperationSingle_In {
            SunshadeOperation sunshadeoperation;
        };

        @nested
        struct BCM_RearView_L_powersupply_In {
            InsideRearView_power insiderearview_powersupply;
        };

        @nested
        struct BCM_RearView_L_antiGlareenable_In {
            AntiGlareenable antiglareenable;
        };

        @nested
        struct BCM_ElecSideStep_F_In {
            BCM_ElecSideStepCmd bcm_elecsidestepcmd;
        };

        @nested
        struct BCM_ElecSideStep_F_SetmotorLearnCMD_In {
            ElecSideStep_motorLearnCMD elecsidestep_motorlearncmd;
        };

        @nested
        struct BCM_Seat_FL_MainXDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FL_BackRestAngle_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FL_MainZDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FL_frontZDir_In {
            Percent percent;
        };

        @nested
        struct BCM_Seat_FL_HeatingLevel_In {
            HeatingLevel heatinglevel;
        };

        @nested
        struct BCM_Seat_FL_VentilationLevel_In {
            VentilationLevel ventilationlevel;
        };

        @nested
        struct BCM_Seat_FL_MassagMode_In {
            MassagMode massagmode;
        };

        @nested
        struct BCM_Seat_FL_MassageStrength_In {
            MassageStrength massagestrength;
        };

        @final
        union ZCU_FL_BCM_Service switch(int32) {
            // 前雨刮刮刷请求
            case 1839418888:
                BCM_Wiper_F_setBCM_Wpr_Rq_In BCM_Wiper_F_setBCM_Wpr_Rq;

            // 前雨刮溅水请求
            case -906974453:
                BCM_Wiper_F_setBCM_Wpr_Splash_In BCM_Wiper_F_setBCM_Wpr_Splash;

            // 前雨刮立即停止
            case -1550851571:
                BCM_Wiper_F_setBCM_Wpr_Stop_ImmRq_In BCM_Wiper_F_setBCM_Wpr_Stop_ImmRq;

            // 前雨刮特殊位置请求
            case -2020963473:
                BCM_Wiper_F_setBCM_Wpr_Posn_Rq_In BCM_Wiper_F_setBCM_Wpr_Posn_Rq;

            // 前雨刮堵转复位
            case -2045907268:
                BCM_Wiper_F_setBCM_Block_OverWrite_In BCM_Wiper_F_setBCM_Block_OverWrite;

            // 前雨刮雨刮使能
            case -384589331:
                BCM_Wiper_F_setBCM_Wpr_EN_In BCM_Wiper_F_setBCM_Wpr_EN;

            // 前雨刮工厂模式设置
            case 1918435624:
                BCM_Wiper_F_setWipingPlantMode_In BCM_Wiper_F_setWipingPlantMode;

            // 启动方向盘倾斜角度（前后，X）的步进式调节
            case -939830472:
                BCM_SteerWheel_StartAdjustTilt_In BCM_SteerWheel_StartAdjustTilt;

            // 停止方向盘倾斜角度（前后，X）调节
            case -1299303055:
                BCM_SteerWheel_StopAdjustTilt_In BCM_SteerWheel_StopAdjustTilt;

            // 启动方向盘高度的步进式调节
            case 1505618267:
                BCM_SteerWheel_StartAdjustHeight_In BCM_SteerWheel_StartAdjustHeight;

            // 停止方向盘高度调节
            case -356399554:
                BCM_SteerWheel_StopAdjustHeight_In BCM_SteerWheel_StopAdjustHeight;

            // 设置方向盘X前后自学习指令
            case -1320058791:
                BCM_SteerWheel_SetXmotorLearnCMD_In BCM_SteerWheel_SetXmotorLearnCMD;

            // 设置方向盘Z向高度自学习指令
            case -981464377:
                BCM_SteerWheel_SetZmotorLearnCMD_In BCM_SteerWheel_SetZmotorLearnCMD;

            // 调整方向盘倾斜(前后，X)角度
            case -363116799:
                BCM_SteerWheel_AdjustTilt_In BCM_SteerWheel_AdjustTilt;

            // 调整方向盘高度(上下，Z)
            case 1485240791:
                BCM_SteerWheel_AdjustHeight_In BCM_SteerWheel_AdjustHeight;

            // 设置方向盘加热器运行指令
            case -1925986119:
                BCM_SteerWhl_Heatr_setHeatCmd_In BCM_SteerWhl_Heatr_setHeatCmd;

            // 停止鸣笛
            case 209569260:
                BCM_Horn_stop_In BCM_Horn_stop;

            // 启动鸣笛
            case -192093161:
                BCM_Horn_start_In BCM_Horn_start;

            // 启动警示音
            case 433369475:
                BCM_Horn_alert_In BCM_Horn_alert;

            // 返回当前是否正在鸣笛
            case 1691778461:
                BCM_Horn_isRunning_In BCM_Horn_isRunning;

            // 左侧后视镜上下步进调节
            case 1588813800:
                BCM_RearView_L_LmirrorUDStepSet_In BCM_RearView_L_LmirrorUDStepSet;

            // 左侧后视镜左右步进调节
            case -1871713337:
                BCM_RearView_L_LmirrorLRStepSet_In BCM_RearView_L_LmirrorLRStepSet;

            // 左侧后视镜上下调节设置
            case 641868737:
                BCM_RearView_L_LmirrorUDSet_In BCM_RearView_L_LmirrorUDSet;

            // 左侧后视镜左右调节设置
            case -592904512:
                BCM_RearView_L_LmirrorLRSet_In BCM_RearView_L_LmirrorLRSet;

            // 左后视镜折叠控制
            case -800690221:
                BCM_RearView_L_FoldUnfold_In BCM_RearView_L_FoldUnfold;

            // 左后视镜加热按键
            case -1722009428:
                BCM_RearView_L_RearDefrostSw_In BCM_RearView_L_RearDefrostSw;

            // 调整左前车窗的车窗开度
            case 263625435:
                BCM_Window_FL_adjustPosition_In BCM_Window_FL_adjustPosition;

            // 打开左前车窗
            case 25894507:
                BCM_Window_FL_open_In BCM_Window_FL_open;

            // 关闭左前车窗
            case 1023044273:
                BCM_Window_FL_close_In BCM_Window_FL_close;

            // 停止开启或关闭左前车窗
            case 163053597:
                BCM_Window_FL_stop_In BCM_Window_FL_stop;

            // 闭锁左后车窗的窗锁
            case 1518952117:
                BCM_Window_RL_lock_In BCM_Window_RL_lock;

            // 解锁左后车窗的窗锁
            case 65540865:
                BCM_Window_RL_unlock_In BCM_Window_RL_unlock;

            // 调整车窗的左后车窗开度
            case 1832595283:
                BCM_Window_RL_adjustPosition_In BCM_Window_RL_adjustPosition;

            // 打开左后车窗
            case -1423874803:
                BCM_Window_RL_open_In BCM_Window_RL_open;

            // 关闭左后车窗
            case -2019044614:
                BCM_Window_RL_close_In BCM_Window_RL_close;

            // 停止开启或关闭左后车窗
            case -37255143:
                BCM_Window_RL_stop_In BCM_Window_RL_stop;

            // 前天窗遮阳帘使能
            case -1560869453:
                BCM_Sunshade_F_setSunshadeEnable_In BCM_Sunshade_F_setSunshadeEnable;

            // 前遮阳帘工作
            case -1429620810:
                BCM_Sunshade_F_setSunshadeOperationSingle_In BCM_Sunshade_F_setSunshadeOperationSingle;

            // 内后视镜供电
            case -1303732620:
                BCM_RearView_L_powersupply_In BCM_RearView_L_powersupply;

            // 内后视镜防眩目使能
            case -1585128586:
                BCM_RearView_L_antiGlareenable_In BCM_RearView_L_antiGlareenable;

            // 左侧电动踏板
            case 1938137113:
                BCM_ElecSideStep_F_In BCM_ElecSideStep_F;

            // 设置左侧电动踏板自学习指令
            case -1491328217:
                BCM_ElecSideStep_F_SetmotorLearnCMD_In BCM_ElecSideStep_F_SetmotorLearnCMD;

            // 主驾座椅前后位置设置
            case 2134067160:
                BCM_Seat_FL_MainXDir_In BCM_Seat_FL_MainXDir;

            // 主驾座椅靠背角度位置设置
            case 1544091831:
                BCM_Seat_FL_BackRestAngle_In BCM_Seat_FL_BackRestAngle;

            // 主驾座椅座垫高度位置设置
            case 1852215730:
                BCM_Seat_FL_MainZDir_In BCM_Seat_FL_MainZDir;

            // 主驾座椅座垫倾斜位置设置
            case 1469297166:
                BCM_Seat_FL_frontZDir_In BCM_Seat_FL_frontZDir;

            // 主驾座椅加热功能请求
            case 844982472:
                BCM_Seat_FL_HeatingLevel_In BCM_Seat_FL_HeatingLevel;

            // 主驾座椅通风功能请求
            case -121413492:
                BCM_Seat_FL_VentilationLevel_In BCM_Seat_FL_VentilationLevel;

            // 主驾座椅按摩模式请求
            case 1412285804:
                BCM_Seat_FL_MassagMode_In BCM_Seat_FL_MassagMode;

            // 主驾座椅按摩强度请求
            case -476836111:
                BCM_Seat_FL_MassageStrength_In BCM_Seat_FL_MassageStrength;
        };
    };
};
