/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_FR_HPCC_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_FR_HPCC_Status.idl
*/

module Seres {
    module ZCU_FR_HPCC {

        typedef uint8 WinSwtntfFltSt; // 开关故障状态

        typedef uint8 WinSwtntfSt; // 车窗升降开关指令

        typedef uint8 HMI_KeySwtMode; // 按键开关指令

        enum notifyLockStatus { // 车窗锁的状态
            @value(0x0) BCM_LockStatus_Window_LOCKED,
            @value(0x1) BCM_LockStatus_Window_UNLOCKED,
            @value(0xFF) BCM_LockStatus_Window_UNKNOWN
        };

        typedef uint8 WinnotifyPosition; // 车窗开度

        enum WinnotifyActuateStatus { // 车窗执行状态
            @value(0x0) BCM_ActuateStatus_IDLE,
            @value(0x1) BCM_ActuateStatus_CLOSING_BACKWARD_FOLDING_DOWNWARD_DECREASING,
            @value(0x2) BCM_ActuateStatus_OPENING_FORWARD_UNFOLDING_UPWARD_INCREASING,
            @value(0x3) BCM_ActuateStatus_STALL,
            @value(0x4) BCM_ActuateStatus_IDLE_MOTOR_FAULT,
            @value(0x5) BCM_ActuateStatus_IDLE_ANTIPINCH_RESTRAIN,
            @value(0x6) BCM_ActuateStatus_IDLE_HEAT_PROTECTION,
            @value(0xFF) BCM_ActuateStatus_UNKNOWN
        };

        typedef boolean WinnotifyAntiPinch; // 车窗是否防夹

        enum WinnotifyLearnedStatus { // 车窗学习状态
            @value(0x0) BCM_LearnedStatus_UNFINISHED,
            @value(0x1) BCM_LearnedStatus_FINISHED,
            @value(0xFF) BCM_LearnedStatus_UNKNOWN
        };

        typedef uint8 BCM_Legacy_WCGRmnSt; // 无线充电器功能状态

        typedef uint8 BCM_Legacy_WCGFaultSt; // 无线充电器故障状态

        typedef uint8 BCM_Legacy_WCGChargingSt; // 无线充电器充电状态

        typedef uint8 BCM_Legacy_WCGWorkSt; // 无线充电器工作状态

        typedef uint8 BCM_Legacy_WCGId; // 无线充电器标识

        @nested
        struct BCM_Legacy_WCGStatusInstance { // 无线充电器状态实例
            BCM_Legacy_WCGId bcm_legacy_wcgid; // 无线充电器标识
            BCM_Legacy_WCGWorkSt bcm_legacy_wcgworkst; // 无线充电器工作状态
            BCM_Legacy_WCGChargingSt bcm_legacy_wcgchargingst; // 无线充电器充电状态
            BCM_Legacy_WCGFaultSt bcm_legacy_wcgfaultst; // 无线充电器故障状态
            BCM_Legacy_WCGRmnSt bcm_legacy_wcgrmnst; // 无线充电器功能状态
        };

        typedef sequence<BCM_Legacy_WCGStatusInstance, 2> BCM_Legacy_WCGStatus; // 无线充电器状态

        typedef uint8 RearDefrostStatus; // 后除霜加热状态

        typedef uint8 FoldUnfoldStatus; // 后视镜折叠状态

        typedef uint8 Percent; // 百分比

        typedef uint8 LeRiAdjSwtMode; // 外后视镜左右调节开关状态

        typedef uint8 UpDwnAdjSwtMode; // 外后视镜上下调节开关状态

        typedef boolean sprayingStatus; // 喷水状态

        typedef uint8 WiperID; // 雨刮控制ID

        @nested
        struct Wiper_sprayingStatus { // 雨刮清洗状态
            WiperID wiperid; // 雨刮ID
            sprayingStatus sprayingstatus; // 清洗状态
        };

        typedef uint8 CHBModeSet; // 车载冷暖箱模式设置

        typedef uint8 CHBDoorOpen; // 车载冷暖箱电动门弹开指令

        typedef uint8 BCM_CHBHeatFltSt; // 车载冷暖箱制热系统故障状态（压缩机）

        typedef uint8 BCM_CHBCoolingFltSt; // 车载冷暖箱制冷系统故障状态（压缩机）

        typedef uint8 BCM_CHBLightFltSt; // 车载冷暖箱LED照明灯故障状态

        typedef uint8 BCM_CHBFanFltSt; // 车载冷暖箱冷却风扇故障状态

        typedef uint8 BCM_CHBTemSensorFltSt; // 车载冷暖箱温度传感器故障状态

        typedef uint8 BCM_CHBTecFltSt; // 车载冷暖箱半导体制冷器故障状态

        typedef uint8 BCM_CHBVoltageFltSt; // 车载冷暖箱供电电压故障状态

        @nested
        struct BCM_CHBFltSt { // 车载冷暖箱故障状态
            BCM_CHBVoltageFltSt bcm_chbvoltagefltst; // 供电电压状态
            BCM_CHBTecFltSt bcm_chbtecfltst; // 半导体制冷器工作状态
            BCM_CHBTemSensorFltSt bcm_chbtemsensorfltst; // 温度传感器工作状态
            BCM_CHBFanFltSt bcm_chbfanfltst; // 冷却风扇工作状态
            BCM_CHBLightFltSt bcm_chblightfltst; // LED照明灯工作状态
            BCM_CHBCoolingFltSt bcm_chbcoolingfltst; // 制冷系统故障状态（压缩机）
            BCM_CHBHeatFltSt bcm_chbheatfltst; // 制热系统故障状态（压缩机）
        };

        typedef uint8 BCM_CHBOffOfSocLowInd; // 电池电量低冷暖箱功能关闭提醒

        typedef uint8 BCM_CHBDoorUnlockWarn; // 车载冷暖箱门未关闭超时提醒

        typedef uint8 BCM_CHBOnOffSt; // 车载冷暖箱开关指令

        typedef uint8 BCM_CHBDoorSt; // 车载冷暖箱门开闭状态

        typedef int8 BCM_CHBTempReqSt; // 车载冷暖箱记忆的上次用户温度设定值

        typedef uint8 BCM_CHBTempModeSt; // 车载冷暖箱当前温控模式状态

        @nested
        struct BCM_CHBWorkSt { // 车载冷暖箱运行状态
            BCM_CHBTempModeSt bcm_chbtempmodest; // 车载冷暖箱当前温控模式状态
            BCM_CHBTempReqSt bcm_chbtempreqst; // 冷暖箱记忆的上次用户温度设定值
            BCM_CHBDoorUnlockWarn bcm_chbdoorunlockwarnind; // 车载冷暖箱门未关闭超时提醒
            BCM_CHBDoorSt bcm_chbdoorst; // 车载冷暖箱门开闭状态
            BCM_CHBOnOffSt bcm_chbonoffstatus; // 车载冷暖箱功能开关状态
            BCM_CHBDoorUnlockWarn bcm_chbdooropenind; // 车载冷暖箱门超时未关闭提醒
            BCM_CHBOffOfSocLowInd bcm_chboffofsoclowind; // 电池电量低冷暖箱功能关闭提醒
        };

        typedef uint8 BCM_Legacy_E2E_Result; // E2E校验结果

        typedef uint8 RLSWindowAutoModActvFb; // 天窗/门窗自动关闭模式激活反馈

        typedef uint8 RLSWindowCtrlReq; // 车窗控制请求

        typedef uint32 RLSHUDBrightnessReq; // HUD亮度请求

        typedef uint8 RLSLampSwitchReason; // 近光灯控制请求原因

        typedef uint8 RLSLowBeamWorkModeReq; // 近光灯控制请求

        typedef uint8 RLSPosnLiModeReq; // 位置灯控制请求

        @nested
        struct RLSBeamLampCtrlReq { // 灯光控制请求
            RLSPosnLiModeReq rlsposnlimodereq; // 位置灯控制请求
            RLSLowBeamWorkModeReq rlslowbeamworkmodereq; // 近光灯控制请求
            RLSLampSwitchReason rlslampswitchreason; // 近光灯控制请求原因
        };

        typedef uint8 RLSSplashReq; // 溅水刮刷开启请求

        typedef uint8 RLSWiperWorkModeReq; // 雨刮工作模式请求

        @nested
        struct RLSWiperCtrlReq { // 雨刮控制请求
            RLSWiperWorkModeReq rlswiperworkmodereq; // 雨刮工作模式请求
            RLSSplashReq rlssplashreq; // 溅水刮刷开启请求
        };

        typedef uint8 RLSRespErr; // 雨量传感器响应错误

        typedef uint8 RLSRainFlt; // 雨量传感器工作状态

        typedef uint8 RLSHumTempFlt; // 温湿度传感器故障

        typedef uint8 RLSOverVoltFlag; // 过压故障标志

        typedef uint8 RLSUnderVoltFlag; // 欠压故障标志

        typedef uint8 RLSOpticalSnsrFltFlag; // 光线传感器故障标志

        @nested
        struct RLSFltSt { // 阳光雨量传感器故障状态
            RLSOpticalSnsrFltFlag rlsopticalsnsrfltflag; // 光线传感器故障标志
            RLSUnderVoltFlag rlsundervoltflag; // 欠压故障标志
            RLSOverVoltFlag rlsovervoltflag; // 过压故障标志
            RLSHumTempFlt rlshumtempflt; // 温湿度传感器故障
            RLSRainFlt rlsrainflt; // 雨量传感器故障
            RLSRespErr rlsresperr; // 雨量传感器响应错误
        };

        typedef uint16 InfraredBrightness; // 红外光值

        typedef uint16 AmbientBrightness; // 车顶环境光值

        typedef uint16 ForwardBrightness; // 前向光值

        typedef uint8 RainFallInfo; // 雨量信息

        typedef int16 TempDewPoint; // 露点温度

        typedef uint16 SolarSensorValue; // 太阳辐射强度

        typedef int16 TempSensorValue; // 玻璃温度

        typedef uint16 HumidSensorValue; // 玻璃湿度

        @nested
        struct RLSEnvInfo { // 阳光雨量信息
            HumidSensorValue windshieldhum; // 玻璃湿度
            TempSensorValue windshieldtemp; // 玻璃温度
            SolarSensorValue solarrightsens; // 右侧太阳辐射强度
            SolarSensorValue solarleftsens; // 左侧太阳辐射强度
            TempDewPoint tempdewpoint; // 露点温度
            RainFallInfo rainfallinfo; // 雨量信息
            ForwardBrightness forwardbrightness; // 前向光值
            AmbientBrightness ambientbrightness; // 车顶环境光值
            InfraredBrightness infraredbrightness; // 红外光值
        };

        typedef uint8 HMI_FaultStatus; // 开关故障状态

        typedef uint8 HMI_PressedStatus; // 指示按键被按压的状态，能识别出当前按键是否被按下，至于按键被按压的时长在原子服务中不体现，上层应用根据原子服务上报的按压状态，确定按压时长决定不同的处理逻辑。

        typedef uint8 ChrgFltSt; // 故障状态

        typedef uint8 chrgPortMotFltSt; // 充电口盖电机故障

        typedef uint8 BCM_ChrgPortFolwClsReq; // 跟随关闭请求

        typedef uint8 BCM_ChrgPortLearnedStatus; // 自学习状态

        typedef uint8 BCM_ChrgIceBreakingSt; // 破冰状态

        typedef uint8 BCM_ChrgAntiPinchSt; // 防夹状态

        typedef uint8 BCM_ChrgPosnSt; // 充电口盖位置

        typedef uint8 BCM_ChrgDirSt; // 充电口盖运行状态

        typedef uint8 BCM_charPortID; // 充电口盖标识ID

        @nested
        struct BCM_ChrgPortDoorSt { // 充电口盖状态
            BCM_charPortID id; // 充电口盖标识ID
            BCM_ChrgDirSt chrgdirst; // 充电口盖运行状态
            BCM_ChrgPosnSt chrgposnst; // 充电口盖位置
            BCM_ChrgAntiPinchSt chrgantipinchst; // 充电口盖防夹状态
            BCM_ChrgIceBreakingSt chrgicebreakingst; // 充电口盖破冰状态
            BCM_ChrgPortLearnedStatus chrgportlearnedstatus; // 充电口盖自学习状态
            BCM_ChrgPortFolwClsReq chrgportfolwclsreq; // 充电口盖跟随关闭请求
            chrgPortMotFltSt chrgportmotfltst; // 充电口盖故障状态
        };

        enum AQSAirquality { // AQS信息
            @value(0x0) Air_Quality_Level_0,
            @value(0x1) Air_Quality_Level_10,
            @value(0x2) Air_Quality_Level_20,
            @value(0x3) Air_Quality_Level_30,
            @value(0x4) Air_Quality_Level_40,
            @value(0x5) Air_Quality_Level_50,
            @value(0x6) Air_Quality_Level_60,
            @value(0x7) Air_Quality_Level_70,
            @value(0x8) Air_Quality_Level_80,
            @value(0x9) Air_Quality_Level_90,
            @value(0xA) Air_Quality_Level_100,
            @value(0xD) not_mounted,
            @value(0xFE) Initial,
            @value(0xFF) Invalid
        };

        typedef uint16 InsideOutsidePM2_5; // 车内车外P2.5信息

        typedef uint8 UVCswitch; // 设置UVC开关

        typedef uint8 IONswitch; // 设置负离子开关

        typedef float TMS_Temperature; // TMS温度信息

        typedef uint8 TMS_DoorPosn; // TMS电机位置

        typedef uint8 ACoutletId; // 出风口编号

        @nested
        struct ACoutletTempvalue { // 出风口温度值
            ACoutletId acoutletid; // 出风口编号
            TMS_Temperature acoutlettemp; // 出风口温度
        };

        typedef sequence<ACoutletTempvalue, 30> ACoutletTempSt; // 出风口温度状态

        enum ACZoneId { // 空调温区编号
            @value(0x0) ACZONE_FRONT_LEFT,
            @value(0x1) ACZONE_FRONT_RIGH,
            @value(0x2) ACZONE_REAR_LEFT,
            @value(0x3) ACZONE_REAR_RIGHT,
            @value(0x4) ACZONE_THIRD_LEFT,
            @value(0x5) ACZONE_THIRD_RIGHT,
            @value(0xFF) ACZoneId_INVALID
        };

        @nested
        struct TMS_TempDoorPosnValue { // 温区风门电机位置状态
            ACZoneId zoneid; // 温区编号
            TMS_DoorPosn tms_tempdoorposn; // 温区风门电机位置
        };

        typedef sequence<TMS_TempDoorPosnValue, 20> TMS_TempDoorPosnst; // 温区风门电机位置状态

        typedef uint8 TMS_CyclingMode; // 通风循环模式电机位置

        @nested
        struct TMS_AirMode { // 空调出风模式
            TMS_DoorPosn face; // 吹面电机
            TMS_DoorPosn foot; // 吹脚电机
            TMS_DoorPosn defrost; // 除霜电机
        };

        @nested
        struct TMS_AirModeValue { // 温区吹风模式信息
            ACZoneId zoneid; // 温区编号
            TMS_AirMode tms_airmode; // 空调出风模式
        };

        typedef sequence<TMS_AirModeValue, 20> TMS_AirModeSt; // 温区吹风模式信息

        typedef uint8 ACBlwLevel; // 风速档位

        @nested
        struct TMS_WindLevelValue { // 温区风速档位值
            ACZoneId zoneid; // 温区
            ACBlwLevel level; // 风量
        };

        typedef sequence<TMS_WindLevelValue, 20> TMS_WindLevelSt; // 温区风速档位信息

        @final
        struct ZCU_FR_HPCC_Status{
            // 上报温区的风速档位
            @optional TMS_WindLevelSt TMS_VentilationSt_notifyLevel_ZCUFR;

            // 上报温区的吹风模式
            @optional TMS_AirModeSt TMS_VentilationSt_notifyMode_ZCUFR;

            // 上报通风循环工作模式
            @optional TMS_CyclingMode TMS_VentilationSt_notifyCyclingMode_ZCUFR;

            // 上报双层流电机循环电机位置
            @optional TMS_CyclingMode TMS_VentilationSt_notifyDouble_layerCycling_ZCUFR;

            // 上报温区风门电机位置
            @optional TMS_TempDoorPosnst TMS_VentilationSt_notifyTemp_ZCUFR;

            // 上报出风口温度
            @optional ACoutletTempSt TMS_VentilationSt_notifyAirOutletTemp_ZCUFR;

            // 上报除霜优先风门电机位置
            @optional TMS_DoorPosn TMS_VentilationSt_notifyDefostPriority_ZCUFR;

            // 上报前蒸发器温度
            @optional TMS_Temperature TMS_VentilationSt_notifyFEvpTemp_ZCUFR;

            // 上报ION状态
            @optional IONswitch TMS_EnvMonitorst_notifyIONswitch;

            // 上报UVC状态
            @optional UVCswitch TMS_EnvMonitorst_notifyUVCswitch;

            // 上报车内PM2.5状态
            @optional InsideOutsidePM2_5 TMS_EnvMonitorst_notifyInsidePM2_5;

            // 上报车外PM2.5状态
            @optional InsideOutsidePM2_5 TMS_EnvMonitorst_notifyOutsidePM2_5;

            // 上报AQS信息
            @optional AQSAirquality TMS_EnvMonitorst_notifyAQSAirquality;

            // 上报充电口盖位置
            @optional BCM_ChrgPosnSt BCM_ChargePortDoor_ntfchargePortPositionStatus;

            // 上报充电口盖状态
            @optional BCM_ChrgPortDoorSt BCM_ChargePortDoor_ntfchargePortStatus;

            // 上报充电口盖故障状态
            @optional ChrgFltSt BCM_ChargePortDoor_chrgPortMotFltSt;

            // 上报开关按压状态
            @optional HMI_PressedStatus HMI_ChargePortSwt_notifyMode;

            // 上报故障状态
            @optional HMI_FaultStatus HMI_ChargePortSwt_notifyFaultStatus;

            // 查询湿度、温度、太阳辐射强度、光强、遮挡程度
            @optional RLSEnvInfo BCM_RLS_Info_notifyRLSEnvInfo;

            // 上报阳光雨量传感器故障状态
            @optional RLSFltSt BCM_RLS_Info_notifyRLSFltSt;

            // 雨刮工作模式请求
            @optional RLSWiperCtrlReq BCM_RLS_Info_notifyRLSWiperCtrlReq;

            // 灯光控制请求
            @optional RLSBeamLampCtrlReq BCM_RLS_Info_notifyRLSBeamLampCtrlReq;

            // HUD亮度请求
            @optional RLSHUDBrightnessReq BCM_RLS_Info_notifyRLSHUDBrightnessReq;

            // 车窗控制请求
            @optional RLSWindowCtrlReq BCM_RLS_Info_notifyRLSWindowCtrlReq;

            // 上报天窗/门窗自动关闭模式激活状态
            @optional RLSWindowAutoModActvFb BCM_RLS_Info_notifyRLSWindowAutoModActvFb;

            // 上报E2E校验
            @optional BCM_Legacy_E2E_Result BCM_RLS_Info_ntfRLSE2E;

            // 车载冷暖箱运行状态
            @optional BCM_CHBWorkSt BCM_CoolgHeatgBox_notifyCHBWorkSt;

            // 车载冷暖箱故障状态
            @optional BCM_CHBFltSt BCM_CoolgHeatgBox_notifyCHBFaultSt;

            // 上报RAC接受到的车载冷暖箱电动门弹开运行指令状态
            @optional CHBDoorOpen BCM_CoolgHeatgBox_RAC_notifyCHBOpenSwtSt;

            // 上报RAC接受到的车载冷暖箱模式设置请求
            @optional CHBModeSet BCM_CoolgHeatgBox_RAC_notifyCHBModSet;

            // 上报清洗的开启状态
            @optional Wiper_sprayingStatus BCM_WiperWash_notifySprayingStatus;

            // 右侧滚轮上下调节状态
            @optional UpDwnAdjSwtMode HMI_ExtrMirrSwt_RiRollrUpDwnAdjSt;

            // 右侧滚轮左右调节状态
            @optional LeRiAdjSwtMode HMI_ExtrMirrSwt_RiRollrLeRiAdjSt;

            // 右侧后视镜上下调节位置
            @optional Percent BCM_RearView_R_RmirrorUDSetFB;

            // 右侧后视镜左右调节位置
            @optional Percent BCM_RearView_R_RmirrorLRSetFB;

            // 右后视镜折叠状态
            @optional FoldUnfoldStatus BCM_RearView_R_FoldUnfoldStatus;

            // 右后视镜加热状态
            @optional RearDefrostStatus BCM_RearView_R_RearDefrostStatus;

            // 上报无线充电器状态
            @optional BCM_Legacy_WCGStatus BCM_WCM_LegacyECU_BCM_Legacy_WCGStatus;

            // 上报右前车窗学习状态
            @optional WinnotifyLearnedStatus BCM_Window_FR_LearnedStatus;

            // 上报右前车窗防夹状态
            @optional WinnotifyAntiPinch BCM_Window_FR_notifyAntiPinch;

            // 上报右前车窗执行状态
            @optional WinnotifyActuateStatus BCM_Window_FR_notifyActuateStatus;

            // 上报右前车窗开度
            @optional WinnotifyPosition BCM_Window_FR_notifyPosition;

            // 上报右后车窗的锁状态
            @optional notifyLockStatus BCM_Window_RR_notifyLockStatus;

            // 上报右后车窗学习状态
            @optional WinnotifyLearnedStatus BCM_Window_RR_LearnedStatus;

            // 上报右后车窗防夹状态
            @optional WinnotifyAntiPinch BCM_Window_RR_notifyAntiPinch;

            // 上报右后车窗执行状态
            @optional WinnotifyActuateStatus BCM_Window_RR_notifyActuateStatus;

            // 上报右后车窗开度
            @optional WinnotifyPosition BCM_Window_RR_notifyPosition;

            // 上报右前电动门内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorInsd_FR_ntfSt;

            // 上报右前电动门内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorInsd_FR_ntfFltSt;

            // 上报又后电动门内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorInsd_RR_ntfSt;

            // 上报又后电动门内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorInsd_RR_ntfFltSt;

            // 上报右前门把手外开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutsd_Outer_FR_ntfSt;

            // 上报右前门把手外开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutsd_Outer_FR_ntfFltSt;

            // 上报右后门把手外开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutsd_Outer_RR_ntfSt;

            // 上报右后门把手外开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutsd_Outer_RR_ntfFltSt;

            // 上报右前门把手内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutd_FR_ntfSt;

            // 上报右前门把手内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutd_FR_ntfFltSt;

            // 上报右后门把手内开关状态
            @optional HMI_KeySwtMode HMI_KeySwt_DoorOutd_RR_ntfSt;

            // 上报右后门把手内开关故障状态
            @optional HMI_FaultStatus HMI_KeySwt_DoorOutd_RR_ntfFltSt;

            // 上报右前车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_FR_ntfSt;

            // 上报右前车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_FR_ntfFltSt;

            // 上报右后车窗升降开关状态
            @optional WinSwtntfSt HMI_WinSwt_RR_ntfSt;

            // 上报右后车窗开关故障状态
            @optional WinSwtntfFltSt HMI_WinSwt_RR_ntfFltSt;

        };
    };
};
