/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/HPCM_BCM_Enh_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/HPCM_BCM_Enh_Status.idl
*/

module Seres {
    module HPCM_BCM_Enh {

        enum PowerModeErrorFlag { // 电源模式故障状态
            @value(0x0) POWERMODEERRORFLAG_FALSE,
            @value(0x1) POWERMODEERRORFLAG_TRUE,
            @value(0x2) POWERMODEERRORFLAG_RESERVED,
            @value(0x3) POWERMODEERRORFLAG_INVALID
        };

        enum PowerMode { // 电源模式
            @value(0x0) POWERMODE_OFF,
            @value(0x1) POWERMODE_ON,
            @value(0x2) POWERMODE_RESERVED,
            @value(0x3) POWERMODE_INVALID
        };

        @nested
        struct PowerModeStatus { // 电源模式状态
            PowerMode powermode; // 电源模式
            PowerModeErrorFlag powermodeerrorflag; // 电源模式故障状态
        };

        typedef uint8 HPCC_LockUnLock; // 解闭锁状态

        enum UsageMode { // 用户模式
            @value(0x0) USAGEMODE_ABANDONED,
            @value(0x1) USAGEMODE_INACTIVE,
            @value(0x2) USAGEMODE_CONVENIENCE,
            @value(0x3) USAGEMODE_DRIVING,
            @value(0x4) USAGEMODE_OTAUPDATING,
            @value(0x5) USAGEMODE_REMOTE,
            @value(0x6) USAGEMODE_REMOTEDRIVING,
            @value(0x7) USAGEMODE_RESERVED,
            @value(0xF) USAGEMODE_INVALID
        };

        @nested
        struct UsageModeStatus { // 用户模式状态
            UsageMode usagemode; // 用户模式
        };

        typedef uint8 CarMode; // 整车模式

        typedef uint8 HPCM_LampFltSts; // 外灯故障状态

        typedef uint8 HPCC_IntelSigDisp_Op; // 位置灯个性皮肤设置

        typedef uint8 HPCC_CfgPara; // 伴我回家设置

        typedef uint8 HPCC_Zangle; // 大灯高度

        typedef uint8 HPCC_OnOffCmd; // 开关设置

        typedef uint8 HPCC_BeamModeStatus; // 灯光模式

        @final
        struct HPCM_BCM_Enh_Status{
            // 大灯状态
            @optional HPCC_BeamModeStatus beammodests;

            // 位置灯提亮使能状态
            @optional HPCC_OnOffCmd posnlampenhancests;

            // 自适应远光灯状态
            @optional HPCC_OnOffCmd adbenablests;

            // 大灯高度挡位状态
            @optional HPCC_Zangle beampositionsts;

            // 示宽引导状态
            @optional HPCC_OnOffCmd widthguidancests;

            // 近光增强状态
            @optional HPCC_OnOffCmd lowbeamenhancests;

            // 转向辅助照明状态
            @optional HPCC_OnOffCmd afssts;

            // 后雾灯开启状态
            @optional HPCC_OnOffCmd foglightsts;

            // 伴我回家状态
            @optional HPCC_CfgPara Extrlightsts;

            // 位置灯个性皮肤
            @optional HPCC_IntelSigDisp_Op posnlampintelsigdisp;

            // 位置灯状态表达样式选择
            @optional HPCC_IntelSigDisp_Op posnlampstatests;

            // 电量显示灯语选择设置
            @optional HPCC_IntelSigDisp_Op batterydispintelsts;

            // 感谢灯语打开指令
            @optional HPCC_OnOffCmd thanksdispsts;

            // 炫光提示灯语打开指令
            @optional HPCC_OnOffCmd glaretipsdispsts;

            // 智驾提示灯语设置开关
            @optional HPCC_IntelSigDisp_Op adasdispintelsts;

            // 位置灯故障状态
            @optional HPCM_LampFltSts posnlampfltsts;

            // 近光灯故障状态
            @optional HPCM_LampFltSts lowbeamfltsts;

            // 远光灯故障状态
            @optional HPCM_LampFltSts hibeamfltsts;

            // 自适应远光灯故障状态
            @optional HPCM_LampFltSts adpvhibeamfltsts;

            // 自动大灯故障状态
            @optional HPCM_LampFltSts autoheadlampfltsts;

            // 转向灯故障状态
            @optional HPCM_LampFltSts turnlampfltsts;

            // 后雾灯故障状态
            @optional HPCM_LampFltSts rearfoglampfltsts;

            // 倒车灯故障状态
            @optional HPCM_LampFltSts rvslampfltsts;

            // 制动灯故障状态
            @optional HPCM_LampFltSts brklampfltsts;

            // ALS故障状态
            @optional HPCM_LampFltSts alsfltsts;

            // AFS故障状态
            @optional HPCM_LampFltSts afsfltsts;

            // 智能交互灯故障状态
            @optional HPCM_LampFltSts isdfltsts;

            // 上报当前车辆模式
            @optional CarMode VMM_CarMode_notifycarModeStatus;

            // 上报当前用户模式状态
            @optional UsageModeStatus VMM_UsageMode_notifyUsageModeStatus;

            // 中控锁状态
            @optional HPCC_LockUnLock centralocksts;

            // 上报电源模式状态
            @optional PowerModeStatus VMM_PowerMode_notifyPowerModeStatus;

        };
    };
};
