/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/ZCU_FR_HPCM_Control_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/ZCU_FR_HPCM_Control_Status.idl
*/

module Seres {
    module ZCU_FR_HPCM_Control {

        typedef uint8 BCM_lockStatus; // 锁的状态

        typedef uint8 BCM_OpenStatus; // 车门开启状态

        typedef uint8 BCM_ChildlockStatus; // 儿童锁状态

        typedef uint8 ALSFltReporting; // 近光灯高度调节故障状态

        typedef uint8 AFSFltReporting; // AFS故障状态

        typedef uint8 AFSOnStReporting; // AFS点亮状态

        typedef uint8 AFSFctStReporting; // AFS功能设置状态

        typedef uint8 AdpvHiBeamFltStReporting; // 自适应远光灯功能故障状态

        typedef uint8 AdpvHiBeamFctStReporting; // 自适应远光灯功能开启状态

        typedef uint8 LampFlow; // 流水状态

        typedef uint8 NtfFltSt; // 灯故障状态

        typedef uint8 DutyRat; // 亮度控制占空比

        typedef uint8 LampCmd; // 灯光控制

        @nested
        struct NtfList { // 车灯状态
            LampCmd bemalightstatus; // 车灯开关状态
            DutyRat brightnessstatus; // 车灯亮度
        };

        @nested
        struct TurnLampStatusReporting { // 转向灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
            LampFlow lampflow; // 流水状态
        };

        @nested
        struct GeneralLampStatusReporting { // 通用灯光状态上报
            NtfList ntflist; // 车灯状态
            NtfFltSt ntffltst; // 故障状态
        };

        @final
        struct ZCU_FR_HPCM_Control_Status{
            // 上报右前位置灯状态
            @optional GeneralLampStatusReporting BCM_PosnLamp_FR_notify;

            // 上报右前转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_FR_notify;

            // 上报右侧转向灯状态
            @optional TurnLampStatusReporting BCM_TurnLamp_RS_notify;

            // 上报右近光灯状态
            @optional GeneralLampStatusReporting BCM_LoBeam_FR_notify;

            // 上报右远光灯状态
            @optional GeneralLampStatusReporting BCM_HiBeam_FR_notify;

            // 上报右自适应远光灯功能开启状态
            @optional AdpvHiBeamFctStReporting BCM_AdpvHiBeamFctSt_FR_notify;

            // 上报右自适应远光灯功能故障状态
            @optional AdpvHiBeamFltStReporting BCM_AdpvHiBeamFltSt_FR_notify;

            // 上报右自适应近光功能设置状态
            @optional AFSFctStReporting BCM_AFSFctSt_FR_notify;

            // 上报右自适应近光打开状态
            @optional AFSOnStReporting BCM_AFSOnSt_FR_notify;

            // 上报右自适应近光故障状态
            @optional AFSFltReporting BCM_AFSFlt_FR_notify;

            // 上报近光灯高度调节故障状态
            @optional ALSFltReporting BCM_ALSFlt_FR_notify;

            // 上报右后儿童锁解闭锁状态
            @optional BCM_ChildlockStatus BCM_Door_RR_notifyChildLockUnlockStatus;

            // 上报右前门开关状态
            @optional BCM_OpenStatus BCM_Door_FR_notifyOpenStatus;

            // 上报右后门开关状态
            @optional BCM_OpenStatus BCM_Door_RR_notifyOpenStatus;

            // 上报右前门解闭锁状态
            @optional BCM_lockStatus BCM_Door_FR_notifyLockUnlockStatus;

            // 上报右后门解闭锁状态
            @optional BCM_lockStatus BCM_Door_RR_notifyLockUnlockStatus;

        };
    };
};
