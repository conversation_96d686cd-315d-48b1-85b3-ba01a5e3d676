/**
* SOA service or topic generate idl
* Auto Generate file, Do not modify manually !!
* Generate date: 2025-06-11 17:30:22.663506
* Version       : v3.1.1
* Change Log    : 新增数据类型定义notifySunshadeFltSt
* Change Author : 张桂文
* Change Date   : 2025-06-11 00:00:00
* Generate PubSub command : idlc -Wno-implicit-extensibility -Wno-enum-consecutive ./output/IDL/topic/HPCM_BCM_HPCC_Status.idl
* Generate RPC command    : idlc -Wno-implicit-extensibility -Wno-enum-consecutive -r <client|server> ./output/IDL/topic/HPCM_BCM_HPCC_Status.idl
*/

module Seres {
    module HPCM_BCM_HPCC {

        typedef uint8 CarMode; // 整车模式

        enum UsageMode { // 用户模式
            @value(0x0) USAGEMODE_ABANDONED,
            @value(0x1) USAGEMODE_INACTIVE,
            @value(0x2) USAGEMODE_CONVENIENCE,
            @value(0x3) USAGEMODE_DRIVING,
            @value(0x4) USAGEMODE_OTAUPDATING,
            @value(0x5) USAGEMODE_REMOTE,
            @value(0x6) USAGEMODE_REMOTEDRIVING,
            @value(0x7) USAGEMODE_RESERVED,
            @value(0xF) USAGEMODE_INVALID
        };

        @nested
        struct UsageModeStatus { // 用户模式状态
            UsageMode usagemode; // 用户模式
        };

        @final
        struct HPCM_BCM_HPCC_Status{
            // 上报当前用户模式状态
            @optional UsageModeStatus VMM_UsageMode_notifyUsageModeStatus;

            // 上报当前车辆模式
            @optional CarMode VMM_CarMode_notifycarModeStatus;

        };
    };
};
