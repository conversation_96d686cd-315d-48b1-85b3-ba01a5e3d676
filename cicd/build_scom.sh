#!/bin/bash

set -e

# 默认参数值
jobs=1
yocto_flag=false
android_flag=false
help_flag=false
clean_flag=false
x86_flag=false
install_prefix=`pwd`/output
cyclonedds_home=../cyclonedds
cycloneddscxx_home=../cyclonedds-cxx
seres_crpc_home=../SERES_C_RPC
seres_cxxrpc_home=../SERES_CPP_RPC
shell_path=`pwd`

# 使用 getopt 处理长参数和短参数
options=$(getopt -o j:xyhac -l jobs:,x86,yocto,android,help -- "$@")
if [ $? != 0 ]; then
    echo "getopt error"
    exit 1
fi

eval set -- "$options"

# 解析命令行参数
while true; do
    case "$1" in
        -j | --jobs)
            jobs=$2
            shift 2
            ;;
        -y | --yocto)
            yocto_flag=true
            shift
            ;;
        -a | --android)
            android_flag=true
            shift
            ;;
        -x | --x86)
            x86_flag=true
            shift
            ;;
        -h | --help)
            help_flag=true
            shift
            ;;
        -c)
            clean_flag=true
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            help_flag=true
            ;;
    esac
done

# ---------- 函数块 ---------

show_helps() {
    echo "Usage: $0 [-j <jobs>] [--yocto] [-h] [--android]"
    echo "  -j, --jobs <jobs>       Number of jobs to run in parallel (default: 1)"
    echo "  -y, --yocto             Enable Yocto build mode"
    echo "  -h, --help              Display this help message"
    echo "  -a, --android           Enable Android build mode"
    echo "  -x, --x86               Enable x86 build mode"
    echo "  -c,                     Clean project"
}

show_parameters() {

    echo -e "\e[32mBuilder parameters:\e[0m"
    echo "-- Number of jobs     : [$jobs]"
    echo "-- Yocto build mode   : [$yocto_flag]"
    echo "-- Android build mode : [$android_flag]"
    echo "-- x86 build mode     : [$x86_flag]"
    if [ "$yocto_flag" = true ]; then
        install_prefix=$install_prefix/yocto
        echo "-- install path       : $install_prefix"
    elif [ "$android_flag" = true ]; then
        install_prefix=$install_prefix/android
        echo "-- install path       : $install_prefix"
    elif [ "$x86_flag" = true ]; then
        install_prefix=$install_prefix/x86
        echo "-- install path       : $install_prefix"
    fi
}

build_cyclonedds(){

    echo -e "\e[32mStart build cyclonedds project\e[0m"

    cd $shell_path

    cd $cyclonedds_home

    if [ -d build ]; then
        cd build
        rm -rf *
    else
        mkdir build && cd build
    fi

    if [[ "$1" =~ ^NDK$ ]]; then
        cmake -DCMAKE_INSTALL_PREFIX=$install_prefix -DNDK=1 ..
        make -j $jobs
        cmake --build . --target install -j $jobs
        echo "The compilation version is ANDROID."
    elif [[ "$1" =~ ^NDK_X86$ ]]; then
        cmake -DCMAKE_INSTALL_PREFIX=$install_prefix -DNDK_X86=1 ..
        make -j $jobs
        cmake --build . --target install -j $jobs
        echo "The compilation version is J6."
    elif [[ "$1" =~ ^J6$ ]]; then
        cmake -DCMAKE_INSTALL_PREFIX=$install_prefix -DJ6=1 ..
        make -j $jobs
        cmake --build . --target install -j $jobs
        echo "The compilation version is J6."
    else
        cmake -DCMAKE_INSTALL_PREFIX=$install_prefix  ..
        make -j $jobs
        cmake --build . --target install -j $jobs
        echo "The compilation version is default."
    fi

    echo -e "\e[32mSuccess build cyclonedds project\e[0m"
}

build_cycloneddscxx() {

    echo -e "\e[32mStart build cycloneddscxx project\e[0m"

    cd $shell_path

    cd $cycloneddscxx_home

    if [ -d build ]; then
        cd build
        rm -rf *
    else
        mkdir build && cd build
    fi

    define=""

    if [ "$yocto_flag" = true ]; then
        define="-DJ6=1"
    fi

    cmake -DCMAKE_INSTALL_PREFIX=$install_prefix $define ..
    cmake --build . --target install -j $jobs

    echo -e "\e[32mSuccess build cycloneddscxx project\e[0m"
}

build_crpc() {

    echo -e "\e[32mStart build crpc project\e[0m"

    cd $shell_path

    cd $seres_crpc_home

    if [ -d build ]; then
        cd build
        rm -rf *
    else
        mkdir build && cd build
    fi

    define=""

    if [ "$yocto_flag" = true ]; then
        define="-DJ6=1"
    fi

    cmake -DCMAKE_INSTALL_PREFIX=$install_prefix $define ..
    cmake --build . --target install -j $jobs

    echo -e "\e[32mSuccess build crpc project\e[0m"
}

build_cxxrpc() {

    echo -e "\e[32mStart build cxxrpc project\e[0m"

    cd $shell_path

    cd $seres_cxxrpc_home

    if [ -d build ]; then
        cd build
        rm -rf *
    else
        mkdir build && cd build
    fi

    define=""

    if [ "$yocto_flag" = true ]; then
        define="-DJ6=1"
    fi

    cmake -DCMAKE_INSTALL_PREFIX=$install_prefix $define ..
    cmake --build . --target install -j $jobs

    echo -e "\e[32mSuccess build cxxrpc project\e[0m"
}


build_kotlinidlctool(){
    echo -e "\e[32mStart build Kotlin Code Generator project\e[0m"

    cd $install_prefix/../../../scom-android/idlkotlin
    if [ -d build ]; then
        cd build
        rm -rf *
    else
        mkdir build && cd build
    fi
    cmake -DCMAKE_INSTALL_PREFIX=$install_prefix ..
    cmake --build . --target install -j $jobs

    echo -e "\e[32mEnd build Kotlin Code Generator project\e[0m"
}


build_kotlindds() {
    echo -e "\e[32mStart build scom-kotlin project\e[0m"
    # build_cyclonedds
    echo -e "\033[34m>>>>>>>>>>>>>>Start build libdsc.so[android x86_64 version]>>>>>>>>>>>>>>\033[0m"
    build_cyclonedds "NDK_x86"
    if [ ! -f  $install_prefix/lib/libddsc.so ]; then
        echo "no libddsc.so was not built in this path: $install_prefix/lib/libddsc.so x86_64 for android"
        exit 1
    else
        echo "libddsc.so was built successfully in this path: $install_prefix/lib/libddsc.so x86_64 for android"
    fi
    cp $install_prefix/lib/libddsc.so  $install_prefix/../../../scom-android/nativelib/src/main/cpp/libs/x86_64/libddsc.so
    echo -e "\033[34m<<<<<<<<<<<<<<Success build libdsc.so[android x86_64 version]<<<<<<<<<<<<<<\033[0m"

    echo -e "\033[34m>>>>>>>>>>>>>>Start build libdsc.so[android arm64-v8a version]>>>>>>>>>>>>>>\033[0m"
    build_cyclonedds "NDK"
    
    if [ ! -f  $install_prefix/lib/libddsc.so ]; then
        echo "libddsc.so was not built in this path: $install_prefix/lib/libddsc.so arm64-v8a"
        exit 1
    else
        echo "libddsc.so was built successfully in this path: $install_prefix/lib/libddsc.so arm64-v8a"
    fi
    cp $install_prefix/lib/libddsc.so  $install_prefix/../../../scom-android/nativelib/src/main/cpp/libs/arm64-v8a/libddsc.so
    echo -e "\033[34m<<<<<<<<<<<<<<Success build libdsc.so[android arm64-v8a version]<<<<<<<<<<<<<<\033[0m"


    echo -e "\033[34m>>>>>>>>>>>>>>Start build libdsc.so[linux x86_64 version]>>>>>>>>>>>>>>\033[0m"
    build_cyclonedds
    if [ ! -f  $install_prefix/lib/libddsc.so ]; then
        echo "no libddsc.so was not built in this path: $install_prefix/lib/libddsc.so x86_64 for Linux"
        exit 1
    else
        echo "libddsc.so was built successfully in this path: $install_prefix/lib/libddsc.so x86_64 for Linux"
    fi
    cp $install_prefix/lib/libddsc.so  $install_prefix/../../../scom-android/nativelib/src/main/cpp/libs/x86/libddsc.so
    echo -e "\033[34m<<<<<<<<<<<<<<Success build libdsc.so[linux x86_64 version]<<<<<<<<<<<<<<-\033[0m"

    cd $install_prefix/../../../scom-android/

    # 将scom-kotlin gradle构建输出物
    ./gradlew kscom-linux_version
    ./gradlew build
    ./gradlew cleanJniLibs
    
    cd $install_prefix
    mkdir aar

    # 将scom-kotlin 编译输出移动位置至output
    for file in  $install_prefix/../../../scom-android/nativelib/build/outputs/aar/*.aar; do
        cp "$file" $install_prefix/aar/
    done

    # cd $install_prefix/../../../scom-android/
    # ./gradlew clean 
    # cd $install_prefix/../../../scom-android/
    build_kotlinidlctool
    echo -e "\e[32mSuccess build scom-kotlin project\e[0m"
}


clean_project() {
    cd $shell_path

    if [ "$yocto_flag" = true ]; then
        rm -rf output/yocto
    elif [ "$x86_flag" = true ]; then
        rm -rf output/x86
    else
        rm -rf output/android
    fi
}


# -------- 逻辑区 ----------

# 显示帮助信息
if [ "$help_flag" = true ]; then
  show_helps
  exit 0
fi

# 清理编译产物
if [ "$clean_flag" = true ]; then
    echo -e "\e[32mStart clean project\e[0m"
    clean_project

    echo -e "\e[32mSuccess clean project\e[0m"
    exit 1
fi

# 编译参数打印
show_parameters

clean_project

if [ "$yocto_flag" = true ]; then
  echo -e "\e[32mEnabling Yocto build mode\e[0m"
  build_cyclonedds "J6"

  export CycloneDDS_DIR=$install_prefix

  build_cycloneddscxx
  build_crpc
  build_cxxrpc
fi

if [ "$x86_flag" = true ]; then
  echo -e "\e[32mEnabling X86 build mode\e[0m"

  build_cyclonedds

  export CycloneDDS_DIR=$install_prefix

  build_cycloneddscxx
  build_crpc
  build_cxxrpc
fi

if [ "$android_flag" = true ]; then
  echo -e "\e[32mEnabling Android build mode\e[0m"

  build_kotlindds
  export CycloneDDS_DIR=$install_prefix
  echo -e "\e[32mSuccess build scom-kotlin project\e[0m"
fi

echo -e "\e[32m   ---------------------------------------------------------------------------- \e[0m"
echo -e "\e[32m   ++     ***********      ***********      ***********      **           ** ++ \e[0m"
echo -e "\e[32m   ++    **               **               **       **      **  *     *  **  ++ \e[0m"
echo -e "\e[32m   ++   ***********      **               **       **      **    *  *   **   ++ \e[0m"
echo -e "\e[32m   ++           **      **               **       **      **     **    **    ++ \e[0m"
echo -e "\e[32m   ++ ***********      ***********      ***********      **     **    **     ++ \e[0m"
echo -e "\e[32m   ---------------------------------------------------------------------------- \e[0m"