#!/bin/bash

set -e

# 默认参数值
jobs=1
yocto_flag=false
help_flag=false
clean_flag=false
x86_flag=false
doip_home=../../scom-doip/doiprust
shell_path=`pwd`
install_prefix=`pwd`/output_doip

# 使用 getopt 处理长参数和短参数
options=$(getopt -o j:xyhac -l jobs:,x86,yocto,android,help -- "$@")
if [ $? != 0 ]; then
    echo "getopt error"
    exit 1
fi

eval set -- "$options"

# 解析命令行参数
while true; do
    case "$1" in
        -j | --jobs)
            jobs=$2
            shift 2
            ;;
        -y | --yocto)
            yocto_flag=true
            shift
            ;;
        -x | --x86)
            x86_flag=true
            shift
            ;;
        -h | --help)
            help_flag=true
            shift
            ;;
        -c)
            clean_flag=true
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            help_flag=true
            ;;
    esac
done

# ---------- 函数块 ---------

show_helps() {
    echo "Usage: $0 [-j <jobs>] [--yocto] [-h] [--android]"
    echo "  -j, --jobs <jobs>       Number of jobs to run in parallel (default: 1)"
    echo "  -y, --yocto             Enable Yocto build mode"
    echo "  -h, --help              Display this help message"
    echo "  -a, --android           Enable Android build mode"
    echo "  -x, --x86               Enable x86 build mode"
    echo "  -c,                     Clean project"
}

show_parameters() {

    echo -e "\e[32mBuilder parameters:\e[0m"
    echo "-- Number of jobs     : [$jobs]"
    echo "-- Yocto build mode   : [$yocto_flag]"
    echo "-- x86 build mode     : [$x86_flag]"

    if [ "$yocto_flag" = true ]; then
        install_prefix=$install_prefix/yocto
        echo "-- install path       : $install_prefix"
    elif [ "$x86_flag" = true ]; then
        install_prefix=$install_prefix/x86
        echo "-- install path       : $install_prefix"
    fi

    # check install dir
    if [[ ! -d "$install_prefix" ]]; then
        mkdir -p "$install_prefix"
    fi
    if [[ ! -d "$install_prefix/include" ]];then
        mkdir -p "$install_prefix/include"
    fi
    if [[ ! -d "$install_prefix/lib" ]];then
        mkdir -p "$install_prefix/lib"
    fi
}

clean_project() {
    cd $shell_path
    rm -rf $install_prefix/include/*
    rm -rf $install_prefix/lib/*
}

build_doip(){
    echo -e "\e[32mStart build DOIP  project\e[0m"
    cd $shell_path
    cd $doip_home

    ./build.sh $1

    cp install/$1/lib/*.so $install_prefix/lib
    cp install/$1/include/*.h  $install_prefix/include
}

# -------- 逻辑区 ----------

# 显示帮助信息
if [ "$help_flag" = true ]; then
  show_helps
  exit 0
fi

# 清理编译产物
if [ "$clean_flag" = true ]; then
    echo -e "\e[32mStart clean project\e[0m"
    clean_project

    echo -e "\e[32mSuccess clean project\e[0m"
    exit 1
fi

show_parameters
clean_project

if [ "$yocto_flag" = true ]; then
    echo -e "\e[32mEnabling Yocto build mode\e[0m"
    build_doip "aarch64"
elif [ "$x86_flag" = true ];then
    echo -e "\e[32mEnabling X86 build mode\e[0m"
    build_doip "x86"
fi