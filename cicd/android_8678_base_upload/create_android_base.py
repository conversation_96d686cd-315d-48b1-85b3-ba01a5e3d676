import os
import xml.etree.ElementTree as ET

# 获取当前工作目录
current_dir = os.getcwd()
# 读取manfest文件
try:
    tree = ET.parse('seres-mt8678-init.xml')
    root = tree.getroot()
except ET.ParseError:
    with open('log.txt', 'a') as log_file:
        log_file.write("XML decoding error\n")
    exit(1)

# 提取project的name和path
projects_dict = {}
for project in root.findall('project'):
    name = project.get('name')
    path = project.get('path')
    if name and path:
        projects_dict[name] = path

print(projects_dict)
# 循环处理每个项目
for project_name, path in projects_dict.items():
    print(project_name)
    print(path)
    try:
        # 进入项目路径
        os.chdir(path)

        # 删除现有的.git目录
        if os.path.exists('.git'):
            os.system('rm -rf .git')

        create_repo_cmd = f'ssh -p 29418 492375@10.0.12.208 gerrit create-project --owner subsystem_integration --parent android_base_access --empty-commit {project_name}'
        os.system(create_repo_cmd)
        # # 初始化git仓库
        os.system('git init')
        # 添加所有文件到git仓库
        os.system('git add . -f')

        # 提交并添加提交消息
        commit_msg = 'wenyan android base init ....'
        os.system(f'git commit -m "{commit_msg}"')

        # 添加远程仓库并推送到远程仓库
        origin_url = f'ssh://492375@10.0.12.208:29418/{project_name}'
        os.system(f'git remote add origin {origin_url}')

        os.system('git push -f origin HEAD:android_mt8678')
        #os.system('git push origin HEAD:DC1E_CN_22.1_src')
    except Exception as e:
        with open('log.txt', 'a') as log_file:
            log_file.write(f"deal with proj {project_name} error: {e}\n")
    finally:
        # 返回到代码根目录
        os.chdir(current_dir)
