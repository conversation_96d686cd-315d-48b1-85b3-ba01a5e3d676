<?xml version="1.0" encoding="utf-8"?>
    <manifest>
      <remote fetch=".." name="pvt"/>

      <default remote="pvt" revision="refs/heads/pvt_RD" sync-j="4"/>

      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/ccu_tool" path="ccu_tool"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/device/mediatek/build" path="device/mediatek/build"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/device/mediatek/common" path="device/mediatek/common">
        <copyfile dest="vendor/mediatek/proprietary/Android.bp" src="addon/vendor/mediatek/proprietary/srcAndroid.bp"/>
        <copyfile dest="vendor/mediatek/Android.mk" src="addon/vendor/mediatek/srcAndroid.mk"/>
        <copyfile dest="vendor/mediatek/proprietary/hardware/libcamera_feature/Android.mk" src="addon/vendor/mediatek/proprietary/hardware/libcamera_feature/srcAndroid.mk"/>
        <copyfile dest="vendor/mediatek/proprietary/hardware/libcamera_3a/Android.mk" src="addon/vendor/mediatek/proprietary/hardware/libcamera_3a/srcAndroid.mk"/>
        <copyfile dest="vendor/mediatek/proprietary/hardware/libcam_hal/Android.mk" src="addon/vendor/mediatek/proprietary/hardware/libcam_hal/srcAndroid.mk"/>
      </project>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/config" path="device/mediatek/config"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/device/mediatek/cuttlestone" path="device/mediatek/cuttlestone"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_aging_k61" path="device/mediatek/kernel/mgk_64_aging_k61"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_entry_level_k61" path="device/mediatek/kernel/mgk_64_entry_level_k61"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_fpga_k61" path="device/mediatek/kernel/mgk_64_fpga_k61"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61" path="device/mediatek/kernel/mgk_64_k61"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61_auto" path="device/mediatek/kernel/mgk_64_k61_auto"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61_auto_vm" path="device/mediatek/kernel/mgk_64_k61_auto_vm"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61_auto_vm_kasan" path="device/mediatek/kernel/mgk_64_k61_auto_vm_kasan"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61_auto_wifi" path="device/mediatek/kernel/mgk_64_k61_auto_wifi"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_k61_wifi" path="device/mediatek/kernel/mgk_64_k61_wifi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_kasan_k61" path="device/mediatek/kernel/mgk_64_kasan_k61"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_khwasan_k61" path="device/mediatek/kernel/mgk_64_khwasan_k61"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/kernel/mgk_64_vulscan_k61" path="device/mediatek/kernel/mgk_64_vulscan_k61"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/mt6991" path="device/mediatek/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/device/mediatek/sepolicy/base" path="device/mediatek/sepolicy/base"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/device/mediatek/sepolicy/car" path="device/mediatek/sepolicy/car"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/device/mediatek/sepolicy/debug" path="device/mediatek/sepolicy/debug"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/device/mediatek/sepolicy/third_party" path="device/mediatek/sepolicy/third_party"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/common" path="device/mediatek/system/common"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ago_h_ww" path="device/mediatek/system/mssi_32_ago_h_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ago_h_ww_armv7" path="device/mediatek/system/mssi_32_ago_h_ww_armv7"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ago_ww" path="device/mediatek/system/mssi_32_ago_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ago_ww_armv7" path="device/mediatek/system/mssi_32_ago_ww_armv7"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_cn" path="device/mediatek/system/mssi_32_cn"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_cn_armv82" path="device/mediatek/system/mssi_32_cn_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_cn_q_ota" path="device/mediatek/system/mssi_32_cn_q_ota"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ww" path="device/mediatek/system/mssi_32_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_32_ww_armv82_tc10" path="device/mediatek/system/mssi_32_ww_armv82_tc10"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_64only_cn_armv82" path="device/mediatek/system/mssi_64_64only_cn_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_64only_ww_armv82" path="device/mediatek/system/mssi_64_64only_ww_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn" path="device/mediatek/system/mssi_64_cn"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_armv82" path="device/mediatek/system/mssi_64_cn_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_armv82_pangu" path="device/mediatek/system/mssi_64_cn_armv82_pangu"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_armv9" path="device/mediatek/system/mssi_64_cn_armv9"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_hwasan_armv82" path="device/mediatek/system/mssi_64_cn_hwasan_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_no_ab" path="device/mediatek/system/mssi_64_cn_no_ab"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_no_ab_armv82" path="device/mediatek/system/mssi_64_cn_no_ab_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_cn_nonab_armv82" path="device/mediatek/system/mssi_64_cn_nonab_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_vsdk_armv82" path="device/mediatek/system/mssi_64_vsdk_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_ww" path="device/mediatek/system/mssi_64_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_ww_armv82" path="device/mediatek/system/mssi_64_ww_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_ww_armv82_tc10" path="device/mediatek/system/mssi_64_ww_armv82_tc10"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_ww_armv8_no_addon_generic_cpu" path="device/mediatek/system/mssi_64_ww_armv8_no_addon_generic_cpu"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_64_ww_tc10" path="device/mediatek/system/mssi_64_ww_tc10"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiot_32_ago" path="device/mediatek/system/mssi_aiot_32_ago"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiot_64_ab" path="device/mediatek/system/mssi_aiot_64_ab"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiot_64_arm82" path="device/mediatek/system/mssi_aiot_64_arm82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiv_32_ago" path="device/mediatek/system/mssi_aiv_32_ago"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiv_64" path="device/mediatek/system/mssi_aiv_64"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_aiv_64_armv82" path="device/mediatek/system/mssi_aiv_64_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_auto_64_cn_armv82" path="device/mediatek/system/mssi_auto_64_cn_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_auto_64_cn_armv82_hwasan" path="device/mediatek/system/mssi_auto_64_cn_armv82_hwasan"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_auto_64_cn_armv82_mumd" path="device/mediatek/system/mssi_auto_64_cn_armv82_mumd"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_auto_64_cn_armv82_vm" path="device/mediatek/system/mssi_auto_64_cn_armv82_vm"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago" path="device/mediatek/system/mssi_t_32_ago"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_h" path="device/mediatek/system/mssi_t_32_ago_h"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_h_ww" path="device/mediatek/system/mssi_t_32_ago_h_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_h_ww_armv7" path="device/mediatek/system/mssi_t_32_ago_h_ww_armv7"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_p" path="device/mediatek/system/mssi_t_32_ago_p"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_q" path="device/mediatek/system/mssi_t_32_ago_q"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_ww" path="device/mediatek/system/mssi_t_32_ago_ww"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_ww_armv7" path="device/mediatek/system/mssi_t_32_ago_ww_armv7"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ago_ww_armv7_datasms" path="device/mediatek/system/mssi_t_32_ago_ww_armv7_datasms"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_cn" path="device/mediatek/system/mssi_t_32_cn"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_32_ww_armv7" path="device/mediatek/system/mssi_t_32_ww_armv7"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64" path="device/mediatek/system/mssi_t_64"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_ab" path="device/mediatek/system/mssi_t_64_ab"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_ab_p" path="device/mediatek/system/mssi_t_64_ab_p"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn" path="device/mediatek/system/mssi_t_64_cn"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_ab" path="device/mediatek/system/mssi_t_64_cn_ab"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_armv82" path="device/mediatek/system/mssi_t_64_cn_armv82"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_armv82_datasms" path="device/mediatek/system/mssi_t_64_cn_armv82_datasms"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_armv82_wifi" path="device/mediatek/system/mssi_t_64_cn_armv82_wifi"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_datasms" path="device/mediatek/system/mssi_t_64_cn_datasms"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_p_ab" path="device/mediatek/system/mssi_t_64_cn_p_ab"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_p_ota" path="device/mediatek/system/mssi_t_64_cn_p_ota"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_q_ota" path="device/mediatek/system/mssi_t_64_cn_q_ota"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_wifi" path="device/mediatek/system/mssi_t_64_cn_wifi"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_cn_wifi_hwasan" path="device/mediatek/system/mssi_t_64_cn_wifi_hwasan"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_sp1" path="device/mediatek/system/mssi_t_64_sp1"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_sp2" path="device/mediatek/system/mssi_t_64_sp2"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_ww_armv82_tc10" path="device/mediatek/system/mssi_t_64_ww_armv82_tc10"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_t_64_ww_armv82_tc10_wifi" path="device/mediatek/system/mssi_t_64_ww_armv82_tc10_wifi"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/device/mediatek/system/mssi_x86_bsp" path="device/mediatek/system/mssi_x86_bsp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/device/mediatek/vendor/camera" path="device/mediatek/vendor/camera"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/device/mediatek/vendor/common" path="device/mediatek/vendor/common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_64only_armv82" path="device/mediatek/vendor/mgvi_64_64only_armv82"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_64only_armv82_thinmodem" path="device/mediatek/vendor/mgvi_64_64only_armv82_thinmodem"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_64only_ww_armv82" path="device/mediatek/vendor/mgvi_64_64only_ww_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_armv82" path="device/mediatek/vendor/mgvi_64_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_armv9" path="device/mediatek/vendor/mgvi_64_armv9"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_dx3_gpu_pretest_armv82" path="device/mediatek/vendor/mgvi_64_dx3_gpu_pretest_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_tee_armv82" path="device/mediatek/vendor/mgvi_64_tee_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_teei_armv82" path="device/mediatek/vendor/mgvi_64_teei_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_ww_armv82" path="device/mediatek/vendor/mgvi_64_ww_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_64_ww_armv8_no_addon_generic_cpu" path="device/mediatek/vendor/mgvi_64_ww_armv8_no_addon_generic_cpu"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_auto_64_armv82" path="device/mediatek/vendor/mgvi_auto_64_armv82"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_auto_64_armv82_mumd" path="device/mediatek/vendor/mgvi_auto_64_armv82_mumd"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_auto_64_armv82_wifi" path="device/mediatek/vendor/mgvi_auto_64_armv82_wifi"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_auto_64_armv82_wifi_vm" path="device/mediatek/vendor/mgvi_auto_64_armv82_wifi_vm"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_auto_64_armv82_wifi_vm_hwasan" path="device/mediatek/vendor/mgvi_auto_64_armv82_wifi_vm_hwasan"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_t_64_armv82" path="device/mediatek/vendor/mgvi_t_64_armv82"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediatek/vendor/mgvi_t_64_wifi_armv82" path="device/mediatek/vendor/mgvi_t_64_wifi_armv82"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediateksample/auto8678p1_64_bsp_vm" path="device/mediateksample/auto8678p1_64_bsp_vm"/>
      <project groups="mtk-vendor-config,mtk-vnd-codebase" name="alps/device/mediateksample/auto8678p1_64_bsp_wifi" path="device/mediateksample/auto8678p1_64_bsp_wifi"/>
      <project groups="mtk-kernel,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/kernel/build/bazel_mgk_rules" path="kernel/build/bazel_mgk_rules">
        <linkfile dest="kernel/WORKSPACE" src="kleaf/bazel.WORKSPACE"/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/prebuilts/clang/clang-tee" path="prebuilts/clang/clang-tee"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/prebuilts/clang/md32rv/linux-x86" path="prebuilts/clang/md32rv/linux-x86"/>
      <project groups="mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/aarch64/aarch64-linux-android-4.9.1" path="prebuilts/gcc/linux-x86/aarch64/aarch64-linux-android-4.9.1"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/aarch64/gcc-arm-8.3-2019.03-x86_64-aarch64-elf" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-8.3-2019.03-x86_64-aarch64-elf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/aarch64/gcc-arm-9.2-2019.12-x86_64-aarch64-none-elf" path="prebuilts/gcc/linux-x86/aarch64/gcc-arm-9.2-2019.12-x86_64-aarch64-none-elf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arc/arc_gnu_2016.09_prebuilt_elf32_le_linux" path="prebuilts/gcc/linux-x86/arc/arc_gnu_2016.09_prebuilt_elf32_le_linux"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arctoolchain" path="prebuilts/gcc/linux-x86/arctoolchain"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arm/arm-linux-androideabi-4.9.1" path="prebuilts/gcc/linux-x86/arm/arm-linux-androideabi-4.9.1"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arm/gcc-arm-8.3-2019.03-x86_64-arm-eabi" path="prebuilts/gcc/linux-x86/arm/gcc-arm-8.3-2019.03-x86_64-arm-eabi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arm/gcc-arm-none-eabi-4_7-2012q4" path="prebuilts/gcc/linux-x86/arm/gcc-arm-none-eabi-4_7-2012q4"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arm/gcc-arm-none-eabi-4_8-2014q3" path="prebuilts/gcc/linux-x86/arm/gcc-arm-none-eabi-4_8-2014q3"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/arm/gcc-linaro-7.1.1-2017.08-x86_64_arm-eabi" path="prebuilts/gcc/linux-x86/arm/gcc-linaro-7.1.1-2017.08-x86_64_arm-eabi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/prebuilts/gcc/linux-x86/riscv/zephyr-sdk-0.15.0" path="prebuilts/gcc/linux-x86/riscv/zephyr-sdk-0.15.0"/>
      <project groups="mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/aosp_gki" path="vendor/aosp_gki"/>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/bt/linux_v2" path="vendor/mediatek/kernel_modules/connectivity/bt/linux_v2_ce">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/bt/linux_v2_ce" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/bt/mt66xx" path="vendor/mediatek/kernel_modules/connectivity/bt/mt66xx">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/bt/mt66xx" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/bt/mt76xx/sdio" path="vendor/mediatek/kernel_modules/connectivity/bt/mt76xx/sdio">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/bt/mt76xx/sdio" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/common" path="vendor/mediatek/kernel_modules/connectivity/common">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/common" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/connfem" path="vendor/mediatek/kernel_modules/connectivity/connfem">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/connfem" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/conninfra" path="vendor/mediatek/kernel_modules/connectivity/conninfra">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/conninfra" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/fmradio" path="vendor/mediatek/kernel_modules/connectivity/fmradio">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/fmradio" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/gps" path="vendor/mediatek/kernel_modules/connectivity/gps">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/gps" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/adaptor" path="vendor/mediatek/kernel_modules/connectivity/wlan/adaptor">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/adaptor" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen2" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen2">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen2" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen3" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen3">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen3" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7663" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7663">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7663" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7668" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7668">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt7668" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt79xx" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt79xx">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4-mt79xx" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/fpsgo_cus" path="vendor/mediatek/kernel_modules/fpsgo_cus">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/fpsgo_cus" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/gpu" path="vendor/mediatek/kernel_modules/gpu">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/gpu" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/hbt_driver_cus" path="vendor/mediatek/kernel_modules/hbt_driver_cus">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/hbt_driver_cus" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/met_drv_v2" path="vendor/mediatek/kernel_modules/met_drv_v2">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/met_drv_v2" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/met_drv_v3" path="vendor/mediatek/kernel_modules/met_drv_v3">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/met_drv_v3" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/msync2_frd_cus" path="vendor/mediatek/kernel_modules/msync2_frd_cus">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/msync2_frd_cus" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/sched_cus" path="vendor/mediatek/kernel_modules/sched_cus">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/sched_cus" src="."/>
      </project>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/kernel_modules/udc" path="vendor/mediatek/kernel_modules/udc">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/udc" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/opensource/apedec" path="vendor/mediatek/opensource/apedec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/opensource/external/doeapp-open" path="vendor/mediatek/opensource/external/doeapp-open"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/opensource/external/eigen" path="vendor/mediatek/opensource/external/eigen"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/opensource/external/libmnl" path="vendor/mediatek/opensource/external/libmnl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/bootable/bootloader/lk" path="vendor/mediatek/proprietary/bootable/bootloader/lk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vext,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/bootable/bootloader/preloader" path="vendor/mediatek/proprietary/bootable/bootloader/preloader"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-sys-vnd,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/bootable/recovery/utils" path="vendor/mediatek/proprietary/bootable/recovery/utils"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/bootable/recovery/utils_vendor" path="vendor/mediatek/proprietary/bootable/recovery/utils_vendor"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/buildinfo_sys" path="vendor/mediatek/proprietary/buildinfo_sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/buildinfo_versions" path="vendor/mediatek/proprietary/buildinfo_versions"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/buildinfo_vnd" path="vendor/mediatek/proprietary/buildinfo_vnd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/cgen" path="vendor/mediatek/proprietary/cgen"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/custom" path="vendor/mediatek/proprietary/custom"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/AudioCompensationFilter" path="vendor/mediatek/proprietary/external/AudioCompensationFilter"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/AudioComponentEngine" path="vendor/mediatek/proprietary/external/AudioComponentEngine"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/AudioDCRemoval" path="vendor/mediatek/proprietary/external/AudioDCRemoval"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/AudioParamParser" path="vendor/mediatek/proprietary/external/AudioParamParser"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/AudioSetParam" path="vendor/mediatek/proprietary/external/AudioSetParam"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/AudioSpeechEnhancement" path="vendor/mediatek/proprietary/external/AudioSpeechEnhancement"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/BootLogo" path="vendor/mediatek/proprietary/external/BootLogo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/EccList" path="vendor/mediatek/proprietary/external/EccList"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/LaunchPPPoe" path="vendor/mediatek/proprietary/external/LaunchPPPoe"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/NetworkLogD" path="vendor/mediatek/proprietary/external/NetworkLogD"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/abseil/abseil-cpp" path="vendor/mediatek/proprietary/external/abseil/abseil-cpp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/adpcmdec_spmlib" path="vendor/mediatek/proprietary/external/adpcmdec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/adsp_utils" path="vendor/mediatek/proprietary/external/adsp_utils"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/aee" path="vendor/mediatek/proprietary/external/aee"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/aihub/NeuronSdk" path="vendor/mediatek/proprietary/external/aihub/NeuronSdk"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/aihub/libapuwareapi" path="vendor/mediatek/proprietary/external/aihub/libapuwareapi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/aihub/mcv_delegate" path="vendor/mediatek/proprietary/external/aihub/mcv_delegate"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/aihub/mvpu_sdk" path="vendor/mediatek/proprietary/external/aihub/mvpu_sdk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/alacdec_spmlib" path="vendor/mediatek/proprietary/external/alacdec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/amr" path="vendor/mediatek/proprietary/external/amr"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/apedec" path="vendor/mediatek/proprietary/external/apedec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/apm_client" path="vendor/mediatek/proprietary/external/apm_client"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audioATCmdHandlerServer" path="vendor/mediatek/proprietary/external/audioATCmdHandlerServer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audio_preprocessing_mtk" path="vendor/mediatek/proprietary/external/audio_preprocessing_mtk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audio_utils" path="vendor/mediatek/proprietary/external/audio_utils"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/audiocommand" path="vendor/mediatek/proprietary/external/audiocommand"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audiocustparam" path="vendor/mediatek/proprietary/external/audiocustparam"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audiodcremoveflt" path="vendor/mediatek/proprietary/external/audiodcremoveflt"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/audioeffect" path="vendor/mediatek/proprietary/external/audioeffect"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/aurisys" path="vendor/mediatek/proprietary/external/aurisys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/awb" path="vendor/mediatek/proprietary/external/awb"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/bessound_HD" path="vendor/mediatek/proprietary/external/bessound_HD"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/blisrc" path="vendor/mediatek/proprietary/external/blisrc"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/boot_logo_updater" path="vendor/mediatek/proprietary/external/boot_logo_updater"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/ccci_mdinit_src" path="vendor/mediatek/proprietary/external/ccci_mdinit_src"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/ccci_rpcd_com" path="vendor/mediatek/proprietary/external/ccci_rpcd_com"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/charger" path="vendor/mediatek/proprietary/external/charger"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/connsyslogD" path="vendor/mediatek/proprietary/external/connsyslogD"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/cvsd_plc_codec" path="vendor/mediatek/proprietary/external/cvsd_plc_codec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/dmc_core" path="vendor/mediatek/proprietary/external/dmc_core"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/dmc_translator_spmlib" path="vendor/mediatek/proprietary/external/dmc_translator_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/doeapp-service" path="vendor/mediatek/proprietary/external/doeapp-service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/doeapp_spmlib" path="vendor/mediatek/proprietary/external/doeapp_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/eara_io" path="vendor/mediatek/proprietary/external/eara_io"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/farmhash" path="vendor/mediatek/proprietary/external/farmhash"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/fft" path="vendor/mediatek/proprietary/external/fft"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/fuelgauged" path="vendor/mediatek/proprietary/external/fuelgauged"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/gps_spmlib" path="vendor/mediatek/proprietary/external/gps_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/hdmi" path="vendor/mediatek/proprietary/external/hdmi"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/ipod" path="vendor/mediatek/proprietary/external/ipod"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/ipsec_policy_monitor" path="vendor/mediatek/proprietary/external/ipsec_policy_monitor"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/iqi/vendor" path="vendor/mediatek/proprietary/external/iqi/vendor"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/lcdc_screen_cap" path="vendor/mediatek/proprietary/external/lcdc_screen_cap"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/libcompresscopy" path="vendor/mediatek/proprietary/external/libcompresscopy"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libdynamiclog_spmlib" path="vendor/mediatek/proprietary/external/libdynamiclog_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libion_mtk" path="vendor/mediatek/proprietary/external/libion_mtk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libipsec_ims" path="vendor/mediatek/proprietary/external/libipsec_ims"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libjpeg-alpha" path="vendor/mediatek/proprietary/external/libjpeg-alpha"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libjpeg-alpha_vendor" path="vendor/mediatek/proprietary/external/libjpeg-alpha_vendor"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libmmprofile" path="vendor/mediatek/proprietary/external/libmmprofile"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libnvram" path="vendor/mediatek/proprietary/external/libnvram"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libnvram_ext" path="vendor/mediatek/proprietary/external/libnvram_ext"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libsched" path="vendor/mediatek/proprietary/external/libsched"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/libshowlogo" path="vendor/mediatek/proprietary/external/libshowlogo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libstorage_otp" path="vendor/mediatek/proprietary/external/libstorage_otp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libsysenv" path="vendor/mediatek/proprietary/external/libsysenv"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/libudf" path="vendor/mediatek/proprietary/external/libudf"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/libwifisalog" path="vendor/mediatek/proprietary/external/libwifisalog"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/lightgbm_lib" path="vendor/mediatek/proprietary/external/lightgbm_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/limiter" path="vendor/mediatek/proprietary/external/limiter"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/loghidlsysservice" path="vendor/mediatek/proprietary/external/loghidlsysservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/loghidlvendorservice" path="vendor/mediatek/proprietary/external/loghidlvendorservice"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/mapi" path="vendor/mediatek/proprietary/external/mapi"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/mdmi" path="vendor/mediatek/proprietary/external/mdmi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/midiSeektable" path="vendor/mediatek/proprietary/external/midiSeektable"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mkimage" path="vendor/mediatek/proprietary/external/mkimage"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mmc_ffu" path="vendor/mediatek/proprietary/external/mmc_ffu"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/mmp" path="vendor/mediatek/proprietary/external/mmp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mntl_link" path="vendor/mediatek/proprietary/external/mntl_link"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/mobile_log_d" path="vendor/mediatek/proprietary/external/mobile_log_d"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mp3dec_spmlib" path="vendor/mediatek/proprietary/external/mp3dec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mrdump" path="vendor/mediatek/proprietary/external/mrdump"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/msbc_codec" path="vendor/mediatek/proprietary/external/msbc_codec"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/mtkFileIO" path="vendor/mediatek/proprietary/external/mtkFileIO"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mvg_app" path="vendor/mediatek/proprietary/external/mvg_app"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mvpu_cl_toolchain/llvm-project_spmlib" path="vendor/mediatek/proprietary/external/mvpu_cl_toolchain/llvm-project_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/mvpu_cl_toolchain_14/llvm-project_spmlib" path="vendor/mediatek/proprietary/external/mvpu_cl_toolchain_14/llvm-project_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/nvram" path="vendor/mediatek/proprietary/external/nvram"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/opencl_icd_loader" path="vendor/mediatek/proprietary/external/opencl_icd_loader"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/otp" path="vendor/mediatek/proprietary/external/otp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/performance" path="vendor/mediatek/proprietary/external/performance"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/performance_system" path="vendor/mediatek/proprietary/external/performance_system"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/permission_check" path="vendor/mediatek/proprietary/external/permission_check"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/propsync" path="vendor/mediatek/proprietary/external/propsync"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/reboot_p_srv" path="vendor/mediatek/proprietary/external/reboot_p_srv"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/sensor-tools" path="vendor/mediatek/proprietary/external/sensor-tools"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/shifter" path="vendor/mediatek/proprietary/external/shifter"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/skia_asm" path="vendor/mediatek/proprietary/external/skia_asm"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/sn" path="vendor/mediatek/proprietary/external/sn"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/tflite_gpu_delegate" path="vendor/mediatek/proprietary/external/tflite_gpu_delegate"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/thermal" path="vendor/mediatek/proprietary/external/thermal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/thermal_core_lib" path="vendor/mediatek/proprietary/external/thermal_core_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/thermal_managerlib" path="vendor/mediatek/proprietary/external/thermal_managerlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/thermalalgolib" path="vendor/mediatek/proprietary/external/thermalalgolib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/touchfilter" path="vendor/mediatek/proprietary/external/touchfilter"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/udf" path="vendor/mediatek/proprietary/external/udf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/ufs_util" path="vendor/mediatek/proprietary/external/ufs_util"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/external/voicecommand" path="vendor/mediatek/proprietary/external/voicecommand"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/vorbisenc" path="vendor/mediatek/proprietary/external/vorbisenc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/wlan_assistant" path="vendor/mediatek/proprietary/external/wlan_assistant"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/wma/wmadec" path="vendor/mediatek/proprietary/external/wma/wmadec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/external/wma/wmaprodec" path="vendor/mediatek/proprietary/external/wma/wmaprodec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/factory" path="vendor/mediatek/proprietary/factory"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/av/AVEnhance" path="vendor/mediatek/proprietary/frameworks/av/AVEnhance"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/av/drm" path="vendor/mediatek/proprietary/frameworks/av/drm"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/av/media/libmtkplayer" path="vendor/mediatek/proprietary/frameworks/av/media/libmtkplayer"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/av/media/libs" path="vendor/mediatek/proprietary/frameworks/av/media/libs"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/av/services/mmsdk" path="vendor/mediatek/proprietary/frameworks/av/services/mmsdk"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/av/smartplatform_spmlib" path="vendor/mediatek/proprietary/frameworks/av/smartplatform_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/base" path="vendor/mediatek/proprietary/frameworks/base"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/common" path="vendor/mediatek/proprietary/frameworks/common"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/ex" path="vendor/mediatek/proprietary/frameworks/ex"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/ml" path="vendor/mediatek/proprietary/frameworks/ml"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/native/include" path="vendor/mediatek/proprietary/frameworks/native/include"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/neuropilot/np_core" path="vendor/mediatek/proprietary/frameworks/neuropilot/np_core"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/neuropilot/np_sys_libs" path="vendor/mediatek/proprietary/frameworks/neuropilot/np_sys_libs"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/neuropilot/np_tflite" path="vendor/mediatek/proprietary/frameworks/neuropilot/np_tflite"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/neuropilot/np_utils_spmlib" path="vendor/mediatek/proprietary/frameworks/neuropilot/np_utils_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/ThermalSignalReceiver" path="vendor/mediatek/proprietary/frameworks/opt/ThermalSignalReceiver"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/opt/agps_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/agps_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/amplus" path="vendor/mediatek/proprietary/frameworks/opt/amplus"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/appluginmanager" path="vendor/mediatek/proprietary/frameworks/opt/appluginmanager"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/atci_service" path="vendor/mediatek/proprietary/frameworks/opt/atci_service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/opt/atcid" path="vendor/mediatek/proprietary/frameworks/opt/atcid"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/batterywarning" path="vendor/mediatek/proprietary/frameworks/opt/batterywarning"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/cta" path="vendor/mediatek/proprietary/frameworks/opt/cta"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/datetimepicker" path="vendor/mediatek/proprietary/frameworks/opt/datetimepicker"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/duraspeed_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/duraspeed_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/ims-base" path="vendor/mediatek/proprietary/frameworks/opt/ims-base"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/ipo" path="vendor/mediatek/proprietary/frameworks/opt/ipo"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/jpe_lib" path="vendor/mediatek/proprietary/frameworks/opt/jpe_lib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/libimsma" path="vendor/mediatek/proprietary/frameworks/opt/libimsma"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/libimsma_bin_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/libimsma_bin_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/mdlogger_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/mdlogger_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/mdm_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/mdm_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/mdml_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/mdml_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/mediatekemail" path="vendor/mediatek/proprietary/frameworks/opt/mediatekemail"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/memoryDumpEncoder_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/memoryDumpEncoder_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/msync-lib" path="vendor/mediatek/proprietary/frameworks/opt/msync-lib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/net/ims" path="vendor/mediatek/proprietary/frameworks/opt/net/ims"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/net/services" path="vendor/mediatek/proprietary/frameworks/opt/net/services"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/nfc" path="vendor/mediatek/proprietary/frameworks/opt/nfc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/opt/simmelock" path="vendor/mediatek/proprietary/frameworks/opt/simmelock"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/subsidylock" path="vendor/mediatek/proprietary/frameworks/opt/subsidylock"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/tatfcommon" path="vendor/mediatek/proprietary/frameworks/opt/tatfcommon"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/telecomm" path="vendor/mediatek/proprietary/frameworks/opt/telecomm"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/telephony" path="vendor/mediatek/proprietary/frameworks/opt/telephony"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/telephony-base" path="vendor/mediatek/proprietary/frameworks/opt/telephony-base"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/timezonepicker" path="vendor/mediatek/proprietary/frameworks/opt/timezonepicker"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/vcard" path="vendor/mediatek/proprietary/frameworks/opt/vcard"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/vtservice" path="vendor/mediatek/proprietary/frameworks/opt/vtservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/frameworks/opt/vtservice_hidl" path="vendor/mediatek/proprietary/frameworks/opt/vtservice_hidl"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/wapicertstore_spmlib" path="vendor/mediatek/proprietary/frameworks/opt/wapicertstore_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/frameworks/opt/wbxml" path="vendor/mediatek/proprietary/frameworks/opt/wbxml"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/doc" path="vendor/mediatek/proprietary/geniezone/doc"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/external/gp" path="vendor/mediatek/proprietary/geniezone/external/gp"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/external/uree" path="vendor/mediatek/proprietary/geniezone/external/uree"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/module/mkp" path="vendor/mediatek/proprietary/geniezone/module/mkp"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/module/unmap2" path="vendor/mediatek/proprietary/geniezone/module/unmap2"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/np_security/app/neuron" path="vendor/mediatek/proprietary/geniezone/np_security/app/neuron"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/np_security/platform" path="vendor/mediatek/proprietary/geniezone/np_security/platform"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/np_security/prebuilts" path="vendor/mediatek/proprietary/geniezone/np_security/prebuilts"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/np_security/source/runtime" path="vendor/mediatek/proprietary/geniezone/np_security/source/runtime"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/np_security/utils" path="vendor/mediatek/proprietary/geniezone/np_security/utils"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/prebuilts/libs" path="vendor/mediatek/proprietary/geniezone/prebuilts/libs"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/rkp/app/sample" path="vendor/mediatek/proprietary/geniezone/rkp/app/sample"/>
      <project groups="mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/rkp/inc" path="vendor/mediatek/proprietary/geniezone/rkp/inc"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/app/gz-test" path="vendor/mediatek/proprietary/geniezone/trusty/app/gz-test"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/app/sample" path="vendor/mediatek/proprietary/geniezone/trusty/app/sample"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/app/storage" path="vendor/mediatek/proprietary/geniezone/trusty/app/storage"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/app/storage-unittest" path="vendor/mediatek/proprietary/geniezone/trusty/app/storage-unittest"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/device/arm/mediatek" path="vendor/mediatek/proprietary/geniezone/trusty/device/arm/mediatek">
        <copyfile dest="vendor/mediatek/proprietary/geniezone/Makefile" src="build/Makefile"/>
        <copyfile dest="vendor/mediatek/proprietary/geniezone/Android.mk" src="build/Android.mk"/>
      </project>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/lib" path="vendor/mediatek/proprietary/geniezone/trusty/lib"/>
      <project groups="geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase,mtk-vnd-vext" name="alps/vendor/mediatek/proprietary/geniezone/trusty/vendor/mediatek/secure_spmlib" path="vendor/mediatek/proprietary/geniezone/trusty/vendor/mediatek/secure_spmlib"/>
      <project groups="geniezone,mtk-geniezone,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/geniezone/trusty/vendor/mediatek/source" path="vendor/mediatek/proprietary/geniezone/trusty/vendor/mediatek/source"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/MMAgent" path="vendor/mediatek/proprietary/hardware/MMAgent"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/NetworkObservationSystem_spmlib" path="vendor/mediatek/proprietary/hardware/NetworkObservationSystem_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/aal" path="vendor/mediatek/proprietary/hardware/aal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/aihub/apuware_hidl_server" path="vendor/mediatek/proprietary/hardware/aihub/apuware_hidl_server"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/aihub/vpu_server" path="vendor/mediatek/proprietary/hardware/aihub/vpu_server"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/aov/license_spmlib" path="vendor/mediatek/proprietary/hardware/aov/license_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/apusys" path="vendor/mediatek/proprietary/hardware/apusys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/asrc_demo" path="vendor/mediatek/proprietary/hardware/asrc_demo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/audio" path="vendor/mediatek/proprietary/hardware/audio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/audio_remote_submix" path="vendor/mediatek/proprietary/hardware/audio_remote_submix"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/bip" path="vendor/mediatek/proprietary/hardware/bip"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/bootctl_service" path="vendor/mediatek/proprietary/hardware/bootctl_service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/bootctrl" path="vendor/mediatek/proprietary/hardware/bootctrl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/bwc" path="vendor/mediatek/proprietary/hardware/bwc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/c2kril" path="vendor/mediatek/proprietary/hardware/c2kril"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/ccci" path="vendor/mediatek/proprietary/hardware/ccci"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/ccci_sys" path="vendor/mediatek/proprietary/hardware/ccci_sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/cmdl_lib" path="vendor/mediatek/proprietary/hardware/cmdl_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/bluetooth/bluetooth_audio" path="vendor/mediatek/proprietary/hardware/connectivity/bluetooth/bluetooth_audio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/bluetooth/driver/mt66xx" path="vendor/mediatek/proprietary/hardware/connectivity/bluetooth/driver/mt66xx"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/bluetooth/driver/mt76xx" path="vendor/mediatek/proprietary/hardware/connectivity/bluetooth/driver/mt76xx"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/bluetooth/service" path="vendor/mediatek/proprietary/hardware/connectivity/bluetooth/service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/combo_tool" path="vendor/mediatek/proprietary/hardware/connectivity/combo_tool"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/connfem" path="vendor/mediatek/proprietary/hardware/connectivity/connfem"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/firmware/gps_fw_spmlib" path="vendor/mediatek/proprietary/hardware/connectivity/firmware/gps_fw_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/firmware/rom_patch" path="vendor/mediatek/proprietary/hardware/connectivity/firmware/rom_patch"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/firmware/wlan" path="vendor/mediatek/proprietary/hardware/connectivity/firmware/wlan"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/fmradio" path="vendor/mediatek/proprietary/hardware/connectivity/fmradio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/gnss/gnss_hal" path="vendor/mediatek/proprietary/hardware/connectivity/gnss/gnss_hal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/gnss/mnld" path="vendor/mediatek/proprietary/hardware/connectivity/gnss/mnld"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/connectivity/gps_sys" path="vendor/mediatek/proprietary/hardware/connectivity/gps_sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/wapi-v2_spmlib" path="vendor/mediatek/proprietary/hardware/connectivity/wapi-v2_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/connectivity/wlan" path="vendor/mediatek/proprietary/hardware/connectivity/wlan"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/consumerir" path="vendor/mediatek/proprietary/hardware/consumerir"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/contexthub" path="vendor/mediatek/proprietary/hardware/contexthub"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/cpu_nn_lib" path="vendor/mediatek/proprietary/hardware/cpu_nn_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/dpframework" path="vendor/mediatek/proprietary/hardware/dpframework"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/dpframework_prot_spmlib" path="vendor/mediatek/proprietary/hardware/dpframework_prot_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/dpm_spm_imgs/mt6991" path="vendor/mediatek/proprietary/hardware/dpm_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/drvb_lib" path="vendor/mediatek/proprietary/hardware/drvb_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/em_hidl_server" path="vendor/mediatek/proprietary/hardware/em_hidl_server"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/external" path="vendor/mediatek/proprietary/hardware/external"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/fastbootd" path="vendor/mediatek/proprietary/hardware/fastbootd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/fbconfig_tool" path="vendor/mediatek/proprietary/hardware/fbconfig_tool"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/fingerprint" path="vendor/mediatek/proprietary/hardware/fingerprint"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/formatter" path="vendor/mediatek/proprietary/hardware/formatter"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/fstab" path="vendor/mediatek/proprietary/hardware/fstab"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gatekeeper" path="vendor/mediatek/proprietary/hardware/gatekeeper"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_aidl_spmlib" path="vendor/mediatek/proprietary/hardware/gpu_aidl_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_aux" path="vendor/mediatek/proprietary/hardware/gpu_aux"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_ext_spmlib" path="vendor/mediatek/proprietary/hardware/gpu_ext_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_hidl" path="vendor/mediatek/proprietary/hardware/gpu_hidl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_img" path="vendor/mediatek/proprietary/hardware/gpu_img"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_mali_lib" path="vendor/mediatek/proprietary/hardware/gpu_mali_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_nn_lib" path="vendor/mediatek/proprietary/hardware/gpu_nn_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpu_rtsdk_lib_init" path="vendor/mediatek/proprietary/hardware/gpu_rtsdk_lib_init"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gpuserv" path="vendor/mediatek/proprietary/hardware/gpuserv"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gralloc_extra" path="vendor/mediatek/proprietary/hardware/gralloc_extra"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/gsm0710muxd" path="vendor/mediatek/proprietary/hardware/gsm0710muxd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/hal_aee" path="vendor/mediatek/proprietary/hardware/hal_aee"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/hdmi" path="vendor/mediatek/proprietary/hardware/hdmi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/hwcomposer" path="vendor/mediatek/proprietary/hardware/hwcomposer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/ims" path="vendor/mediatek/proprietary/hardware/ims"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/imsa" path="vendor/mediatek/proprietary/hardware/imsa"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/interfaces" path="vendor/mediatek/proprietary/hardware/interfaces"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/jpeg" path="vendor/mediatek/proprietary/hardware/jpeg"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/jpeg_sys" path="vendor/mediatek/proprietary/hardware/jpeg_sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/keyinstall" path="vendor/mediatek/proprietary/hardware/keyinstall"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/keymanage" path="vendor/mediatek/proprietary/hardware/keymanage"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/lbs/geofence_hal" path="vendor/mediatek/proprietary/hardware/lbs/geofence_hal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/lbs/gnss_service" path="vendor/mediatek/proprietary/hardware/lbs/gnss_service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/lbs/mtk_flp_hal" path="vendor/mediatek/proprietary/hardware/lbs/mtk_flp_hal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/lbs/mtk_gnss_service" path="vendor/mediatek/proprietary/hardware/lbs/mtk_gnss_service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/lbs/mtk_lbs_service" path="vendor/mediatek/proprietary/hardware/lbs/mtk_lbs_service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libaal_sec_spmlib" path="vendor/mediatek/proprietary/hardware/libaal_sec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libc2/audio" path="vendor/mediatek/proprietary/hardware/libc2/audio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libc2/fsr" path="vendor/mediatek/proprietary/hardware/libc2/fsr"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libc2/service" path="vendor/mediatek/proprietary/hardware/libc2/service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libc2/video" path="vendor/mediatek/proprietary/hardware/libc2/video"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libc2/wma/wma_spmlib" path="vendor/mediatek/proprietary/hardware/libc2/wma/wma_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcam_hal/libmgr_spmlib" path="vendor/mediatek/proprietary/hardware/libcam_hal/libmgr_spmlib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libae_core_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libae_core_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libae_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libae_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libae_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libae_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_core_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_core_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaf_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaiawb_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaiawb_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaiseg_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaiseg_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaishutter_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaishutter_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libaitracking_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libaitracking_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_core_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_core_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libawb_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libawbsync_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libawbsync_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libccu_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libccu_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libcommon_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libcommon_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libdce_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libdce_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libflash_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libflash_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libflash_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libflash_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libflicker_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libflicker_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libgma_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libgma_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libhlr_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libhlr_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/liblce_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/liblce_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libltm_v2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libltm_v2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libn3d3a_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libn3d3a_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libpd_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libpd_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_3a/libtnc_lib" path="vendor/mediatek/proprietary/hardware/libcamera_3a/libtnc_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libcamera_drv_spmlib" path="vendor/mediatek/proprietary/hardware/libcamera_drv_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_ext" path="vendor/mediatek/proprietary/hardware/libcamera_ext"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/lib3dnr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/lib3dnr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libInlineCompGyro_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libInlineCompGyro_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libabf_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libabf_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libaics_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libaics_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libaidepth_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libaidepth_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libaifr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libaifr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libaihdr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libaihdr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libainr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libainr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libais2_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libais2_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libautorama_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libautorama_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libc3d_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libc3d_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libcac_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libcac_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libcfb_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libcfb_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libdngop_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libdngop_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libeis_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libeis_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libfdft_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libfdft_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libflicker_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libflicker_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libfsc_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libfsc_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libfus_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libfus_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libgyro_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libgyro_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libhdr10p_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libhdr10p_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libhdr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libhdr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libispfeature_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libispfeature_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/liblsc_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/liblsc_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libmcnr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libmcnr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libmfnr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libmfnr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libmtkltm_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libmtkltm_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libn3d_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libn3d_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libnr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libnr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libplatform_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libplatform_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/librefocus_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/librefocus_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libsat_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libsat_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libstereocam_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libstereocam_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libswtcc_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libswtcc_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libvaidepth_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libvaidepth_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libvainr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libvainr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libvpunr_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libvpunr_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/libvsf_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/libvsf_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libcamera_feature/ultrahdrmtk_lib" path="vendor/mediatek/proprietary/hardware/libcamera_feature/ultrahdrmtk_lib"/>
      <project groups="libcam,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libcamera_lib" path="vendor/mediatek/proprietary/hardware/libcamera_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libdnn_feature" path="vendor/mediatek/proprietary/hardware/libdnn_feature"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-sys-vnd,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libgem" path="vendor/mediatek/proprietary/hardware/libgem"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libheichal" path="vendor/mediatek/proprietary/hardware/libheichal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libhwcomposer" path="vendor/mediatek/proprietary/hardware/libhwcomposer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libkmsetkey" path="vendor/mediatek/proprietary/hardware/libkmsetkey"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/liblights" path="vendor/mediatek/proprietary/hardware/liblights"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libaal_key_lib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libaal_key_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libaispq_spmlib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libaispq_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libblulight_defender_lib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libblulight_defender_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libchameleon_lib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libchameleon_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libdre_lib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libdre_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmiravision_feature/libscltm_lib" path="vendor/mediatek/proprietary/hardware/libmiravision_feature/libscltm_lib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/libmsync2_0" path="vendor/mediatek/proprietary/hardware/libmsync2_0"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libmtk_bsg" path="vendor/mediatek/proprietary/hardware/libmtk_bsg"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libpq_sec_spmlib" path="vendor/mediatek/proprietary/hardware/libpq_sec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libvcodec_spmlib" path="vendor/mediatek/proprietary/hardware/libvcodec_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libvcodec_up_spmlib" path="vendor/mediatek/proprietary/hardware/libvcodec_up_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/libvibrator" path="vendor/mediatek/proprietary/hardware/libvibrator"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/m4u" path="vendor/mediatek/proprietary/hardware/m4u"/>
      <project groups="mbrainimpl_private,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mbrainimpl_private_spmlib" path="vendor/mediatek/proprietary/hardware/mbrainimpl_private_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mcupmfw/build" path="vendor/mediatek/proprietary/hardware/mcupmfw/build"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mddp" path="vendor/mediatek/proprietary/hardware/mddp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/memtrack" path="vendor/mediatek/proprietary/hardware/memtrack"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vnd-codebase,mtk-vnd-vext" name="alps/vendor/mediatek/proprietary/hardware/meow_spmlib" path="vendor/mediatek/proprietary/hardware/meow_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/meta" path="vendor/mediatek/proprietary/hardware/meta"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/miravision" path="vendor/mediatek/proprietary/hardware/miravision"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mmlpq" path="vendor/mediatek/proprietary/hardware/mmlpq"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mmservice" path="vendor/mediatek/proprietary/hardware/mmservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtk-hidl-service" path="vendor/mediatek/proprietary/hardware/mtk-hidl-service"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkavm/apps" path="vendor/mediatek/proprietary/hardware/mtkavm/apps"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkavm/libalgo" path="vendor/mediatek/proprietary/hardware/mtkavm/libalgo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkavm/services" path="vendor/mediatek/proprietary/hardware/mtkavm/services"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkcam" path="vendor/mediatek/proprietary/hardware/mtkcam"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkcam-sched_spmlib" path="vendor/mediatek/proprietary/hardware/mtkcam-sched_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkcam-turbo-android" path="vendor/mediatek/proprietary/hardware/mtkcam-turbo-android"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mtkcam3" path="vendor/mediatek/proprietary/hardware/mtkcam3"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/muxreport" path="vendor/mediatek/proprietary/hardware/muxreport"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mvpu_algo" path="vendor/mediatek/proprietary/hardware/mvpu_algo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mvpu_runtime_spmlib" path="vendor/mediatek/proprietary/hardware/mvpu_runtime_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/mvpu_spmlib" path="vendor/mediatek/proprietary/hardware/mvpu_spmlib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/mvpu_ssdk" path="vendor/mediatek/proprietary/hardware/mvpu_ssdk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/netagent" path="vendor/mediatek/proprietary/hardware/netagent"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/nwk_opt" path="vendor/mediatek/proprietary/hardware/nwk_opt"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/omadm" path="vendor/mediatek/proprietary/hardware/omadm"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/power" path="vendor/mediatek/proprietary/hardware/power"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/power_applist" path="vendor/mediatek/proprietary/hardware/power_applist"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-util,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/power_util" path="vendor/mediatek/proprietary/hardware/power_util"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/pq" path="vendor/mediatek/proprietary/hardware/pq"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/pqframework" path="vendor/mediatek/proprietary/hardware/pqframework"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/pqselector_spmlib" path="vendor/mediatek/proprietary/hardware/pqselector_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/ril" path="vendor/mediatek/proprietary/hardware/ril"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/seclib" path="vendor/mediatek/proprietary/hardware/seclib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/secure_element" path="vendor/mediatek/proprietary/hardware/secure_element"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/sensor" path="vendor/mediatek/proprietary/hardware/sensor"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/sfservice" path="vendor/mediatek/proprietary/hardware/sfservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/smartpa" path="vendor/mediatek/proprietary/hardware/smartpa"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/soundtrigger" path="vendor/mediatek/proprietary/hardware/soundtrigger"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/spmfw/build" path="vendor/mediatek/proprietary/hardware/spmfw/build"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/spmfw/mt6991" path="vendor/mediatek/proprietary/hardware/spmfw/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/surfaceengine" path="vendor/mediatek/proprietary/hardware/surfaceengine"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/hardware/terservice" path="vendor/mediatek/proprietary/hardware/terservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/thermal" path="vendor/mediatek/proprietary/hardware/thermal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/usb" path="vendor/mediatek/proprietary/hardware/usb"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/vcodec" path="vendor/mediatek/proprietary/hardware/vcodec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/vpu" path="vendor/mediatek/proprietary/hardware/vpu"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/vpu_nn_algo" path="vendor/mediatek/proprietary/hardware/vpu_nn_algo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/wpfa" path="vendor/mediatek/proprietary/hardware/wpfa"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/hardware/wpfa_sw_relay" path="vendor/mediatek/proprietary/hardware/wpfa_sw_relay"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/ncc/nann_lib" path="vendor/mediatek/proprietary/ncc/nann_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/ncc/ncc_lib" path="vendor/mediatek/proprietary/ncc/ncc_lib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP01" path="vendor/mediatek/proprietary/operator/SPEC/OP01"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP02" path="vendor/mediatek/proprietary/operator/SPEC/OP02"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP03" path="vendor/mediatek/proprietary/operator/SPEC/OP03"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP05" path="vendor/mediatek/proprietary/operator/SPEC/OP05"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP06" path="vendor/mediatek/proprietary/operator/SPEC/OP06"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP07" path="vendor/mediatek/proprietary/operator/SPEC/OP07"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP08" path="vendor/mediatek/proprietary/operator/SPEC/OP08"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP09" path="vendor/mediatek/proprietary/operator/SPEC/OP09"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP11" path="vendor/mediatek/proprietary/operator/SPEC/OP11"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP112" path="vendor/mediatek/proprietary/operator/SPEC/OP112"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP12" path="vendor/mediatek/proprietary/operator/SPEC/OP12"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP129" path="vendor/mediatek/proprietary/operator/SPEC/OP129"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP15" path="vendor/mediatek/proprietary/operator/SPEC/OP15"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP16" path="vendor/mediatek/proprietary/operator/SPEC/OP16"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP18" path="vendor/mediatek/proprietary/operator/SPEC/OP18"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP20" path="vendor/mediatek/proprietary/operator/SPEC/OP20"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/SPEC/OP236" path="vendor/mediatek/proprietary/operator/SPEC/OP236"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/common" path="vendor/mediatek/proprietary/operator/common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/external/volte_clientapi/Common" path="vendor/mediatek/proprietary/operator/external/volte_clientapi/Common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/external/volte_rcs/Common" path="vendor/mediatek/proprietary/operator/external/volte_rcs/Common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/external/volte_rcs_ril/Common" path="vendor/mediatek/proprietary/operator/external/volte_rcs_ril/Common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/external/volte_rcs_stack_lib" path="vendor/mediatek/proprietary/operator/external/volte_rcs_stack_lib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/external/volte_uce/Common" path="vendor/mediatek/proprietary/operator/external/volte_uce/Common"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/frameworks/bootanimation" path="vendor/mediatek/proprietary/operator/frameworks/bootanimation"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/frameworks/ims" path="vendor/mediatek/proprietary/operator/frameworks/ims"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/frameworks/telephony" path="vendor/mediatek/proprietary/operator/frameworks/telephony"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/frameworks/wifi" path="vendor/mediatek/proprietary/operator/frameworks/wifi"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/hardware/interfaces" path="vendor/mediatek/proprietary/operator/hardware/interfaces"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/operator/hardware/ril" path="vendor/mediatek/proprietary/operator/hardware/ril"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Browser" path="vendor/mediatek/proprietary/operator/packages/apps/Browser"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Dialer" path="vendor/mediatek/proprietary/operator/packages/apps/Dialer"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/ImsPco" path="vendor/mediatek/proprietary/operator/packages/apps/ImsPco"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Launcher3" path="vendor/mediatek/proprietary/operator/packages/apps/Launcher3"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Mms" path="vendor/mediatek/proprietary/operator/packages/apps/Mms"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Music" path="vendor/mediatek/proprietary/operator/packages/apps/Music"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Omacp" path="vendor/mediatek/proprietary/operator/packages/apps/Omacp"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Presence/Common" path="vendor/mediatek/proprietary/operator/packages/apps/Presence/Common"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/RCSe/Common" path="vendor/mediatek/proprietary/operator/packages/apps/RCSe/Common"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Settings" path="vendor/mediatek/proprietary/operator/packages/apps/Settings"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/SystemUI" path="vendor/mediatek/proprietary/operator/packages/apps/SystemUI"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Wallpaper" path="vendor/mediatek/proprietary/operator/packages/apps/Wallpaper"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/apps/Wo3G" path="vendor/mediatek/proprietary/operator/packages/apps/Wo3G"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/Entitlement" path="vendor/mediatek/proprietary/operator/packages/services/Entitlement"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/Ims" path="vendor/mediatek/proprietary/operator/packages/services/Ims"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/Telecomm" path="vendor/mediatek/proprietary/operator/packages/services/Telecomm"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/Telephony" path="vendor/mediatek/proprietary/operator/packages/services/Telephony"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/WifiOffload" path="vendor/mediatek/proprietary/operator/packages/services/WifiOffload"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/operator/packages/services/rsu" path="vendor/mediatek/proprietary/operator/packages/services/rsu"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/ATMWifiMeta" path="vendor/mediatek/proprietary/packages/apps/ATMWifiMeta"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/AlwaysOnDisplay" path="vendor/mediatek/proprietary/packages/apps/AlwaysOnDisplay"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/AutoDialer" path="vendor/mediatek/proprietary/packages/apps/AutoDialer"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/BSPTelephonyDevTool" path="vendor/mediatek/proprietary/packages/apps/BSPTelephonyDevTool"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/BatteryWarning" path="vendor/mediatek/proprietary/packages/apps/BatteryWarning"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Calendar" path="vendor/mediatek/proprietary/packages/apps/Calendar"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Camera2" path="vendor/mediatek/proprietary/packages/apps/Camera2"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/CarBTDemo" path="vendor/mediatek/proprietary/packages/apps/CarBTDemo"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/CarcorderMcamDemo" path="vendor/mediatek/proprietary/packages/apps/CarcorderMcamDemo"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/CdsInfo" path="vendor/mediatek/proprietary/packages/apps/CdsInfo"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Contacts" path="vendor/mediatek/proprietary/packages/apps/Contacts"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/DeskClock" path="vendor/mediatek/proprietary/packages/apps/DeskClock"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Dialer" path="vendor/mediatek/proprietary/packages/apps/Dialer"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/DuraSpeed" path="vendor/mediatek/proprietary/packages/apps/DuraSpeed"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/packages/apps/EmCamera" path="vendor/mediatek/proprietary/packages/apps/EmCamera"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Email" path="vendor/mediatek/proprietary/packages/apps/Email"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/EmergencyInfo" path="vendor/mediatek/proprietary/packages/apps/EmergencyInfo"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/EngineerMode" path="vendor/mediatek/proprietary/packages/apps/EngineerMode"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/FMRadio" path="vendor/mediatek/proprietary/packages/apps/FMRadio"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/FullscreenMode" path="vendor/mediatek/proprietary/packages/apps/FullscreenMode"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/GnssVisibilityControl" path="vendor/mediatek/proprietary/packages/apps/GnssVisibilityControl"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Launcher3" path="vendor/mediatek/proprietary/packages/apps/Launcher3"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/LocationEM2" path="vendor/mediatek/proprietary/packages/apps/LocationEM2"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MTKLogger" path="vendor/mediatek/proprietary/packages/apps/MTKLogger"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MdmConfig" path="vendor/mediatek/proprietary/packages/apps/MdmConfig"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MdmlSample" path="vendor/mediatek/proprietary/packages/apps/MdmlSample"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MiraVision" path="vendor/mediatek/proprietary/packages/apps/MiraVision"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Mms" path="vendor/mediatek/proprietary/packages/apps/Mms"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MtkSettings" path="vendor/mediatek/proprietary/packages/apps/MtkSettings"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MultiStatusBar" path="vendor/mediatek/proprietary/packages/apps/MultiStatusBar"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/MutiPlayer" path="vendor/mediatek/proprietary/packages/apps/MutiPlayer"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Omacp" path="vendor/mediatek/proprietary/packages/apps/Omacp"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/QuickSearchBox" path="vendor/mediatek/proprietary/packages/apps/QuickSearchBox"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/packages/apps/SensorHub" path="vendor/mediatek/proprietary/packages/apps/SensorHub"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/SettingsLib" path="vendor/mediatek/proprietary/packages/apps/SettingsLib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/SettingsProvider" path="vendor/mediatek/proprietary/packages/apps/SettingsProvider"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/Stk" path="vendor/mediatek/proprietary/packages/apps/Stk"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/SystemUI" path="vendor/mediatek/proprietary/packages/apps/SystemUI"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/VoiceAssistantAdaptor_lib" path="vendor/mediatek/proprietary/packages/apps/VoiceAssistantAdaptor_lib"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/VoiceCommand" path="vendor/mediatek/proprietary/packages/apps/VoiceCommand"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/VoiceUnlock" path="vendor/mediatek/proprietary/packages/apps/VoiceUnlock"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/WallpaperPicker" path="vendor/mediatek/proprietary/packages/apps/WallpaperPicker"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/WapiCertManager" path="vendor/mediatek/proprietary/packages/apps/WapiCertManager"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/YGPS" path="vendor/mediatek/proprietary/packages/apps/YGPS"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/apps/agpsInterface" path="vendor/mediatek/proprietary/packages/apps/agpsInterface"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/inputmethods/MtkLatinIME" path="vendor/mediatek/proprietary/packages/inputmethods/MtkLatinIME"/>
      <project groups="mtk-aosp,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/packages/modules/Bluetooth" path="vendor/mediatek/proprietary/packages/modules/Bluetooth"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/modules/Permission" path="vendor/mediatek/proprietary/packages/modules/Permission"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/overlay/system" path="vendor/mediatek/proprietary/packages/overlay/system"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/packages/overlay/vendor" path="vendor/mediatek/proprietary/packages/overlay/vendor"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/providers/CalendarProvider" path="vendor/mediatek/proprietary/packages/providers/CalendarProvider"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/providers/ContactsProvider" path="vendor/mediatek/proprietary/packages/providers/ContactsProvider"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/providers/TelephonyProvider" path="vendor/mediatek/proprietary/packages/providers/TelephonyProvider"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/AtciService" path="vendor/mediatek/proprietary/packages/services/AtciService"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/CallRecorderService" path="vendor/mediatek/proprietary/packages/services/CallRecorderService"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/CapCtrl" path="vendor/mediatek/proprietary/packages/services/CapCtrl"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/Car" path="vendor/mediatek/proprietary/packages/services/Car"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/CarrierExpress" path="vendor/mediatek/proprietary/packages/services/CarrierExpress"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/GbaService" path="vendor/mediatek/proprietary/packages/services/GbaService"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/GnssDebugReport" path="vendor/mediatek/proprietary/packages/services/GnssDebugReport"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/Ims" path="vendor/mediatek/proprietary/packages/services/Ims"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/LPPeService" path="vendor/mediatek/proprietary/packages/services/LPPeService"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/Mms" path="vendor/mediatek/proprietary/packages/services/Mms"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/MtkGeofence" path="vendor/mediatek/proprietary/packages/services/MtkGeofence"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/SmartPower_spmlib" path="vendor/mediatek/proprietary/packages/services/SmartPower_spmlib"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/Telecomm" path="vendor/mediatek/proprietary/packages/services/Telecomm"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/Telephony" path="vendor/mediatek/proprietary/packages/services/Telephony"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/WifiOffload" path="vendor/mediatek/proprietary/packages/services/WifiOffload"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/packages/services/WifiOffload_legacy_hidl" path="vendor/mediatek/proprietary/packages/services/WifiOffload_legacy_hidl"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/apm_service" path="vendor/mediatek/proprietary/packages/services/apm_service"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/packages/services/vmshareservice" path="vendor/mediatek/proprietary/packages/services/vmshareservice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vext,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/releaseinfo_spmlib" path="vendor/mediatek/proprietary/releaseinfo_spmlib"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/scripts" path="vendor/mediatek/proprietary/scripts"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/secureprocessor/hwrot_spm_imgs/mt6991" path="vendor/mediatek/proprietary/secureprocessor/hwrot_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/secureprocessor/ise" path="vendor/mediatek/proprietary/secureprocessor/ise"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/system/core/xflash" path="vendor/mediatek/proprietary/system/core/xflash"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/system/netdagent" path="vendor/mediatek/proprietary/system/netdagent"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/HIFI4/drivers" path="vendor/mediatek/proprietary/tinysys/adsp/HIFI4/drivers"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/HIFI4/middleware" path="vendor/mediatek/proprietary/tinysys/adsp/HIFI4/middleware"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/HIFI5_SP" path="vendor/mediatek/proprietary/tinysys/adsp/HIFI5_SP"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/adsp_imgs/HIFI5" path="vendor/mediatek/proprietary/tinysys/adsp/adsp_imgs/HIFI5"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/adsp_imgs_auto" path="vendor/mediatek/proprietary/tinysys/adsp/adsp_imgs_auto"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/common" path="vendor/mediatek/proprietary/tinysys/adsp/common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/adsp/riscv" path="vendor/mediatek/proprietary/tinysys/adsp/riscv"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/apusys_spm_imgs/mt6991" path="vendor/mediatek/proprietary/tinysys/apusys_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/common" path="vendor/mediatek/proprietary/tinysys/common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/gpueb_spm_imgs/mt6991" path="vendor/mediatek/proprietary/tinysys/gpueb_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/kernel/FreeRTOS_v10.1.0" path="vendor/mediatek/proprietary/tinysys/kernel/FreeRTOS_v10.1.0"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/kernel/FreeRTOS_v10.1.0.1" path="vendor/mediatek/proprietary/tinysys/kernel/FreeRTOS_v10.1.0.1"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/mcupm_spm_imgs/mt6991" path="vendor/mediatek/proprietary/tinysys/mcupm_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/sap" path="vendor/mediatek/proprietary/tinysys/sap"/>
      <project groups="mtk-pure-vnd-codebase,mtk-tinysys,mtk-vext,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/scp" path="vendor/mediatek/proprietary/tinysys/scp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/sspm_spm_imgs/mt6991" path="vendor/mediatek/proprietary/tinysys/sspm_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tinysys/vcp_spm_imgs/mt6991" path="vendor/mediatek/proprietary/tinysys/vcp_spm_imgs/mt6991"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tools/dct" path="vendor/mediatek/proprietary/tools/dct"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/tools/ptgen" path="vendor/mediatek/proprietary/tools/ptgen"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vext,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/atf" path="vendor/mediatek/proprietary/trustzone/atf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vnd-codebase,mtk-vnd-vext" name="alps/vendor/mediatek/proprietary/trustzone/common/hal/secure_spmlib" path="vendor/mediatek/proprietary/trustzone/common/hal/secure_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/common/hal/source" path="vendor/mediatek/proprietary/trustzone/common/hal/source"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/custom" path="vendor/mediatek/proprietary/trustzone/custom"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/microtrust/source" path="vendor/mediatek/proprietary/trustzone/microtrust/source"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/optee/3.18.0/optee_client" path="vendor/mediatek/proprietary/trustzone/optee/3.18.0/optee_client"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/optee/3.18.0/secure_spmlib" path="vendor/mediatek/proprietary/trustzone/optee/3.18.0/secure_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/sdf/sdfhw_spmlib" path="vendor/mediatek/proprietary/trustzone/sdf/sdfhw_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/sdf/tee_sdf_spmlib" path="vendor/mediatek/proprietary/trustzone/sdf/tee_sdf_spmlib"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/trustkernel/source" path="vendor/mediatek/proprietary/trustzone/trustkernel/source"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/proprietary/trustzone/trustonic/hardware/interfaces" path="vendor/mediatek/proprietary/trustzone/trustonic/hardware/interfaces"/>
      <project groups="mtk-sys-codebase,mtk-system" name="alps/vendor/mediatek/proprietary/trustzone/trustonic/system_app" path="vendor/mediatek/proprietary/trustzone/trustonic/system_app"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="alps/vendor/mediatek/widevine_spmlib" path="vendor/mediatek/widevine_spmlib"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk,yukawa" name="device/amlogic/yukawa"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk,yukawa" name="device/amlogic/yukawa-kernel"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,pdk-cw-fs" name="device/common"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/armv7-a-neon"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/art"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/car"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/common"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/goldfish"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/goldfish-opengl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/lionhead"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/mini-emulator-arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/mini-emulator-armv7-a-neon"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/mini-emulator-x86"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/mini-emulator-x86_64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/opengl-transport"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/qemu"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/trusty"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk" name="device/generic/uml"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/vulkan-cereal"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/x86"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/generic/x86_64"/>
      <project groups="broadcom_pdk,device,generic_fs,mtk-sys-codebase,mtk-system,pdk" name="device/google/atv"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk" name="device/google/contexthub"/>
      <project groups="coral,device,generic_fs,mtk-sys-codebase,mtk-system" name="device/google/coral"/>
      <project groups="coral,device,generic_fs,mtk-sys-codebase,mtk-system" name="device/google/coral-kernel"/>
      <project groups="coral,device,generic_fs,mtk-sys-codebase,mtk-system" name="device/google/coral-sepolicy"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk" name="device/google/cuttlefish"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk" name="device/google/cuttlefish_prebuilts"/>
      <project dest-branch="master" groups="device,gull,mtk-sys-codebase,mtk-system,pdk,trout" name="device/google/trout"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/google/vrservices"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/google_car"/>
      <project groups="device,dragonboard,mtk-sys-codebase,mtk-system,pdk" name="device/linaro/dragonboard"/>
      <project groups="device,dragonboard,mtk-sys-codebase,mtk-system,pdk" name="device/linaro/dragonboard-kernel"/>
      <project groups="device,hikey,mtk-sys-codebase,mtk-system,pdk" name="device/linaro/hikey"/>
      <project groups="device,hikey,mtk-sys-codebase,mtk-system,pdk" name="device/linaro/hikey-kernel"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk,poplar" name="device/linaro/poplar"/>
      <project groups="device,mtk-sys-codebase,mtk-system,pdk,poplar" name="device/linaro/poplar-kernel"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="device/sample"/>
      <project groups="beagle_x15,device,mtk-sys-codebase,mtk-system,pdk" name="device/ti/beagle-x15" path="device/ti/beagle_x15"/>
      <project groups="beagle_x15,device,mtk-sys-codebase,mtk-system,pdk" name="device/ti/beagle-x15-kernel" path="device/ti/beagle_x15-kernel"/>
      <project groups="mtk-kernel,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="kernel/build" path="kernel/build/kernel">
        <linkfile dest="kernel/tools/bazel" src="kleaf/bazel.sh"/>
        <linkfile dest="kernel/build/build_abi.sh" src="build_abi.sh"/>
        <linkfile dest="kernel/build/build_test.sh" src="build_test.sh"/>
        <linkfile dest="kernel/build/build_utils.sh" src="build_utils.sh"/>
        <linkfile dest="kernel/build/config.sh" src="config.sh"/>
        <linkfile dest="kernel/build/envsetup.sh" src="envsetup.sh"/>
        <linkfile dest="kernel/build/_setup_env.sh" src="_setup_env.sh"/>
        <linkfile dest="kernel/build/multi-switcher.sh" src="multi-switcher.sh"/>
        <linkfile dest="kernel/build/abi" src="abi"/>
        <linkfile dest="kernel/build/static_analysis" src="static_analysis"/>
      </project>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="kernel/configs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/4.19/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.10/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.10/x86-64" path="kernel/prebuilts/5.10/x86_64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.15/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.15/x86-64" path="kernel/prebuilts/5.15/x86_64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.4/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/5.4/x86-64" path="kernel/prebuilts/5.4/x86_64"/>
      <project groups="mtk-aosp-vnd,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="kernel/prebuilts/6.1/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/6.1/x86-64" path="kernel/prebuilts/6.1/x86_64"/>
      <project groups="mtk-kernel,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="kernel/prebuilts/build-tools" path="kernel/prebuilts/kernel-build-tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/4.19/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/4.19/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.10/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.10/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.15/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.15/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.4/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/5.4/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/6.1/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/6.1/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/mainline/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/common-modules/virtual-device/mainline/x86-64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/mainline/arm64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="kernel/prebuilts/mainline/x86-64" path="kernel/prebuilts/mainline/x86_64"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="kernel/tests"/>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="neptune/bt_driver/linux_v2" path="vendor/mediatek/kernel_modules/connectivity/bt/linux_v2">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/bt/linux_v2" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="neptune/wlan_daemon/ated_ext" path="vendor/mediatek/proprietary/hardware/connectivity/wlan_rf/ated_ext"/>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="neptune/wlan_driver/gen4m" path="vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4m">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/connectivity/wlan/core/gen4m" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="neptune/wlan_tool/phy_tune" path="vendor/mediatek/proprietary/hardware/connectivity/wlan_rf/phy_tune"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="neptune/wlan_tool/wfa_cert/quicktrack" path="vendor/mediatek/proprietary/hardware/connectivity/quicktrack"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="neptune/wlan_tool/wfa_cert/sigma" path="vendor/mediatek/proprietary/hardware/connectivity/sigma"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="neptune/wlan_tool/wifi_test_tool" path="vendor/mediatek/proprietary/hardware/connectivity/wlan_rf/wifi_test_tool"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/art" path="art"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/bionic" path="bionic"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/bootable/libbootloader" path="bootable/libbootloader"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/bootable/recovery" path="bootable/recovery"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/build" path="build/make">
        <linkfile dest="build/CleanSpec.mk" src="CleanSpec.mk"/>
        <linkfile dest="build/buildspec.mk.default" src="buildspec.mk.default"/>
        <linkfile dest="build/core" src="core"/>
        <linkfile dest="build/envsetup.sh" src="envsetup.sh"/>
        <linkfile dest="build/target" src="target"/>
        <linkfile dest="build/tools" src="tools"/>
      </project>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/build/bazel" path="build/bazel">
        <linkfile dest="WORKSPACE" src="bazel.WORKSPACE"/>
        <linkfile dest="BUILD" src="bazel.BUILD"/>
      </project>
      <project groups="mtk-build-sys,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/build/bazel_common_rules" path="build/bazel_common_rules">
        <linkfile dest="kernel/build/bazel_common_rules" src="."/>
      </project>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tradefed" name="platform/build/blueprint" path="build/blueprint"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/build/orchestrator" path="build/orchestrator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/build/pesto" path="build/pesto"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tradefed" name="platform/build/soong" path="build/soong">
        <linkfile dest="Android.bp" src="root.bp"/>
        <linkfile dest="bootstrap.bash" src="bootstrap.bash"/>
      </project>
      <project groups="cts,mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/cts" path="cts"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/dalvik" path="dalvik"/>
      <project groups="developers,mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/developers/build" path="developers/build"/>
      <project groups="developers,mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/development" path="development"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/AFLplusplus" path="external/AFLplusplus"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/FP16" path="external/FP16"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/FXdiv" path="external/FXdiv"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ImageMagick" path="external/ImageMagick"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/MPAndroidChart" path="external/MPAndroidChart"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/OpenCSD" path="external/OpenCSD"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/TestParameterInjector" path="external/TestParameterInjector"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/XNNPACK" path="external/XNNPACK"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/aac" path="external/aac"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/abseil-cpp" path="external/abseil-cpp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/android-clat" path="external/android-clat"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/android-key-attestation" path="external/android-key-attestation"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/androidplot" path="external/androidplot"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/angle" path="external/angle"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ant-glob" path="external/ant-glob"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/antlr" path="external/antlr"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/apache-commons-bcel" path="external/apache-commons-bcel"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/apache-commons-compress" path="external/apache-commons-compress"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/apache-commons-io" path="external/apache-commons-io"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/apache-commons-lang" path="external/apache-commons-lang"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/apache-commons-math" path="external/apache-commons-math"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/apache-harmony" path="external/apache-harmony"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/apache-http" path="external/apache-http"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/apache-velocity-engine" path="external/apache-velocity-engine"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/apache-xml" path="external/apache-xml"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/arm-optimized-routines" path="external/arm-optimized-routines"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/arm-trusted-firmware" path="external/arm-trusted-firmware"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/auto" path="external/auto"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/autotest" path="external/autotest"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/avb" path="external/avb"/>
      <project groups="mtk-build-sys,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bazel-skylib" path="external/bazel-skylib">
        <linkfile dest="kernel/external/bazel-skylib" src="."/>
      </project>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bazelbuild-kotlin-rules" path="external/bazelbuild-kotlin-rules"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bazelbuild-rules_android" path="external/bazelbuild-rules_android"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bazelbuild-rules_license" path="external/bazelbuild-rules_license"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/bc" path="external/bc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bcc" path="external/bcc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/blktrace" path="external/blktrace"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/boringssl" path="external/boringssl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bouncycastle" path="external/bouncycastle"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/bpftool" path="external/bpftool"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/brotli" path="external/brotli"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bsdiff" path="external/bsdiff"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/bzip2" path="external/bzip2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/caliper" path="external/caliper"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/capstone" path="external/capstone"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/catch2" path="external/catch2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cblas" path="external/cblas"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cbor-java" path="external/cbor-java"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/chromium-trace" path="external/chromium-trace"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/chromium-webview" path="external/chromium-webview"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/clang" path="external/clang"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cldr" path="external/cldr"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cn-cbor" path="external/cn-cbor"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/compiler-rt" path="external/compiler-rt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/connectedappssdk" path="external/connectedappssdk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/conscrypt" path="external/conscrypt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cpu_features" path="external/cpu_features"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/cpuinfo" path="external/cpuinfo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/crcalc" path="external/crcalc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/cronet" path="external/cronet"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/crosvm" path="external/crosvm"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/curl" path="external/curl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/dagger2" path="external/dagger2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/deqp" path="external/deqp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/deqp-deps/SPIRV-Headers" path="external/deqp-deps/SPIRV-Headers"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/deqp-deps/SPIRV-Tools" path="external/deqp-deps/SPIRV-Tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/deqp-deps/amber" path="external/deqp-deps/amber"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/deqp-deps/glslang" path="external/deqp-deps/glslang"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/desugar" path="external/desugar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/dexmaker" path="external/dexmaker"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/dlmalloc" path="external/dlmalloc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/dng_sdk" path="external/dng_sdk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/dnsmasq" path="external/dnsmasq"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/doclava" path="external/doclava"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/dokka" path="external/dokka"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/double-conversion" path="external/double-conversion"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/downloader" path="external/downloader"/>
      <project groups="drm_hwcomposer,mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/drm_hwcomposer" path="external/drm_hwcomposer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/dtc" path="external/dtc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/dynamic_depth" path="external/dynamic_depth"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/e2fsprogs" path="external/e2fsprogs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/easymock" path="external/easymock"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/eigen" path="external/eigen"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/elfutils" path="external/elfutils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/emma" path="external/emma"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/erofs-utils" path="external/erofs-utils"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/error_prone" path="external/error_prone"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/escapevelocity" path="external/escapevelocity"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ethtool" path="external/ethtool"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/exfatprogs" path="external/exfatprogs"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/exoplayer" path="external/exoplayer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/expat" path="external/expat"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/f2fs-tools" path="external/f2fs-tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/fastrpc" path="external/fastrpc"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fdlibm" path="external/fdlibm"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fec" path="external/fec"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fft2d" path="external/fft2d"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/firebase-messaging" path="external/firebase-messaging"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/flac" path="external/flac"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/flatbuffers" path="external/flatbuffers"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fmtlib" path="external/fmtlib"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fonttools" path="external/fonttools"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/freetype" path="external/freetype"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/fsck_msdos" path="external/fsck_msdos"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/fsverity-utils" path="external/fsverity-utils"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/gemmlowp" path="external/gemmlowp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/geojson-jackson" path="external/geojson-jackson"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/geonames" path="external/geonames"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/gflags" path="external/gflags"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/gfxstream-protocols" path="external/gfxstream-protocols"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/giflib" path="external/giflib"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/glide" path="external/glide"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/go-cmp" path="external/go-cmp"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/golang-protobuf" path="external/golang-protobuf"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-benchmark" path="external/google-benchmark"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/external/google-breakpad" path="external/google-breakpad"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/arbutus-slab" path="external/google-fonts/arbutus-slab"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/arvo" path="external/google-fonts/arvo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/barlow" path="external/google-fonts/barlow"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/big-shoulders-text" path="external/google-fonts/big-shoulders-text"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/carrois-gothic-sc" path="external/google-fonts/carrois-gothic-sc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/coming-soon" path="external/google-fonts/coming-soon"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/cutive-mono" path="external/google-fonts/cutive-mono"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/dancing-script" path="external/google-fonts/dancing-script"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/fraunces" path="external/google-fonts/fraunces"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/karla" path="external/google-fonts/karla"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/lato" path="external/google-fonts/lato"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/lustria" path="external/google-fonts/lustria"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/rubik" path="external/google-fonts/rubik"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/source-sans-pro" path="external/google-fonts/source-sans-pro"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fonts/zilla-slab" path="external/google-fonts/zilla-slab"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-fruit" path="external/google-fruit"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-java-format" path="external/google-java-format"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-smali" path="external/google-smali"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/google-styleguide" path="external/google-styleguide"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/googletest" path="external/googletest"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/gptfdisk" path="external/gptfdisk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/external/grpc-grpc" path="external/grpc-grpc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/external/grpc-grpc-java" path="external/grpc-grpc-java"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/external/gson" path="external/gson"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/guava" path="external/guava"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/guice" path="external/guice"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/gwp_asan" path="external/gwp_asan"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/hamcrest" path="external/hamcrest"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/harfbuzz_ng" path="external/harfbuzz_ng"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/hyphenation-patterns" path="external/hyphenation-patterns"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/icing" path="external/icing"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/icu" path="external/icu"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/igt-gpu-tools" path="external/igt-gpu-tools"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/image_io" path="external/image_io"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ims" path="external/ims"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/iperf3" path="external/iperf3"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/iproute2" path="external/iproute2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ipsec-tools" path="external/ipsec-tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/iptables" path="external/iptables"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/iputils" path="external/iputils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/iw" path="external/iw"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/jackson-annotations" path="external/jackson-annotations"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/jackson-core" path="external/jackson-core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/jackson-databind" path="external/jackson-databind"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jacoco" path="external/jacoco"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jarjar" path="external/jarjar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/javaparser" path="external/javaparser"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/javapoet" path="external/javapoet"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/javasqlite" path="external/javasqlite"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/javassist" path="external/javassist"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/jazzer-api" path="external/jazzer-api"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jcommander" path="external/jcommander"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jdiff" path="external/jdiff"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/jemalloc_new" path="external/jemalloc_new"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/jimfs" path="external/jimfs"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,pdk-fs,tradefed" name="platform/external/jline" path="external/jline"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/jsilver" path="external/jsilver"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/jsmn" path="external/jsmn"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/jsoncpp" path="external/jsoncpp"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jsr305" path="external/jsr305"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/jsr330" path="external/jsr330"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/junit" path="external/junit"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/junit-params" path="external/junit-params"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/kernel-headers" path="external/kernel-headers"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/kmod" path="external/kmod"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/kotlinc" path="external/kotlinc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/kotlinpoet" path="external/kotlinpoet"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/kotlinx.atomicfu" path="external/kotlinx.atomicfu"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/kotlinx.coroutines" path="external/kotlinx.coroutines"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/kotlinx.metadata" path="external/kotlinx.metadata"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ksoap2" path="external/ksoap2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ksp" path="external/ksp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ktfmt" path="external/ktfmt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/leveldb" path="external/leveldb"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libabigail" path="external/libabigail"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libaom" path="external/libaom"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libavc" path="external/libavc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libbackup" path="external/libbackup"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libbpf" path="external/libbpf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libbrillo" path="external/libbrillo"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libcap" path="external/libcap"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libcap-ng" path="external/libcap-ng"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libchrome" path="external/libchrome"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libchrome-gestures" path="external/libchrome-gestures"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libchromeos-rs" path="external/libchromeos-rs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libconfig" path="external/libconfig"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/external/libcups" path="external/libcups"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libcxx" path="external/libcxx"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libcxxabi" path="external/libcxxabi"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libdivsufsort" path="external/libdivsufsort"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libdrm" path="external/libdrm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libepoxy" path="external/libepoxy"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libese" path="external/libese"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libevent" path="external/libevent"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libexif" path="external/libexif"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libffi" path="external/libffi"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libfuse" path="external/libfuse"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libgav1" path="external/libgav1"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libgsm" path="external/libgsm"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libhevc" path="external/libhevc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libiio" path="external/libiio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libjpeg-turbo" path="external/libjpeg-turbo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libkmsxx" path="external/libkmsxx"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libldac" path="external/libldac"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,sysui-studio" name="platform/external/libmonet" path="external/libmonet"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libmpeg2" path="external/libmpeg2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libnetfilter_conntrack" path="external/libnetfilter_conntrack"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libnfnetlink" path="external/libnfnetlink"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libnl" path="external/libnl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libogg" path="external/libogg"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libopus" path="external/libopus"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libpalmrejection" path="external/libpalmrejection"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libpcap" path="external/libpcap"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libphonenumber" path="external/libphonenumber"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libpng" path="external/libpng"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libprotobuf-mutator" path="external/libprotobuf-mutator"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libsrtp2" path="external/libsrtp2"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libtextclassifier" path="external/libtextclassifier"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libtraceevent" path="external/libtraceevent"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libtracefs" path="external/libtracefs"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/liburing" path="external/liburing"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libusb" path="external/libusb"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libutf" path="external/libutf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libvpx" path="external/libvpx"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libwebm" path="external/libwebm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libwebsockets" path="external/libwebsockets"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/libxaac" path="external/libxaac"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libxkbcommon" path="external/libxkbcommon"/>
      <project groups="libxml2,mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/libxml2" path="external/libxml2"/>
      <project groups="libyuv,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/libyuv" path="external/libyuv"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/licenseclassifier" path="external/licenseclassifier"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/linux-kselftest" path="external/linux-kselftest"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/llvm" path="external/llvm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/lmfit" path="external/lmfit"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/lottie" path="external/lottie"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/ltp" path="external/ltp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/lua" path="external/lua"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/lz4" path="external/lz4"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/lzma" path="external/lzma"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/marisa-trie" path="external/marisa-trie"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/markdown" path="external/markdown"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mbedtls" path="external/mbedtls"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/mdnsresponder" path="external/mdnsresponder"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/external/mesa3d" path="external/mesa3d"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/mime-support" path="external/mime-support"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/minigbm" path="external/minigbm"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/minijail" path="external/minijail"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/mksh" path="external/mksh"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mobile-data-download" path="external/mobile-data-download"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mobly-bundled-snippets" path="external/mobly-bundled-snippets"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mobly-snippet-lib" path="external/mobly-snippet-lib"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mockftpserver" path="external/mockftpserver"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/mockito" path="external/mockito"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mockito-kotlin" path="external/mockito-kotlin"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mockwebserver" path="external/mockwebserver"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/modp_b64" path="external/modp_b64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mp4parser" path="external/mp4parser"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ms-tpm-20-ref" path="external/ms-tpm-20-ref"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mtools" path="external/mtools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/mtpd" path="external/mtpd"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/musl" path="external/musl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/nanohttpd" path="external/nanohttpd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/nanopb-c" path="external/nanopb-c"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/naver-fonts" path="external/naver-fonts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/neon_2_sse" path="external/neon_2_sse"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/neven" path="external/neven"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/newfs_msdos" path="external/newfs_msdos"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/nist-pkits" path="external/nist-pkits"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/nist-sip" path="external/nist-sip"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/nos/host/generic" path="external/nos/host/generic"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/noto-fonts" path="external/noto-fonts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/nullaway" path="external/nullaway"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/oauth" path="external/oauth"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/obex" path="external/obex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/objenesis" path="external/objenesis"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/oboe" path="external/oboe"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/obstack" path="external/obstack"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/oj-libjdwp" path="external/oj-libjdwp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/okhttp" path="external/okhttp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/okio" path="external/okio"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/one-true-awk" path="external/one-true-awk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/open-dice" path="external/open-dice"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/external/opencensus-java" path="external/opencensus-java"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/openscreen" path="external/openscreen"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/openthread" path="external/openthread"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/openwrt-prebuilts" path="external/openwrt-prebuilts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/oss-fuzz" path="external/oss-fuzz"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ot-br-posix" path="external/ot-br-posix"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/ow2-asm" path="external/ow2-asm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/owasp/java-encoder" path="external/owasp/java-encoder"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/owasp/sanitizer" path="external/owasp/sanitizer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/pandora/avatar" path="external/pandora/avatar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/pandora/bt-test-interfaces" path="external/pandora/bt-test-interfaces"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/pandora/mmi2grpc" path="external/pandora/mmi2grpc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/parameter-framework" path="external/parameter-framework"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/pcre" path="external/pcre"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/pdfium" path="external/pdfium"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/perfetto" path="external/perfetto"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/pffft" path="external/pffft"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/piex" path="external/piex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/pigweed" path="external/pigweed"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ply" path="external/ply"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ppp" path="external/ppp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/proguard" path="external/proguard"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/protobuf" path="external/protobuf"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/psimd" path="external/psimd"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/pthreadpool" path="external/pthreadpool"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/puffin" path="external/puffin"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/absl-py" path="external/python/absl-py"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/apitools" path="external/python/apitools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/asn1crypto" path="external/python/asn1crypto"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/bumble" path="external/python/bumble"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/cachetools" path="external/python/cachetools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/cffi" path="external/python/cffi"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/python/cpython2" path="external/python/cpython2"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/python/cpython3" path="external/python/cpython3"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/cryptography" path="external/python/cryptography"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/dateutil" path="external/python/dateutil"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/enum34" path="external/python/enum34"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/google-api-python-client" path="external/python/google-api-python-client"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/google-auth-library-python" path="external/python/google-auth-library-python"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/httplib2" path="external/python/httplib2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/ipaddress" path="external/python/ipaddress"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/jinja" path="external/python/jinja"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/markupsafe" path="external/python/markupsafe"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/mobly" path="external/python/mobly"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/oauth2client" path="external/python/oauth2client"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/parse_type" path="external/python/parse_type"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/portpicker" path="external/python/portpicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/pyasn1" path="external/python/pyasn1"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/pyasn1-modules" path="external/python/pyasn1-modules"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pybind11" path="external/python/pybind11"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pycparser" path="external/python/pycparser"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pyee" path="external/python/pyee"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pyfakefs" path="external/python/pyfakefs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pyserial" path="external/python/pyserial"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/python-api-core" path="external/python/python-api-core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/pyyaml" path="external/python/pyyaml"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/rsa" path="external/python/rsa"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/setuptools" path="external/python/setuptools"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/external/python/six" path="external/python/six"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/timeout-decorator" path="external/python/timeout-decorator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/python/typing" path="external/python/typing"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,vts" name="platform/external/python/uritemplates" path="external/python/uritemplates"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rappor" path="external/rappor"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/renderscript-intrinsics-replacement-toolkit" path="external/renderscript-intrinsics-replacement-toolkit"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/replicaisland" path="external/replicaisland"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rmi4utils" path="external/rmi4utils"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rnnoise" path="external/rnnoise"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/external/robolectric" path="external/robolectric"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/external/robolectric-shadows" path="external/robolectric-shadows"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/roboto-flex-fonts" path="external/roboto-flex-fonts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/roboto-fonts" path="external/roboto-fonts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rootdev" path="external/rootdev"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/beto-rust" path="external/rust/beto-rust"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/aarch64-paging" path="external/rust/crates/aarch64-paging"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ahash" path="external/rust/crates/ahash"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/aho-corasick" path="external/rust/crates/aho-corasick"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/android_log-sys" path="external/rust/crates/android_log-sys"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/android_logger" path="external/rust/crates/android_logger"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/anes" path="external/rust/crates/anes"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/anyhow" path="external/rust/crates/anyhow"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/arbitrary" path="external/rust/crates/arbitrary"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/argh" path="external/rust/crates/argh"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/argh_derive" path="external/rust/crates/argh_derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/argh_shared" path="external/rust/crates/argh_shared"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ash" path="external/rust/crates/ash"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/async-stream" path="external/rust/crates/async-stream"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/async-stream-impl" path="external/rust/crates/async-stream-impl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/async-task" path="external/rust/crates/async-task"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/async-trait" path="external/rust/crates/async-trait"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/atomic" path="external/rust/crates/atomic"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/atty" path="external/rust/crates/atty"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/base64" path="external/rust/crates/base64"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/bencher" path="external/rust/crates/bencher"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/bindgen" path="external/rust/crates/bindgen"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/bindgen-cli" path="external/rust/crates/bindgen-cli"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/bitflags" path="external/rust/crates/bitflags"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/bitreader" path="external/rust/crates/bitreader"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/bstr" path="external/rust/crates/bstr"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/buddy_system_allocator" path="external/rust/crates/buddy_system_allocator"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/byteorder" path="external/rust/crates/byteorder"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/bytes" path="external/rust/crates/bytes"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/cast" path="external/rust/crates/cast"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/cesu8" path="external/rust/crates/cesu8"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/cexpr" path="external/rust/crates/cexpr"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/cfg-if" path="external/rust/crates/cfg-if"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/chrono" path="external/rust/crates/chrono"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/ciborium" path="external/rust/crates/ciborium"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/ciborium-io" path="external/rust/crates/ciborium-io"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/ciborium-ll" path="external/rust/crates/ciborium-ll"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/clang-sys" path="external/rust/crates/clang-sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/clap" path="external/rust/crates/clap"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/clap_derive" path="external/rust/crates/clap_derive"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/clap_lex" path="external/rust/crates/clap_lex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/codespan-reporting" path="external/rust/crates/codespan-reporting"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/combine" path="external/rust/crates/combine"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/command-fds" path="external/rust/crates/command-fds"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/const-oid" path="external/rust/crates/const-oid"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/coset" path="external/rust/crates/coset"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crc32fast" path="external/rust/crates/crc32fast"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/criterion" path="external/rust/crates/criterion"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/criterion-plot" path="external/rust/crates/criterion-plot"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crossbeam-channel" path="external/rust/crates/crossbeam-channel"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crossbeam-deque" path="external/rust/crates/crossbeam-deque"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crossbeam-epoch" path="external/rust/crates/crossbeam-epoch"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crossbeam-queue" path="external/rust/crates/crossbeam-queue"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/crossbeam-utils" path="external/rust/crates/crossbeam-utils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/csv" path="external/rust/crates/csv"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/csv-core" path="external/rust/crates/csv-core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/der" path="external/rust/crates/der"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/der-oid-macro" path="external/rust/crates/der-oid-macro"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/der-parser" path="external/rust/crates/der-parser"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/der_derive" path="external/rust/crates/der_derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/derive-getters" path="external/rust/crates/derive-getters"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/derive_arbitrary" path="external/rust/crates/derive_arbitrary"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/document-features" path="external/rust/crates/document-features"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/downcast-rs" path="external/rust/crates/downcast-rs"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/either" path="external/rust/crates/either"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/enumn" path="external/rust/crates/enumn"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/env_logger" path="external/rust/crates/env_logger"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/fallible-iterator" path="external/rust/crates/fallible-iterator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/fallible-streaming-iterator" path="external/rust/crates/fallible-streaming-iterator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/fastrand" path="external/rust/crates/fastrand"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/flagset" path="external/rust/crates/flagset"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/flate2" path="external/rust/crates/flate2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/fnv" path="external/rust/crates/fnv"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/foreign-types" path="external/rust/crates/foreign-types"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/foreign-types-shared" path="external/rust/crates/foreign-types-shared"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/form_urlencoded" path="external/rust/crates/form_urlencoded"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures" path="external/rust/crates/futures"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-channel" path="external/rust/crates/futures-channel"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-core" path="external/rust/crates/futures-core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-executor" path="external/rust/crates/futures-executor"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-io" path="external/rust/crates/futures-io"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-macro" path="external/rust/crates/futures-macro"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-sink" path="external/rust/crates/futures-sink"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-task" path="external/rust/crates/futures-task"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-test" path="external/rust/crates/futures-test"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/futures-util" path="external/rust/crates/futures-util"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/fxhash" path="external/rust/crates/fxhash"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/gdbstub" path="external/rust/crates/gdbstub"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/gdbstub_arch" path="external/rust/crates/gdbstub_arch"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/getrandom" path="external/rust/crates/getrandom"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/glam" path="external/rust/crates/glam"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/glob" path="external/rust/crates/glob"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/grpcio" path="external/rust/crates/grpcio"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/grpcio-compiler" path="external/rust/crates/grpcio-compiler"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/grpcio-sys" path="external/rust/crates/grpcio-sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/half" path="external/rust/crates/half"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/hashbrown" path="external/rust/crates/hashbrown"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/hashlink" path="external/rust/crates/hashlink"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/heck" path="external/rust/crates/heck"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/hex" path="external/rust/crates/hex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/idna" path="external/rust/crates/idna"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/indexmap" path="external/rust/crates/indexmap"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/instant" path="external/rust/crates/instant"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/intrusive-collections" path="external/rust/crates/intrusive-collections"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/itertools" path="external/rust/crates/itertools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/itoa" path="external/rust/crates/itoa"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/jni" path="external/rust/crates/jni"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/jni-sys" path="external/rust/crates/jni-sys"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/kernlog" path="external/rust/crates/kernlog"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/lazy_static" path="external/rust/crates/lazy_static"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/lazycell" path="external/rust/crates/lazycell"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/libc" path="external/rust/crates/libc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/libfuzzer-sys" path="external/rust/crates/libfuzzer-sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/libloading" path="external/rust/crates/libloading"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/libm" path="external/rust/crates/libm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/libsqlite3-sys" path="external/rust/crates/libsqlite3-sys"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/libtest-mimic" path="external/rust/crates/libtest-mimic"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/libz-sys" path="external/rust/crates/libz-sys"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/linked-hash-map" path="external/rust/crates/linked-hash-map"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/litrs" path="external/rust/crates/litrs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/lock_api" path="external/rust/crates/lock_api"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/log" path="external/rust/crates/log"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/lru-cache" path="external/rust/crates/lru-cache"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/macaddr" path="external/rust/crates/macaddr"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/managed" path="external/rust/crates/managed"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/memchr" path="external/rust/crates/memchr"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/memoffset" path="external/rust/crates/memoffset"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/merge" path="external/rust/crates/merge"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/merge_derive" path="external/rust/crates/merge_derive"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/minimal-lexical" path="external/rust/crates/minimal-lexical"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/mio" path="external/rust/crates/mio"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/nix" path="external/rust/crates/nix"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/no-panic" path="external/rust/crates/no-panic"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/nom" path="external/rust/crates/nom"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/num-bigint" path="external/rust/crates/num-bigint"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/num-derive" path="external/rust/crates/num-derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/num-integer" path="external/rust/crates/num-integer"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/num-traits" path="external/rust/crates/num-traits"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/num_cpus" path="external/rust/crates/num_cpus"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/octets" path="external/rust/crates/octets"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/oid-registry" path="external/rust/crates/oid-registry"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/once_cell" path="external/rust/crates/once_cell"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/oorandom" path="external/rust/crates/oorandom"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/openssl" path="external/rust/crates/openssl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/openssl-macros" path="external/rust/crates/openssl-macros"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/os_str_bytes" path="external/rust/crates/os_str_bytes"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/parking_lot" path="external/rust/crates/parking_lot"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/parking_lot_core" path="external/rust/crates/parking_lot_core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/paste" path="external/rust/crates/paste"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/peeking_take_while" path="external/rust/crates/peeking_take_while"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/percent-encoding" path="external/rust/crates/percent-encoding"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pest" path="external/rust/crates/pest"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pest_derive" path="external/rust/crates/pest_derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pest_generator" path="external/rust/crates/pest_generator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pest_meta" path="external/rust/crates/pest_meta"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pin-project" path="external/rust/crates/pin-project"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pin-project-internal" path="external/rust/crates/pin-project-internal"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pin-project-lite" path="external/rust/crates/pin-project-lite"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pin-utils" path="external/rust/crates/pin-utils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pkcs1" path="external/rust/crates/pkcs1"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/pkcs8" path="external/rust/crates/pkcs8"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/plotters" path="external/rust/crates/plotters"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/plotters-backend" path="external/rust/crates/plotters-backend"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/plotters-svg" path="external/rust/crates/plotters-svg"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ppv-lite86" path="external/rust/crates/ppv-lite86"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/proc-macro-error" path="external/rust/crates/proc-macro-error"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/proc-macro-error-attr" path="external/rust/crates/proc-macro-error-attr"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/proc-macro-nested" path="external/rust/crates/proc-macro-nested"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/proc-macro2" path="external/rust/crates/proc-macro2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/protobuf" path="external/rust/crates/protobuf"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/protobuf-codegen" path="external/rust/crates/protobuf-codegen"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/protobuf-json-mapping" path="external/rust/crates/protobuf-json-mapping"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/protobuf-parse" path="external/rust/crates/protobuf-parse"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/protobuf-support" path="external/rust/crates/protobuf-support"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/psci" path="external/rust/crates/psci"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/quiche" path="external/rust/crates/quiche"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/quickcheck" path="external/rust/crates/quickcheck"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/quote" path="external/rust/crates/quote"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rand" path="external/rust/crates/rand"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rand_chacha" path="external/rust/crates/rand_chacha"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rand_core" path="external/rust/crates/rand_core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rand_xorshift" path="external/rust/crates/rand_xorshift"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rayon" path="external/rust/crates/rayon"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rayon-core" path="external/rust/crates/rayon-core"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/regex" path="external/rust/crates/regex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/regex-automata" path="external/rust/crates/regex-automata"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/regex-syntax" path="external/rust/crates/regex-syntax"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/remain" path="external/rust/crates/remain"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/remove_dir_all" path="external/rust/crates/remove_dir_all"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ring" path="external/rust/crates/ring"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rusqlite" path="external/rust/crates/rusqlite"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/rustc-demangle" path="external/rust/crates/rustc-demangle"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/rustc-demangle-capi" path="external/rust/crates/rustc-demangle-capi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/rustc-hash" path="external/rust/crates/rustc-hash"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rusticata-macros" path="external/rust/crates/rusticata-macros"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/rustversion" path="external/rust/crates/rustversion"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ryu" path="external/rust/crates/ryu"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/same-file" path="external/rust/crates/same-file"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/scopeguard" path="external/rust/crates/scopeguard"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/sec1" path="external/rust/crates/sec1"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/semver" path="external/rust/crates/semver"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/serde" path="external/rust/crates/serde"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/serde-xml-rs" path="external/rust/crates/serde-xml-rs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/serde_cbor" path="external/rust/crates/serde_cbor"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/serde_derive" path="external/rust/crates/serde_derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/serde_json" path="external/rust/crates/serde_json"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/serde_test" path="external/rust/crates/serde_test"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/shared_child" path="external/rust/crates/shared_child"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/shared_library" path="external/rust/crates/shared_library"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/shlex" path="external/rust/crates/shlex"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/slab" path="external/rust/crates/slab"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/smallvec" path="external/rust/crates/smallvec"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/socket2" path="external/rust/crates/socket2"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/spin" path="external/rust/crates/spin"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/spki" path="external/rust/crates/spki"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/static_assertions" path="external/rust/crates/static_assertions"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/syn" path="external/rust/crates/syn"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/syn-mid" path="external/rust/crates/syn-mid"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/synstructure" path="external/rust/crates/synstructure"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tempfile" path="external/rust/crates/tempfile"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/termcolor" path="external/rust/crates/termcolor"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/textwrap" path="external/rust/crates/textwrap"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/thiserror" path="external/rust/crates/thiserror"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/thiserror-impl" path="external/rust/crates/thiserror-impl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/thread_local" path="external/rust/crates/thread_local"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tinytemplate" path="external/rust/crates/tinytemplate"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/tinyvec" path="external/rust/crates/tinyvec"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tinyvec_macros" path="external/rust/crates/tinyvec_macros"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tokio" path="external/rust/crates/tokio"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tokio-macros" path="external/rust/crates/tokio-macros"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tokio-stream" path="external/rust/crates/tokio-stream"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tokio-test" path="external/rust/crates/tokio-test"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tokio-util" path="external/rust/crates/tokio-util"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tracing" path="external/rust/crates/tracing"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tracing-attributes" path="external/rust/crates/tracing-attributes"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/tracing-core" path="external/rust/crates/tracing-core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/ucd-trie" path="external/rust/crates/ucd-trie"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/unicode-bidi" path="external/rust/crates/unicode-bidi"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/unicode-ident" path="external/rust/crates/unicode-ident"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/unicode-normalization" path="external/rust/crates/unicode-normalization"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/unicode-segmentation" path="external/rust/crates/unicode-segmentation"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/unicode-width" path="external/rust/crates/unicode-width"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/unicode-xid" path="external/rust/crates/unicode-xid"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/untrusted" path="external/rust/crates/untrusted"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/url" path="external/rust/crates/url"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/userfaultfd" path="external/rust/crates/userfaultfd"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/userfaultfd-sys" path="external/rust/crates/userfaultfd-sys"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/uuid" path="external/rust/crates/uuid"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/virtio-drivers" path="external/rust/crates/virtio-drivers"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/vsock" path="external/rust/crates/vsock"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/vulkano" path="external/rust/crates/vulkano"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/walkdir" path="external/rust/crates/walkdir"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/weak-table" path="external/rust/crates/weak-table"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/webpki" path="external/rust/crates/webpki"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/which" path="external/rust/crates/which"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/x509-cert" path="external/rust/crates/x509-cert"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/x509-parser" path="external/rust/crates/x509-parser"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/xml-rs" path="external/rust/crates/xml-rs"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/zerocopy" path="external/rust/crates/zerocopy"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/zerocopy-derive" path="external/rust/crates/zerocopy-derive"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/zeroize" path="external/rust/crates/zeroize"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/rust/crates/zeroize_derive" path="external/rust/crates/zeroize_derive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/crates/zip" path="external/rust/crates/zip"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/cxx" path="external/rust/cxx"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/rust/pica" path="external/rust/pica"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/ruy" path="external/ruy"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/s2-geometry-library-java" path="external/s2-geometry-library-java"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/external/scapy" path="external/scapy"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/scrypt" path="external/scrypt"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/scudo" path="external/scudo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/seccomp-tests" path="external/seccomp-tests"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/selinux" path="external/selinux"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/setfilters" path="external/setfilters"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/setupcompat" path="external/setupcompat"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/setupdesign" path="external/setupdesign"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/sfntly" path="external/sfntly"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/sg3_utils" path="external/sg3_utils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/shaderc/spirv-headers" path="external/shaderc/spirv-headers"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/shflags" path="external/shflags"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/skia" path="external/skia"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/sl4a" path="external/sl4a"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/slf4j" path="external/slf4j"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/smali" path="external/smali"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/snakeyaml" path="external/snakeyaml"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/sonic" path="external/sonic"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/sonivox" path="external/sonivox"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/spdx-tools" path="external/spdx-tools"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/speex" path="external/speex"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/sqlite" path="external/sqlite"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/squashfs-tools" path="external/squashfs-tools"/>
      <project groups="mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/stardoc" path="external/stardoc">
        <linkfile dest="kernel/external/stardoc" src="."/>
      </project>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/starlark-go" path="external/starlark-go"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/strace" path="external/strace"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/stressapptest" path="external/stressapptest"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/subsampling-scale-image-view" path="external/subsampling-scale-image-view"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/swiftshader" path="external/swiftshader"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/tagsoup" path="external/tagsoup"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/tcpdump" path="external/tcpdump"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/tensorflow" path="external/tensorflow"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/testng" path="external/testng"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/tflite-support" path="external/tflite-support"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/timezone-boundary-builder" path="external/timezone-boundary-builder"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/tinyalsa" path="external/tinyalsa"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/tinyalsa_new" path="external/tinyalsa_new"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/tinycompress" path="external/tinycompress"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/tinyxml2" path="external/tinyxml2"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/toybox" path="external/toybox"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/tpm2-tss" path="external/tpm2-tss"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/trace-cmd" path="external/trace-cmd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/tremolo" path="external/tremolo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/truth" path="external/truth"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/turbine" path="external/turbine"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/ukey2" path="external/ukey2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/unicode" path="external/unicode"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/uwb" path="external/uwb"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/v4l2_codec2" path="external/v4l2_codec2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs,vboot" name="platform/external/vboot_reference" path="external/vboot_reference"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/virglrenderer" path="external/virglrenderer"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/vixl" path="external/vixl"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/vogar" path="external/vogar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/volley" path="external/volley"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/vulkan-headers" path="external/vulkan-headers"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/vulkan-validation-layers" path="external/vulkan-validation-layers"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/walt" path="external/walt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/wayland" path="external/wayland"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/wayland-protocols" path="external/wayland-protocols"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/webp" path="external/webp"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/webrtc" path="external/webrtc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/wmediumd" path="external/wmediumd"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/wpa_supplicant_8" path="external/wpa_supplicant_8"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,qcom_msm8x26" name="platform/external/wuffs-mirror-release-c" path="external/wuffs-mirror-release-c"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/wycheproof" path="external/wycheproof"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/xmp_toolkit" path="external/xmp_toolkit"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/xz-embedded" path="external/xz-embedded"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/xz-java" path="external/xz-java"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,projectarch,vts" name="platform/external/yapf" path="external/yapf"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/zlib" path="external/zlib"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/zopfli" path="external/zopfli"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/external/zstd" path="external/zstd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/external/zucchini" path="external/zucchini"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/external/zxing" path="external/zxing"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/av" path="frameworks/av"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/base" path="frameworks/base"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/frameworks/compile/libbcc" path="frameworks/compile/libbcc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/compile/mclinker" path="frameworks/compile/mclinker"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/compile/slang" path="frameworks/compile/slang"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/ex" path="frameworks/ex"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/hardware/interfaces" path="frameworks/hardware/interfaces"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/layoutlib" path="frameworks/layoutlib"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/libs/binary_translation" path="frameworks/libs/binary_translation"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/libs/gsma_services" path="frameworks/libs/gsma_services"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/libs/modules-utils" path="frameworks/libs/modules-utils"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/libs/native_bridge_support" path="frameworks/libs/native_bridge_support"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/libs/net" path="frameworks/libs/net"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/libs/service_entitlement" path="frameworks/libs/service_entitlement"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/libs/systemui" path="frameworks/libs/systemui"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/minikin" path="frameworks/minikin"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/multidex" path="frameworks/multidex"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/native" path="frameworks/native"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/frameworks/opt/bitmap" path="frameworks/opt/bitmap"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/calendar" path="frameworks/opt/calendar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/frameworks/opt/car/services" path="frameworks/opt/car/services"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/frameworks/opt/car/setupwizard" path="frameworks/opt/car/setupwizard"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/chips" path="frameworks/opt/chips"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/colorpicker" path="frameworks/opt/colorpicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/frameworks/opt/net/ethernet" path="frameworks/opt/net/ethernet"/>
      <project groups="frameworks_ims,mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/net/ims" path="frameworks/opt/net/ims"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/net/voip" path="frameworks/opt/net/voip"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/opt/net/wifi" path="frameworks/opt/net/wifi"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/photoviewer" path="frameworks/opt/photoviewer"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/setupwizard" path="frameworks/opt/setupwizard"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/frameworks/opt/telephony" path="frameworks/opt/telephony"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/timezonepicker" path="frameworks/opt/timezonepicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/tv/tvsystem" path="frameworks/opt/tv/tvsystem"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/opt/vcard" path="frameworks/opt/vcard"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/frameworks/proto_logging" path="frameworks/proto_logging"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/frameworks/rs" path="frameworks/rs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/frameworks/wilhelm" path="frameworks/wilhelm"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/broadcom/libbt" path="hardware/broadcom/libbt"/>
      <project groups="broadcom_wlan,mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/broadcom/wlan" path="hardware/broadcom/wlan"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/google/aemu" path="hardware/google/aemu"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/google/apf" path="hardware/google/apf"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/google/av" path="hardware/google/av"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/google/camera" path="hardware/google/camera"/>
      <project groups="easel,mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/google/easel" path="hardware/google/easel"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/google/gfxstream" path="hardware/google/gfxstream"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/google/interfaces" path="hardware/google/interfaces"/>
      <project groups="generic_fs,mtk-sys-codebase,mtk-system,pixel" name="platform/hardware/google/pixel" path="hardware/google/pixel"/>
      <project groups="generic_fs,mtk-sys-codebase,mtk-system,pixel" name="platform/hardware/google/pixel-sepolicy" path="hardware/google/pixel-sepolicy"/>
      <project groups="mtk-aosp-vnd,mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/interfaces" path="hardware/interfaces"/>
      <project groups="invensense,mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/invensense" path="hardware/invensense"/>
      <project groups="coral,generic_fs,mtk-sys-codebase,mtk-system" name="platform/hardware/knowles/athletico/sound_trigger_hal" path="hardware/knowles/athletico/sound_trigger_hal"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/hardware/libhardware" path="hardware/libhardware"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/libhardware_legacy" path="hardware/libhardware_legacy"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/nxp/nfc" path="hardware/nxp/nfc"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/nxp/secure_element" path="hardware/nxp/secure_element"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/nxp/uwb" path="hardware/nxp/uwb"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom,qcom_audio" name="platform/hardware/qcom/audio" path="hardware/qcom/audio"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom" name="platform/hardware/qcom/bootctrl" path="hardware/qcom/bootctrl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom" name="platform/hardware/qcom/bt" path="hardware/qcom/bt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom_camera" name="platform/hardware/qcom/camera" path="hardware/qcom/camera"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom" name="platform/hardware/qcom/data/ipacfg-mgr" path="hardware/qcom/data/ipacfg-mgr"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom,qcom_display" name="platform/hardware/qcom/display" path="hardware/qcom/display"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom,qcom_gps" name="platform/hardware/qcom/gps" path="hardware/qcom/gps"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom,qcom_keymaster" name="platform/hardware/qcom/keymaster" path="hardware/qcom/keymaster"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom" name="platform/hardware/qcom/media" path="hardware/qcom/media"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom" name="platform/hardware/qcom/power" path="hardware/qcom/power"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-qcom,qcom_wlan" name="platform/hardware/qcom/wlan" path="hardware/qcom/wlan"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/hardware/ril" path="hardware/ril"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/samsung/nfc" path="hardware/samsung/nfc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/st/nfc" path="hardware/st/nfc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/st/secure_element" path="hardware/st/secure_element"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/st/secure_element2" path="hardware/st/secure_element2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/synaptics/wlan" path="hardware/synaptics/wlan"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/hardware/ti/am57x" path="hardware/ti/am57x"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/libcore" path="libcore"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/libnativehelper" path="libnativehelper"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/BasicSmsReceiver" path="packages/apps/BasicSmsReceiver"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Browser2" path="packages/apps/Browser2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Calendar" path="packages/apps/Calendar"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Camera2" path="packages/apps/Camera2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Cluster" path="packages/apps/Car/Cluster"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/DebuggingRestrictionController" path="packages/apps/Car/DebuggingRestrictionController"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/DialerPrebuilt" path="packages/apps/Car/DialerPrebuilt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/LatinIME" path="packages/apps/Car/LatinIME"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Launcher" path="packages/apps/Car/Launcher"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/LinkViewer" path="packages/apps/Car/LinkViewer"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/LocalMediaPlayer" path="packages/apps/Car/LocalMediaPlayer"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/MediaPrebuilt" path="packages/apps/Car/MediaPrebuilt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/MessengerPrebuilt" path="packages/apps/Car/MessengerPrebuilt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Notification" path="packages/apps/Car/Notification"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Provision" path="packages/apps/Car/Provision"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Radio" path="packages/apps/Car/Radio"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/RotaryController" path="packages/apps/Car/RotaryController"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Settings" path="packages/apps/Car/Settings"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/SettingsIntelligence" path="packages/apps/Car/SettingsIntelligence"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/SystemUI" path="packages/apps/Car/SystemUI"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/SystemUpdater" path="packages/apps/Car/SystemUpdater"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/Templates" path="packages/apps/Car/Templates"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/VoiceControl" path="packages/apps/Car/VoiceControl"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Car/systemlibs" path="packages/apps/Car/systemlibs"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/CarrierConfig" path="packages/apps/CarrierConfig"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/CellBroadcastReceiver" path="packages/apps/CellBroadcastReceiver"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/CertInstaller" path="packages/apps/CertInstaller"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Contacts" path="packages/apps/Contacts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/DeskClock" path="packages/apps/DeskClock"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/apps/DevCamera" path="packages/apps/DevCamera"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Dialer" path="packages/apps/Dialer"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/DocumentsUI" path="packages/apps/DocumentsUI"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/EmergencyInfo" path="packages/apps/EmergencyInfo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Gallery" path="packages/apps/Gallery"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Gallery2" path="packages/apps/Gallery2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/HTMLViewer" path="packages/apps/HTMLViewer"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/ImsServiceEntitlement" path="packages/apps/ImsServiceEntitlement"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/KeyChain" path="packages/apps/KeyChain"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Launcher3" path="packages/apps/Launcher3"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/LegacyCamera" path="packages/apps/LegacyCamera"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/ManagedProvisioning" path="packages/apps/ManagedProvisioning"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Messaging" path="packages/apps/Messaging"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Music" path="packages/apps/Music"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/MusicFX" path="packages/apps/MusicFX"/>
      <project groups="apps_nfc,mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Nfc" path="packages/apps/Nfc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/OnDeviceAppPrediction" path="packages/apps/OnDeviceAppPrediction"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/apps/PhoneCommon" path="packages/apps/PhoneCommon"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Protips" path="packages/apps/Protips"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Provision" path="packages/apps/Provision"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/QuickAccessWallet" path="packages/apps/QuickAccessWallet"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/QuickSearchBox" path="packages/apps/QuickSearchBox"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SafetyRegulatoryInfo" path="packages/apps/SafetyRegulatoryInfo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SampleLocationAttribution" path="packages/apps/SampleLocationAttribution"/>
      <project groups="apps_se,mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SecureElement" path="packages/apps/SecureElement"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Settings" path="packages/apps/Settings"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SettingsIntelligence" path="packages/apps/SettingsIntelligence"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SpareParts" path="packages/apps/SpareParts"/>
      <project groups="apps_stk,mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Stk" path="packages/apps/Stk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/StorageManager" path="packages/apps/StorageManager"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/SystemUIGo" path="packages/apps/SystemUIGo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/apps/TV" path="packages/apps/TV"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Tag" path="packages/apps/Tag"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/apps/Test/connectivity" path="packages/apps/Test/connectivity"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/ThemePicker" path="packages/apps/ThemePicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/Traceur" path="packages/apps/Traceur"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/TvSettings" path="packages/apps/TvSettings"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/TvSystemUI" path="packages/apps/TvSystemUI"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/WallpaperPicker" path="packages/apps/WallpaperPicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/apps/WallpaperPicker2" path="packages/apps/WallpaperPicker2"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/inputmethods/LatinIME" path="packages/inputmethods/LatinIME"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/inputmethods/LeanbackIME" path="packages/inputmethods/LeanbackIME"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/packages/modules/AdServices" path="packages/modules/AdServices"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/AppSearch" path="packages/modules/AppSearch"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/ArtPrebuilt" path="packages/modules/ArtPrebuilt"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/modules/Auxiliary" path="packages/modules/Auxiliary"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/Bluetooth" path="packages/modules/Bluetooth"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/CaptivePortalLogin" path="packages/modules/CaptivePortalLogin"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/modules/CellBroadcastService" path="packages/modules/CellBroadcastService"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/ConfigInfrastructure" path="packages/modules/ConfigInfrastructure"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Connectivity" path="packages/modules/Connectivity"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Cronet" path="packages/modules/Cronet"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/DeviceLock" path="packages/modules/DeviceLock"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/DnsResolver" path="packages/modules/DnsResolver"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/ExtServices" path="packages/modules/ExtServices"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/GeoTZ" path="packages/modules/GeoTZ"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Gki" path="packages/modules/Gki"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/packages/modules/HealthFitness" path="packages/modules/HealthFitness"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/IPsec" path="packages/modules/IPsec"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/modules/ImsMedia" path="packages/modules/ImsMedia"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/modules/IntentResolver" path="packages/modules/IntentResolver"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/Media" path="packages/modules/Media"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/packages/modules/ModuleMetadata" path="packages/modules/ModuleMetadata"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/NetworkPermissionConfig" path="packages/modules/NetworkPermissionConfig"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/NetworkStack" path="packages/modules/NetworkStack"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/NeuralNetworks" path="packages/modules/NeuralNetworks"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/packages/modules/OnDevicePersonalization" path="packages/modules/OnDevicePersonalization"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Permission" path="packages/modules/Permission"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/RemoteKeyProvisioning" path="packages/modules/RemoteKeyProvisioning"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/RuntimeI18n" path="packages/modules/RuntimeI18n"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/SEPolicy" path="packages/modules/SEPolicy"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Scheduling" path="packages/modules/Scheduling"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/SdkExtensions" path="packages/modules/SdkExtensions"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/StatsD" path="packages/modules/StatsD"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Telephony" path="packages/modules/Telephony"/>
      <project groups="mtk-aosp,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Uwb" path="packages/modules/Uwb"/>
      <project groups="mtk-aosp,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/Virtualization" path="packages/modules/Virtualization"/>
      <project groups="mtk-aosp-vnd,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/Wifi" path="packages/modules/Wifi"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/packages/modules/adb" path="packages/modules/adb"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/modules/common" path="packages/modules/common"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk-cw-fs,pdk-fs" name="platform/packages/modules/vndk" path="packages/modules/vndk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/providers/BlockedNumberProvider" path="packages/providers/BlockedNumberProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/providers/BookmarkProvider" path="packages/providers/BookmarkProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/providers/CalendarProvider" path="packages/providers/CalendarProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/providers/CallLogProvider" path="packages/providers/CallLogProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/providers/ContactsProvider" path="packages/providers/ContactsProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/providers/DownloadProvider" path="packages/providers/DownloadProvider"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/providers/MediaProvider" path="packages/providers/MediaProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/providers/PartnerBookmarksProvider" path="packages/providers/PartnerBookmarksProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/providers/TelephonyProvider" path="packages/providers/TelephonyProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/providers/TvProvider" path="packages/providers/TvProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/providers/UserDictionaryProvider" path="packages/providers/UserDictionaryProvider"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/screensavers/Basic" path="packages/screensavers/Basic"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/screensavers/PhotoTable" path="packages/screensavers/PhotoTable"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/AlternativeNetworkAccess" path="packages/services/AlternativeNetworkAccess"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/BuiltInPrintService" path="packages/services/BuiltInPrintService"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase" name="platform/packages/services/Car" path="packages/services/Car"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/DeviceAsWebcam" path="packages/services/DeviceAsWebcam"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/Iwlan" path="packages/services/Iwlan"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/Mms" path="packages/services/Mms"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/Mtp" path="packages/services/Mtp"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/packages/services/Telecomm" path="packages/services/Telecomm"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/packages/services/Telephony" path="packages/services/Telephony"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/wallpapers/ImageWallpaper" path="packages/wallpapers/ImageWallpaper"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/packages/wallpapers/LivePicker" path="packages/wallpapers/LivePicker"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/pdk" path="pdk"/>
      <project groups="cts,mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/platform_testing" path="platform_testing"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/prebuilts/abi-dumps/ndk" path="prebuilts/abi-dumps/ndk"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/prebuilts/abi-dumps/platform" path="prebuilts/abi-dumps/platform"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/prebuilts/abi-dumps/vndk" path="prebuilts/abi-dumps/vndk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/prebuilts/android-emulator" path="prebuilts/android-emulator"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/asuite" path="prebuilts/asuite"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/bazel/common" path="prebuilts/bazel/common"/>
      <project groups="darwin,mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/bazel/darwin-x86_64" path="prebuilts/bazel/darwin-x86_64"/>
      <project groups="linux,mtk-build-sys,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/bazel/linux-x86_64" path="prebuilts/bazel/linux-x86_64">
        <linkfile dest="kernel/prebuilts/bazel/linux-x86_64" src="."/>
      </project>
      <project groups="mtk-build-sys,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/build-tools" path="prebuilts/build-tools">
        <linkfile dest="kernel/prebuilts/build-tools" src="."/>
      </project>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/bundletool" path="prebuilts/bundletool"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/checkcolor" path="prebuilts/checkcolor"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/checkstyle" path="prebuilts/checkstyle"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/clang-tools" path="prebuilts/clang-tools"/>
      <project groups="darwin,mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/clang/host/darwin-x86" path="prebuilts/clang/host/darwin-x86"/>
      <project groups="mtk-build-sys,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/prebuilts/clang/host/linux-x86" path="prebuilts/clang/host/linux-x86">
        <linkfile dest="kernel/prebuilts/clang/host/linux-x86" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-fs" name="platform/prebuilts/cmdline-tools" path="prebuilts/cmdline-tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/prebuilts/devtools" path="prebuilts/devtools"/>
      <project groups="linux,mtk-build-sys,mtk-host,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/gcc/linux-x86/host/x86_64-linux-glibc2.17-4.8" path="prebuilts/gcc/linux-x86/host/x86_64-linux-glibc2.17-4.8">
        <linkfile dest="kernel/prebuilts/gcc/linux-x86/host/x86_64-linux-glibc2.17-4.8" src="."/>
      </project>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs" name="platform/prebuilts/gcc/linux-x86/host/x86_64-w64-mingw32-4.8" path="prebuilts/gcc/linux-x86/host/x86_64-w64-mingw32-4.8"/>
      <project groups="darwin,mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/prebuilts/go/darwin-x86" path="prebuilts/go/darwin-x86"/>
      <project groups="linux,mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tradefed" name="platform/prebuilts/go/linux-x86" path="prebuilts/go/linux-x86"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,pdk-cw-fs,pdk-fs" name="platform/prebuilts/gradle-plugin" path="prebuilts/gradle-plugin"/>
      <project groups="mtk-host,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/jdk/jdk11" path="prebuilts/jdk/jdk11">
        <linkfile dest="kernel/prebuilts/jdk/jdk11" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/jdk/jdk17" path="prebuilts/jdk/jdk17"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/jdk/jdk8" path="prebuilts/jdk/jdk8"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/jdk/jdk9" path="prebuilts/jdk/jdk9"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/ktlint" path="prebuilts/ktlint"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/manifest-merger" path="prebuilts/manifest-merger"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/prebuilts/maven_repo/bumptech" path="prebuilts/maven_repo/bumptech"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/misc" path="prebuilts/misc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/prebuilts/ndk" path="prebuilts/ndk"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/qemu-kernel" path="prebuilts/qemu-kernel"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/r8" path="prebuilts/r8"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/remoteexecution-client" path="prebuilts/remoteexecution-client"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/runtime" path="prebuilts/runtime"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/rust" path="prebuilts/rust"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/prebuilts/sdk" path="prebuilts/sdk"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tools" name="platform/prebuilts/tools" path="prebuilts/tools"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/vndk/v29" path="prebuilts/vndk/v29"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/vndk/v30" path="prebuilts/vndk/v30"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/vndk/v31" path="prebuilts/vndk/v31"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/vndk/v32" path="prebuilts/vndk/v32"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/prebuilts/vndk/v33" path="prebuilts/vndk/v33"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk-cw-fs,pdk-fs" name="platform/sdk" path="sdk"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/apex" path="system/apex"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/bpf" path="system/bpf"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/bpfprogs" path="system/bpfprogs"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/ca-certificates" path="system/ca-certificates"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/chre" path="system/chre"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/connectivity/wificond" path="system/connectivity/wificond"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/core" path="system/core"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/dmesgd" path="system/dmesgd"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/extras" path="system/extras"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/gatekeeper" path="system/gatekeeper"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/gsid" path="system/gsid"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/hardware/interfaces" path="system/hardware/interfaces"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/hwservicemanager" path="system/hwservicemanager"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/incremental_delivery" path="system/incremental_delivery"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/iorap" path="system/iorap"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/keymaster" path="system/keymaster"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/keymint" path="system/keymint"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/libartpalette" path="system/libartpalette"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libbase" path="system/libbase"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libcppbor" path="system/libcppbor"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/libfmq" path="system/libfmq"/>
      <project groups="mtk-hidl,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libhidl" path="system/libhidl"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libhwbinder" path="system/libhwbinder"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/libprocinfo" path="system/libprocinfo"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/librustutils" path="system/librustutils"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libsysprop" path="system/libsysprop"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libufdt" path="system/libufdt"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libvintf" path="system/libvintf"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/libziparchive" path="system/libziparchive"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/linkerconfig" path="system/linkerconfig"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/logging" path="system/logging"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/media" path="system/media"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/memory/libdmabufheap" path="system/memory/libdmabufheap"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/memory/libion" path="system/memory/libion"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/memory/libmeminfo" path="system/memory/libmeminfo"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/memory/libmemtrack" path="system/memory/libmemtrack"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/memory/libmemunreachable" path="system/memory/libmemunreachable"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/memory/lmkd" path="system/memory/lmkd"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/netd" path="system/netd"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/nfc" path="system/nfc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/nvram" path="system/nvram"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/security" path="system/security"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,mtk-vndk,pdk" name="platform/system/sepolicy" path="system/sepolicy"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/server_configurable_flags" path="system/server_configurable_flags"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/teeui" path="system/teeui"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/system/testing/gtest_extras" path="system/testing/gtest_extras"/>
      <project groups="mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/timezone" path="system/timezone"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/tools/aidl" path="system/tools/aidl"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/tools/hidl" path="system/tools/hidl"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/tools/mkbootimg" path="system/tools/mkbootimg">
        <linkfile dest="kernel/tools/mkbootimg" src="."/>
      </project>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/tools/sysprop" path="system/tools/sysprop"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/tools/xsdc" path="system/tools/xsdc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/unwinding" path="system/unwinding"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/update_engine" path="system/update_engine"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/system/vold" path="system/vold"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/test/app_compat/csuite" path="test/app_compat/csuite"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/test/dittosuite" path="test/dittosuite"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,projectarch,vts" name="platform/test/framework" path="test/framework"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/test/mlts/benchmark" path="test/mlts/benchmark"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/test/mlts/models" path="test/mlts/models"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-cw-fs,pdk-fs" name="platform/test/robolectric-extensions" path="test/robolectric-extensions"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/test/vts" path="test/vts"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/test/vts-testcase/hal" path="test/vts-testcase/hal"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/test/vts-testcase/hal-trace" path="test/vts-testcase/hal-trace"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/test/vts-testcase/kernel" path="test/vts-testcase/kernel"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,projectarch,vts" name="platform/test/vts-testcase/nbu" path="test/vts-testcase/nbu"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,projectarch,vts" name="platform/test/vts-testcase/performance" path="test/vts-testcase/performance"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,projectarch,vts" name="platform/test/vts-testcase/security" path="test/vts-testcase/security"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,vts" name="platform/test/vts-testcase/vndk" path="test/vts-testcase/vndk"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/aadevtools" path="tools/aadevtools"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,projectarch,tools,tradefed,vts" name="platform/tools/acloud" path="tools/acloud"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,tools" name="platform/tools/apifinder" path="tools/apifinder"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tradefed" name="platform/tools/apksig" path="tools/apksig"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tradefed" name="platform/tools/apkzlib" path="tools/apkzlib"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/asuite" path="tools/asuite"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/currysrc" path="tools/currysrc"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk-fs,tools" name="platform/tools/dexter" path="tools/dexter"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tools" name="platform/tools/doc_generation" path="tools/doc_generation"/>
      <project groups="mtk-sys-codebase,mtk-system,nopresubmit,pdk,tradefed" name="platform/tools/loganalysis" path="tools/loganalysis"/>
      <project groups="mtk-host,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk,tools" name="platform/tools/metalava" path="tools/metalava"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/ndkports" path="tools/ndkports"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/netsim" path="tools/netsim"/>
      <project groups="adt-infra,cts,developers,motodev,mtk-sys-codebase,mtk-system,pdk,tools,tradefed" name="platform/tools/repohooks" path="tools/repohooks"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tools" name="platform/tools/security" path="tools/security"/>
      <project groups="mtk-aosp-vnd,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="platform/tools/test/connectivity" path="tools/test/connectivity"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/test/graphicsbenchmark" path="tools/test/graphicsbenchmark"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/test/mobly_extensions" path="tools/test/mobly_extensions"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk" name="platform/tools/test/mobly_snippets" path="tools/test/mobly_snippets"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tradefed" name="platform/tools/tradefederation/prebuilts" path="tools/tradefederation/prebuilts"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tools" name="platform/tools/treble" path="tools/treble"/>
      <project groups="cts,mtk-sys-codebase,mtk-system,pdk,pdk-cw-fs,pdk-fs,tools" name="platform/tools/trebuchet" path="tools/trebuchet"/>
      <project groups="mtk-sys-codebase,mtk-system,pdk,tools,vendor" name="platform/tools/vendor/google_prebuilts/arc" path="tools/vendor/google_prebuilts/arc"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="quark/XMP-Toolkit-SDK" path="vendor/mediatek/opensource/external/XMP-Toolkit-SDK"/>
      <project groups="mtk-kernel,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="quark/kernel-6.1" path="kernel-6.1">
        <linkfile dest="kernel/kernel-6.1" src="."/>
      </project>
      <project groups="mtk-kernel,mtk-kernel-codebase,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="quark/kernel_device_modules-6.1" path="kernel/kernel_device_modules-6.1">
        <linkfile dest="kernel/build/build.sh" src="scripts/legacy_build.sh"/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vext,mtk-vnd-codebase" name="quark/lk2" path="vendor/mediatek/proprietary/bootable/bootloader/lk2"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-android" path="vendor/mediatek/proprietary/hardware/mtkcam-android"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-core" path="vendor/mediatek/proprietary/hardware/mtkcam-core"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-custom" path="vendor/mediatek/proprietary/hardware/mtkcam-custom"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-halif" path="vendor/mediatek/proprietary/hardware/mtkcam-halif"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-hwcore" path="vendor/mediatek/proprietary/hardware/mtkcam-hwcore"/>
      <project groups="mtk-cam,mtk-pure-vnd-codebase,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-hwprot-lib" path="vendor/mediatek/proprietary/hardware/mtkcam-hwprot-lib"/>
      <project groups="mtk-mgvi,mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-interfaces" path="vendor/mediatek/proprietary/hardware/mtkcam-interfaces"/>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase,mtkcam-decouple" name="quark/mtkcam-kernel_device_modules" path="vendor/mediatek/kernel_modules/mtkcam">
        <linkfile dest="kernel/vendor/mediatek/kernel_modules/mtkcam" src="."/>
      </project>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="quark/nlohmann-json" path="vendor/mediatek/opensource/external/nlohmann-json"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="quark/rapidjson" path="vendor/mediatek/opensource/external/rapidjson"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vext,mtk-vnd-codebase" name="quark/tf-a-2.10" path="vendor/mediatek/proprietary/trustzone/tf-a-2.10"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vext,mtk-vnd-codebase" name="quark/tf-a-2.4" path="vendor/mediatek/proprietary/trustzone/tf-a-2.4"/>
      <project groups="mtk-pure-vnd-codebase,mtk-trustzone,mtk-vext,mtk-vnd-codebase" name="quark/tf-a-2.6" path="vendor/mediatek/proprietary/trustzone/tf-a-2.6"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vext,mtk-vnd-codebase" name="quark/tf-a-2.8" path="vendor/mediatek/proprietary/trustzone/tf-a-2.8"/>
      <project groups="mtk-build-sys,mtk-pure-vnd-codebase,mtk-sys-codebase,mtk-vnd-codebase,pdk" name="toolchain/pgo-profiles"/>
      <project groups="mtk-kernel-codebase,mtk-kernel-module,mtk-pure-vnd-codebase,mtk-vnd-codebase" name="toolchain/prebuilts/ndk/r23" path="kernel/prebuilts/ndk-r23"/>
      <project groups="mtk-pure-vnd-codebase,mtk-sdk,mtk-sys-codebase,mtk-vnd-codebase,pdk,pdk-cw-fs,pdk-fs" name="tools/platform-compat"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="yocto/src/apps/atom-base/progs/rpc-binder-ut" path="vendor/mediatek/proprietary/external/rpc-binder-ut"/>
      <project groups="mtk-pure-vnd-codebase,mtk-vendor,mtk-vnd-codebase" name="yocto/src/apps/atom-base/progs/vsock-ut" path="vendor/mediatek/proprietary/external/vsock-ut"/>
      <!-- 以下为pvt私有仓库 -->
      <project groups="pvt" name="alps/vendor/mediatek/proprietary/trustzone/grt" path="vendor/mediatek/proprietary/trustzone/grt"/>
    </manifest>
