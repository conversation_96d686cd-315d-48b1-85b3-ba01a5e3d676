1. Prerequisites
----------------

A C compiler.  Any C89 or better compiler that supports variadic macros
should work.  Where supported, configure will attempt to enable the
compiler's run-time integrity checking options.  Some notes about
specific compilers:
 - clang: -ftrapv and -sanitize=integer require the compiler-rt runtime
  (CC=clang LDFLAGS=--rtlib=compiler-rt ./configure)

To support Privilege Separation (which is now required) you will need
to create the user, group and directory used by sshd for privilege
separation.  See README.privsep for details.


The remaining items are optional.

A working installation of zlib:
Zlib 1.1.4 or ******* or greater (earlier 1.2.x versions have problems):
https://zlib.net/

libcrypto from either of LibreSSL or OpenSSL.  Building without libcrypto
is supported but severely restricts the available ciphers and algorithms.
 - LibreSSL (https://www.libressl.org/) 3.1.0 or greater
 - OpenSSL (https://www.openssl.org) 1.1.1 or greater

LibreSSL/OpenSSL should be compiled as a position-independent library
(i.e. -fPIC, eg by configuring OpenSSL as "./config [options] -fPIC"
or LibreSSL as "CFLAGS=-fPIC ./configure") otherwise OpenSSH will not
be able to link with it.  If you must use a non-position-independent
libcrypto, then you may need to configure OpenSSH --without-pie.

If you build either from source, running the OpenSSL self-test ("make
tests") or the LibreSSL equivalent ("make check") and ensuring that all
tests pass is strongly recommended.

NB. If you operating system supports /dev/random, you should configure
libcrypto (LibreSSL/OpenSSL) to use it. OpenSSH relies on libcrypto's
direct support of /dev/random, or failing that, either prngd or egd.

PRNGD:

If your system lacks kernel-based random collection, the use of Lutz
Jaenicke's PRNGd is recommended. It requires that libcrypto be configured
to support it.

http://prngd.sourceforge.net/

EGD:

The Entropy Gathering Daemon (EGD) supports the same interface as prngd.
It also supported only if libcrypto is configured to support it.

http://egd.sourceforge.net/

PAM:

OpenSSH can utilise Pluggable Authentication Modules (PAM) if your
system supports it. PAM is standard most Linux distributions, Solaris,
HP-UX 11, AIX >= 5.2, FreeBSD, NetBSD and Mac OS X.

Information about the various PAM implementations are available:

Solaris PAM:	http://www.sun.com/software/solaris/pam/
Linux PAM:	http://www.kernel.org/pub/linux/libs/pam/
OpenPAM:	http://www.openpam.org/

If you wish to build the GNOME passphrase requester, you will need the GNOME
libraries and headers.

GNOME:
http://www.gnome.org/

Alternatively, Jim Knoble <<EMAIL>> has written an excellent X11
passphrase requester. This is maintained separately at:

http://www.jmknoble.net/software/x11-ssh-askpass/

LibEdit:

sftp supports command-line editing via NetBSD's libedit.  If your platform
has it available natively you can use that, alternatively you might try
these multi-platform ports:

http://www.thrysoee.dk/editline/
http://sourceforge.net/projects/libedit/

LDNS:

LDNS is a DNS BSD-licensed resolver library which supports DNSSEC.

http://nlnetlabs.nl/projects/ldns/

Autoconf:

If you modify configure.ac or configure doesn't exist (eg if you checked
the code out of git yourself) then you will need autoconf-2.69 and
automake-1.16.1 to rebuild the automatically generated files by running
"autoreconf".  Earlier versions may also work but this is not guaranteed.

http://www.gnu.org/software/autoconf/
http://www.gnu.org/software/automake/

Basic Security Module (BSM):

Native BSM support is known to exist in Solaris from at least 2.5.1,
FreeBSD 6.1 and OS X.  Alternatively, you may use the OpenBSM
implementation (http://www.openbsm.org).

makedepend:

https://www.x.org/archive/individual/util/

If you are making significant changes to the code you may need to rebuild
the dependency (.depend) file using "make depend", which requires the
"makedepend" tool from the X11 distribution.

libfido2:

libfido2 allows the use of hardware security keys over USB.  libfido2
in turn depends on libcbor.  libfido2 >= 1.5.0 is strongly recommended.
Limited functionality is possible with earlier libfido2 versions.

https://github.com/Yubico/libfido2
https://github.com/pjk/libcbor


2. Building / Installation
--------------------------

To install OpenSSH with default options:

./configure
make
make install

This will install the OpenSSH binaries in /usr/local/bin, configuration files
in /usr/local/etc, the server in /usr/local/sbin, etc. To specify a different
installation prefix, use the --prefix option to configure:

./configure --prefix=/opt
make
make install

Will install OpenSSH in /opt/{bin,etc,lib,sbin}. You can also override
specific paths, for example:

./configure --prefix=/opt --sysconfdir=/etc/ssh
make
make install

This will install the binaries in /opt/{bin,lib,sbin}, but will place the
configuration files in /etc/ssh.

If you are using PAM, you may need to manually install a PAM control
file as "/etc/pam.d/sshd" (or wherever your system prefers to keep
them).  Note that the service name used to start PAM is __progname,
which is the basename of the path of your sshd (e.g., the service name
for /usr/sbin/osshd will be osshd).  If you have renamed your sshd
executable, your PAM configuration may need to be modified.

A generic PAM configuration is included as "contrib/sshd.pam.generic",
you may need to edit it before using it on your system. If you are
using a recent version of Red Hat Linux, the config file in
contrib/redhat/sshd.pam should be more useful.  Failure to install a
valid PAM file may result in an inability to use password
authentication.  On HP-UX 11 and Solaris, the standard /etc/pam.conf
configuration will work with sshd (sshd will match the other service
name).

There are a few other options to the configure script:

--with-audit=[module] enable additional auditing via the specified module.
Currently, drivers for "debug" (additional info via syslog) and "bsm"
(Sun's Basic Security Module) are supported.

--with-pam enables PAM support. If PAM support is compiled in, it must
also be enabled in sshd_config (refer to the UsePAM directive).

--with-prngd-socket=/some/file allows you to enable EGD or PRNGD
support and to specify a PRNGd socket. Use this if your Unix lacks
/dev/random.

--with-prngd-port=portnum allows you to enable EGD or PRNGD support
and to specify a EGD localhost TCP port. Use this if your Unix lacks
/dev/random.

--with-lastlog=FILE will specify the location of the lastlog file.
./configure searches a few locations for lastlog, but may not find
it if lastlog is installed in a different place.

--without-lastlog will disable lastlog support entirely.

--with-osfsia, --without-osfsia will enable or disable OSF1's Security
Integration Architecture.  The default for OSF1 machines is enable.

--with-utmpx enables utmpx support. utmpx support is automatic for
some platforms.

--without-shadow disables shadow password support.

--with-ipaddr-display forces the use of a numeric IP address in the
$DISPLAY environment variable. Some broken systems need this.

--with-default-path=PATH allows you to specify a default $PATH for sessions
started by sshd. This replaces the standard path entirely.

--with-pid-dir=PATH specifies the directory in which the sshd.pid file is
created.

--with-xauth=PATH specifies the location of the xauth binary

--with-ssl-dir=DIR allows you to specify where your Libre/OpenSSL
libraries are installed.

--with-ssl-engine enables Libre/OpenSSL's (hardware) ENGINE support

--without-openssl builds without using OpenSSL.  Only a subset of ciphers
and algorithms are supported in this configuration.

--without-zlib builds without zlib.  This disables the Compression option.

--with-4in6 Check for IPv4 in IPv6 mapped addresses and convert them to
real (AF_INET) IPv4 addresses. Works around some quirks on Linux.

If you need to pass special options to the compiler or linker, you
can specify these as environment variables before running ./configure.
For example:

CC="/usr/foo/cc" CFLAGS="-O" LDFLAGS="-s" LIBS="-lrubbish" ./configure

3. Configuration
----------------

The runtime configuration files are installed by in ${prefix}/etc or
whatever you specified as your --sysconfdir (/usr/local/etc by default).

The default configuration should be instantly usable, though you should
review it to ensure that it matches your security requirements.

To generate a host key, run "make host-key". Alternately you can do so
manually using the following commands:

    ssh-keygen -t [type] -f /etc/ssh/ssh_host_key -N ""

for each of the types you wish to generate (rsa, dsa or ecdsa) or

    ssh-keygen -A

to generate keys for all supported types.

Replacing /etc/ssh with the correct path to the configuration directory.
(${prefix}/etc or whatever you specified with --sysconfdir during
configuration).

If you have configured OpenSSH with EGD/prngd support, ensure that EGD or
prngd is running and has collected some entropy first.

For more information on configuration, please refer to the manual pages
for sshd, ssh and ssh-agent.

4. (Optional) Send survey
-------------------------

$ make survey
[check the contents of the file "survey" to ensure there's no information
that you consider sensitive]
$ make send-survey

This will send configuration information for the currently configured
host to a survey address.  This will help determine which configurations
are actually in use, and what valid combinations of configure options
exist.  The raw data is available only to the OpenSSH developers, however
summary data may be published.

5. Problems?
------------

If you experience problems compiling, installing or running OpenSSH,
please refer to the "reporting bugs" section of the webpage at
https://www.openssh.com/
