<?xml version="1.0" encoding="UTF-8"?>
<manifest>
  <!-- 远程仓库定义 -->
  <remote  name="origin"
           fetch="ssh://10.0.12.208:29418/" />
  <!-- 默认设置（可选，如果所有项目都使用相同的远程和分支） -->
  <!-- 但在这个例子中，我们将直接在project元素中指定revision -->
  <!--
  <default revision="dev"
           remote="origin"
           sync-j="4" />
  -->
  <!-- 第一个Git仓库，下载dev分支 -->
  <project path="SERES_DDS/SERES_CPP_RPC"
           name="SERES_DDS/SERES_CPP_RPC"
           revision="dev"
           remote="origin" />
  <!-- 第二个Git仓库，同样下载dev分支 -->
  <project path="SERES_DDS/SERES_C_RPC"
           name="SERES_DDS/SERES_C_RPC"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/SERES_DDS_TOOLS"
           name="SERES_DDS/SERES_DDS_TOOLS"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/SERES_PY_RPC"
           name="SERES_DDS/SERES_PY_RPC"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/cicd"
           name="SERES_DDS/cicd"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/cyclonedds"
           name="SERES_DDS/cyclonedds"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/cyclonedds-cxx"
           name="SERES_DDS/cyclonedds-cxx"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/cyclonedds-python"
           name="SERES_DDS/cyclonedds-python"
           revision="dev"
           remote="origin" />
    <project path="SERES_DDS/mcu_dds"
           name="SERES_DDS/mcu_dds"
           revision="dev"
           remote="origin" />
</manifest>
