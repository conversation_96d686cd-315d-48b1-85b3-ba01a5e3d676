#!/bin/bash
echo ------------------------------------------------------------------
echo - USING JOB SCRIPT IN SVN \$Revision: 4359 $ 
echo ------------------------------------------------------------------
echo - Running time : $(date "+%Y/%m/%d %H:%M:%S")

# Definition - path may be used in script, use the definition as much as possible
__EP_NUMB__=${JENKINS_EP_NUMBER}
__JOB_TYP__=${JENKINS_JOB_TYPE}
__SUB_PRO__=${JENKINS_SUB_PROJ}

__USER_FD__="/home/<USER>"
__ROOT_FD__=${__USER_FD__}/Jenkins/${__EP_NUMB__}/${__JOB_TYP__}/${__SUB_PRO__}

# Enter into project root folder
echo - Enter into project root folder ${__ROOT_FD__}/SERES_DDS/cicd
cd ${__ROOT_FD__}/SERES_DDS/cicd


docker run --rm -t --security-opt seccomp=unconfined --name dds_compile_container -v /home/<USER>/Jenkins:/home/<USER>/Jenkins  hb.seres.cn/eea3.0/dds_compile_image:init bash /home/<USER>/Jenkins/00000/${JENKINS_JOB_TYPE}/LINUX_JM3.0_dev/SERES_DDS/cicd/ci_build.sh