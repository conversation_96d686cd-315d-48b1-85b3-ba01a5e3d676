#!/bin/bash

set -e

# 默认参数值
jobs=1
yocto_flag=false
help_flag=false
clean_flag=false
x86_flag=false
ota_duc_home=../../SERES_OTA/mt8678_DUC
shell_path=`pwd`
install_prefix=`pwd`/output_ota

# 使用 getopt 处理长参数和短参数
options=$(getopt -o j:xyhac -l jobs:,x86,yocto,android,help -- "$@")
if [ $? != 0 ]; then
    echo "getopt error"
    exit 1
fi

eval set -- "$options"

# 解析命令行参数
while true; do
    case "$1" in
        -j | --jobs)
            jobs=$2
            shift 2
            ;;
        -y | --yocto)
            yocto_flag=true
            shift
            ;;
        -x | --x86)
            x86_flag=true
            shift
            ;;
        -h | --help)
            help_flag=true
            shift
            ;;
        -c)
            clean_flag=true
            shift
            ;;
        --)
            shift
            break
            ;;
        *)
            echo "Invalid option: $1" >&2
            help_flag=true
            ;;
    esac
done

# ---------- 函数块 ---------

show_helps() {
    echo "Usage: $0 [-j <jobs>] [--yocto] [-h] [--android]"
    echo "  -j, --jobs <jobs>       Number of jobs to run in parallel (default: 1)"
    echo "  -y, --yocto             Enable Yocto build mode"
    echo "  -h, --help              Display this help message"
    echo "  -x, --x86               Enable x86 build mode"
    echo "  -c,                     Clean project"
}

show_parameters() {
    echo -e "\e[32mBuilder parameters:\e[0m"
    echo "-- Number of jobs     : [$jobs]"
    echo "-- Yocto build mode   : [$yocto_flag]"
    echo "-- x86 build mode     : [$x86_flag]"
}

build_ota_duc() {
    echo -e "\e[32mStart build OTA DUC project\e[0m"
    cd $ota_duc_home

    if [ "$yocto_flag" = true ]; then
        echo -e "\e[32mEnabling Yocto build mode\e[0m"
        ./lunch.sh --yocto --output $install_prefix --doip
    fi

    if [ "$x86_flag" = true ]; then
        echo -e "\e[32mEnabling X86 build mode\e[0m"
        ./lunch.sh --x86 --output $install_prefix --doip
    fi
}

clean_project() {
    cd $shell_path

    if [ "$yocto_flag" = true ]; then
        rm -rf output_ota/yocto
    fi

    if [ "$x86_flag" = true ]; then
        rm -rf output_ota/x86
    fi
}


# -------- 逻辑区 ----------

# 显示帮助信息
if [ "$help_flag" = true ]; then
    show_helps
    exit 0
fi

# 清理编译产物
if [ "$clean_flag" = true ]; then
    echo -e "\e[32mStart clean project\e[0m"
    clean_project
    echo -e "\e[32mSuccess clean project\e[0m"
    exit 1
fi

# 编译参数打印
show_parameters

build_ota_duc
