{"name": "jenkins.configuration.project", "description": "configuratin used to set build script path and artifacts assimble", "version": "$Id: ProjectConfig.json 7212 2020-05-08 06:21:23Z MYAN2 $", "artifacts": [{"folder": "a_Package_DEV", "description": "destination can only be Cal HOST VIP GIP ICS for pre-definition.", "contents": [{"from": "SERES_DDS/cicd/output/", "to": "SERES_DDS/cicd/output/", "rename": ""}]}, {"folder": "b_Package_USB", "description": "it contains USB update packages, USB update should work if user put contents in this folder directly into USB root path.", "contents": [{"from": "", "to": "", "rename": ""}]}, {"folder": "c_Package_VBF", "description": "", "contents": [{"from": "", "to": "", "rename": ""}]}, {"folder": "d_Package_PLANT", "description": "it contains offline burning package for plant manufacture", "contents": [{"from": "", "to": "", "rename": ""}]}, {"folder": "f_Package_vmlinux_ko", "description": "it contains OTA update packages, OTA update should work if user put contents in this folder directly into OTA server.", "contents": [{"from": "", "to": "", "rename": ""}]}, {"folder": "z_Package_UserDef", "description": "it contains user-defined packages which is not list above.", "contents": [{"from": "", "to": "", "rename": ""}]}], "build_scripts": [{"type": "Nightly", "path": "SERES_DDS/cicd", "name": "docker_build.sh"}, {"type": "Release", "path": "SERES_DDS/cicd", "name": "docker_build.sh"}, {"type": "Prebuild", "path": "build/tools", "name": "integration_prebuild.sh"}, {"type": "Coverity", "path": "", "name": ""}, {"type": "Unittest", "path": "", "name": ""}]}