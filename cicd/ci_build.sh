#!/bin/bash
export JAVA_HOME=$(dirname $(dirname $(readlink -f $(which javac))))
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd "$SCRIPT_DIR"
bash build_scom.sh  -x -j 32
bash build_scom.sh  -y -j 32
bash build_scom.sh  -a -j 32
#docker run --rm -t --security-opt seccomp=unconfined --name dds_compile_container -v /home/<USER>/Jenkins:/home/<USER>/Jenkins  hb.seres.cn/eea3.0/dds_compile_image:init bash /home/<USER>/Jenkins/00000/${__JOB_TYP__}/LINUX_JM3.0_dev/SERES_DDS/cicd/ci_build.sh
