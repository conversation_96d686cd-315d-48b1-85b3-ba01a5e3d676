#pragma once
#include <functional>
#include <memory>
#include "format.hpp"

template <typename T>
class IDoipChannel {
public:
    virtual ~IDoipChannel() = default;

    virtual void Connect(const std::string& ip) = 0;
    virtual void Disconnect(const std::string& ip) = 0;
    virtual void ConnectAsync(const std::string& ip) = 0;
    virtual void DisconnectAsync(const std::string& ip) = 0;
    virtual void CloseChannel() = 0;
    
    virtual void Read(T& payload) = 0;
    virtual void Write(const T& payload) = 0;
    virtual void WriteAsync(const T& payload) = 0;
    
    using CallbackType = std::function<void(const T&)>;
    virtual void RegisterCallback(const CallbackType& callback) = 0;
    using ListenerCallback = std::function<void(ListenerEventType , ListenerEventInfo)>;
    virtual void RegisterListener(const ListenerCallback& callback) = 0;

    virtual void RegisterListenerEvent(ListenerEventType mask) = 0;
}; 