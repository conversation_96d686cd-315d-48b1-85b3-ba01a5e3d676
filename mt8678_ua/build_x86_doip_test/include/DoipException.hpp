#pragma once
#include <string>
#include <sstream>

class DoipException : public std::exception {
private:
    std::string errorMessage;

public:
    // 构造函数，接收原始错误信息并添加前缀
    DoipException(const std::string& msg) {
        std::ostringstream oss;
        oss << "[Doip exception] " << msg;
        errorMessage = oss.str();
    }

    DoipException(const std::string& msg, const char* function, int line) {
        std::ostringstream oss;
        oss << "[Doip] " << function << "()" << " - " << line << " : " << msg;
        errorMessage = oss.str();
    }

    // 重写 what() 方法，返回包含前缀的错误信息
    const char* what() const noexcept override {
        return errorMessage.c_str();
    }
};