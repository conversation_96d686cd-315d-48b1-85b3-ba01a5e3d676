#ifndef __UA_INSTALL_MCU_STATE_STATE_H_
#define __UA_INSTALL_MCU_STATE_STATE_H_

#include <memory>
#include <string>

#include "format.hpp"
#include "ua_state.h"
#include "obt/ota_doip_channel.h"
#include "obt/obt_update_ecu_task.h"

namespace seres {
namespace ua {
class InstallMcuState : public UaStateBase,
                        public std::enable_shared_from_this<InstallMcuState> {
 public:
  InstallMcuState();
  virtual ~InstallMcuState() = default;
  virtual void onEntry();
  virtual bool onEvent(std::shared_ptr<StateParam>& param);
  virtual void onExit();
  virtual void onResume();
 private:
  void enter();
  void UpdateResultCallback(uint16_t addr, int32_t ret);
  std::vector<unsigned char> readBinaryFile(const std::string& filePath);
  void ota1ReadCallback(const OtaDataInfo& data) ;

  std::shared_ptr<ObtUpdateEcuTask> task1; 
};

}  // namespace ua
}  // namespace seres

#endif
