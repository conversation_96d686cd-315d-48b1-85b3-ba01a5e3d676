#include "install_mcu_state.h"

#include <fstream>
#include <functional>
#include <iomanip>
#include <iostream>
#include <thread>

#include "messageloop/message_loop.h"
#include "obt/obt_update_ecu_task.h"
#include "persistance/ua_persistence.h"
#include "ua_log.h"
#include "ua_service.h"
#include "unity/ua_common_definition.h"

namespace seres {
namespace ua {
InstallMcuState::InstallMcuState() : UaStateBase(kUaIntallMcuState) {
  setActivable(true);
}

void InstallMcuState::onEntry() {
  StdOutLogger::GetInstance().Info("enter install mcu  state");
  PERSIST.SetHasTask(true);
  PERSIST.SetUaSm(UA_STATE_INSTALL_MCU);
  seres::base::MessageLoop::GetInstance().PostDelayedTask(
      std::make_shared<seres::base::Message>(
          std::bind(&InstallMcuState::enter, shared_from_this()), getName()),
      2 * 1000);
}

bool InstallMcuState::onEvent(std::shared_ptr<StateParam> &event) {}

void InstallMcuState::onExit() {}

void InstallMcuState::onResume() {
  StdOutLogger::GetInstance().Info("enter UpdateManageState onResume");
}

void InstallMcuState::enter() {
  std::thread::id threadId = std::this_thread::get_id();
  std::cout << "Thread ID (hex): " << std::hex << threadId << std::endl;
  task1 = std::make_shared<ObtUpdateEcuTask>(0x0064);
  task1->SetResultCallback(std::bind(&InstallMcuState::UpdateResultCallback,
                                     shared_from_this(), std::placeholders::_1,
                                     std::placeholders::_2));
  task1->Enter();
}

void InstallMcuState::UpdateResultCallback(uint16_t addr, int32_t ret) {
  std::thread::id threadId = std::this_thread::get_id();
  std::cout << "UpdateResultCallback ret: " << ret;
}

}  // namespace ua
}  // namespace seres
