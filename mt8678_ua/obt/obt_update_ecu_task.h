#ifndef OBT_UPDATE_ECU_TASK_H_
#define OBT_UPDATE_ECU_TASK_H_

// #include <unistd.h>
#include <stdint.h>
#include <functional>
#include <fstream>
#include "format.hpp"
#include "obt_define.h"
// #include <fstream>
// #include <vector>

// // #include "EventBase.h"
// // #include "ObtReqEvent.h"
// // #include "ObtRspEvent.h"
namespace seres {
namespace ua {

using resultCallback = std::function<void(uint16_t addr, int32_t ret)>;

class ObtUpdateEcuTask {
 public:
  struct memInfo {
    uint32_t dataBlkMemAddr;
    uint32_t dataBlkLen;
  };

  //   enum OBT_PUB_KEY_TYPE { OBT_PUB_KEY_TYPE_DEV = 0, OBT_PUB_KEY_TYPE_PRO =
  //   1 };

 public:
  ObtUpdateEcuTask(uint16_t addr);
  virtual ~ObtUpdateEcuTask();

  //   bool on_event(std::shared_ptr<EventBase>& evt);

  void Enter();
  bool CancelUpdate();
  bool CancelUpdate(uint16_t addr);
  void Exit();
  void Cancel();

  void SetResultCallback(resultCallback cb) { rst_callback_ = cb; };
  void RawDataRespCallback(const OtaDataInfo& data);
  void RequestToChangeSession(unsigned char expect_session);
  void CheckPreCondition();
  void SecurityAccess();
  void SendSeed();
  void EraseMemory();
  void RequestDownload();
  void DataTransfer();
  void TransferExit();
  void VerifyAuth();
  //   void set_already_stop() { already_stop_flag_ = true; }

 private:
  void RequestToChangeSessionHandle(OtaDataInfo data);
  void CheckPreConditionHandle(OtaDataInfo data);
  void RequestDownloadHandle(OtaDataInfo data);
  void SecurityAccessHandle(OtaDataInfo data);
  void SendSeedHandle(OtaDataInfo data);
  void EraseMemoryHandle(OtaDataInfo data);
  void DataTransferHandle(OtaDataInfo data);
  void TransferExitHandle(OtaDataInfo data);
  void VerifyAuthHandle(OtaDataInfo data);
  std::vector<unsigned char> parseHexFile(const std::string& filePath, size_t filePosition, uint16_t maxBytes);
  
  //   void update_ecu(std::shared_ptr<ObtReqUpdateEventMsg>& evt);

  //   bool parse_vbf_file(std::string& file);

  //   void sort_files(std::vector<std::string> files);

  //   int32_t update_start(bool afterSbl = false);

  //   int32_t update_sbl();

  //   int32_t update_non_sbl();

  //   int32_t read_pub_key();

  //   // 27, unlock for download
  //   int32_t unlock_for_download(uint8_t level, bool seed = false,
  //                               std::array<uint8_t, 3> key = {0});

  //   // 31, verify authenticity
  //   int32_t verify_auth();

  //   // 31, verify pkg integrity
  //   int32_t verify_pkg_integrity();

  //   // 31. activate SBL
  //   int32_t activate_sbl();

  //   // 31, erase memory
  //   int32_t erase_memory();

  //   // 34
  //   int32_t req_download();

  //   // 36
  //   int32_t data_transfer();

  //   // 37
  //   int32_t transfer_exit();

  //   int32_t update_prepare(bool afterSbl = false);

  //   // uds resp
  //   void handle_update_rsp(std::shared_ptr<EventBase>& evt);

  //   int32_t generic_key_for27(std::vector<uint8_t> seed, uint64_t constant,
  //                             std::array<uint8_t, 3>& key);

  //   // update next file
  //   void next_file(uint16_t addr);

  //   void notify_update_progress(const uint16_t addr, ObtUpdateState
  //   updateState,
  //                               UpdateErrCode errCode);

  //   // return errcode by task
  //   UpdateErrCode task_to_errcode(const OBT_TASK_ID task);

  //   // raw data rsp
  //   void uds_raw_data_rsp(std::shared_ptr<EventBase>& evt);
  //   void print_uds_data(std::shared_ptr<ObtSendUdsRawDataRspEvent>& evt);

  //   // 0x22
  //   void uds_raw_data_read_pub_key_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x27
  //   void uds_raw_data_unlock_for_download_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x31
  //   void uds_raw_data_routine_ctrl_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x31
  //   void uds_raw_data_verify_auth_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x31
  //   void uds_raw_data_activate_sbl_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x31
  //   void uds_raw_data_verify_integrity_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x31
  //   void uds_raw_data_erase_memory_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x34
  //   void uds_raw_data_req_down_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x36
  //   void uds_raw_data_transfer_data_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);
  //   // 0x37
  //   void uds_raw_data_transfer_exit_handle(
  //       std::shared_ptr<ObtSendUdsRawDataRspEvent>& pData);

 private:
  ObtUpdateEcuTask(const ObtUpdateEcuTask&);
  ObtUpdateEcuTask& operator=(const ObtUpdateEcuTask&);

 private:
  int32_t fd_;
  int16_t curr_addr;
  string curr_file_;
  vector<string> files_;
  //   uint64_t file_idx_;
  //   uint8_t addr_type_;

  //   //  parse from vbf version section
  //   string vbf_version_;  //  2.5: without a signature; 2.6: with a signature
  //   //  parse from vbf header section
  //   string sw_type_;                 //  sw paty type: EXE/SBL...
  //   uint16_t addr_;                  //  ecu address
  //   uint8_t format_id_;              //  a data compression/encryption
  //   vector<uint8_t> signature_;      //  sop: 31 02 12 verify authenticity
  //   after
  //                                    //  transfering one vbf file
  //   vector<uint8_t> signature_dev_;  //  test: 31 02 12 verify authenticity
  //   after
  //                                    //  transfering one vbf file
  //   uint32_t call_;                  //  vbt mem addr, for activate sbl
  //   vector<memInfo> mem_info_;  //  erase memory info, 31 01 FF 01(including
  //   vbt
  //                               //  block)/activate sbl: 31 01 03 01
  //   uint32_t data_blk_idx_;     //  had transfered data block counter
  //   off_t cursor_pos_;

  //   //  parse from vbf data section
  uint32_t mem_addr_;      //  data block start address
  //   uint64_t blk_data_len_;  //  the data length that one data block

  uint8_t blk_counter_;       //  block sequence counter for 36
  std::ifstream hexFile_;
  size_t filePosition_;
  size_t curr_filePosition_;
  std::vector<unsigned char> buffer;
  //   uint64_t pin_code_master_;  //  pin code for 27 generic key
  //   uint64_t pin_code_slave_;   //  pin code bak for 27 generic key
  uint16_t max_blk_len_;      //  34 rsp value
  uint64_t currrnt_buffer_len_;       //  transfer data len for 36
  //   uint64_t updted_size_;      //  had update size
  OBT_TASK_ID curr_task_;  //  current task

  //   ObtUpdateEcuTask::OBT_PUB_KEY_TYPE
  //       pub_key_type_;        // OBT_PUB_KEY_TYPE_DEV: using m_signatureDev;
  //                             // OBT_PUB_KEY_TYPE_PRO: using m_signature
  //   bool auth_verify_retry_;  // use another signature to verify if cur
  //   signature
  //                             // is fail

  //   bool notify_app_flag_;
  //   bool already_stop_flag_;
  //   bool is_seed_;
  //   bool pin_code_slave_flag_;
  //   bool sid_36_retry_flag_;
  //   vector<uint8_t> seed_data_;
  //   updateResultCB
  //       notify_app_cb_;            // notify app the feedback and result of
  //       update
  resultCallback rst_callback_;  // notify updateMgr to deal mcuTask
};

}  // namespace ua
}  // namespace seres

#endif /* OBT_UPDATE_MCU_TASK_H_   */