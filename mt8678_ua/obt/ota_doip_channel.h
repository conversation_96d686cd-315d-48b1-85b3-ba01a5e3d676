#pragma once
#include <memory>
#include <mutex>

#include "DoipFactory.hpp"

namespace seres {
namespace ua {

class OtaDoipChannel {
 public:
  static OtaDoipChannel& GetInstance() {
    static OtaDoipChannel instance;
    return instance;
  }
  OtaDoipChannel(const OtaDoipChannel&) = delete;
  OtaDoipChannel& operator=(const OtaDoipChannel&) = delete;

  void Connect(const std::string& ip);

  void SendDataSync(unsigned short TA, const std::vector<unsigned char>& udsReq,
                    std::vector<unsigned char>& response);

  void SendDataAsync(unsigned short TA,
                     const std::vector<unsigned char>& udsReq);

  void RegisterDataHandler(unsigned short TA,
                           std::function<void(const OtaDataInfo&)> handler);
  // void RegisterDataHandler(std::function<void(const OtaDataInfo&)> handler);

  void UnRegisterDataHandler(uint16_t addr);

  void OnDataReceived(const OtaDataInfo& data);

 private:
  OtaDoipChannel();

  std::unique_ptr<IDoipChannel<OtaDataInfo>> m_channel;
  std::mutex m_mutex;
//   std::function<void(const OtaDataInfo&)> m_dataHandler;
  std::unordered_map<uint16_t, std::function<void(const OtaDataInfo&)>>
      callback_map_{};
};

}  // namespace ua
}  // namespace seres