#include "ota_doip_channel.h"

#include <arpa/inet.h>

#include <iomanip>
#include <iostream>
#include "DoipException.hpp"
#include "Interface.hpp"
#include "../messageloop/message_loop.h"

constexpr const char* kDefaultIP = "127.0.0.1";
constexpr unsigned short kDefaultTA = 0x0064;

namespace seres {
namespace ua {
OtaDoipChannel::OtaDoipChannel() {
  m_channel = DoipFactory::create_channel<OtaDataInfo>(0x000B, 13400);
  m_channel->RegisterCallback([this](const OtaDataInfo& data) {
    OnDataReceived(data);
  });
}
void OtaDoipChannel::Connect(const std::string& ip) {
  std::lock_guard<std::mutex> lock(m_mutex);
  try {
    m_channel->ConnectAsync(ip);

  } catch (const DoipException& e) {
    std::cerr << e.what() << std::endl;
  }
}

void OtaDoipChannel::SendDataSync(unsigned short TA,
                                  const std::vector<unsigned char>& udsReq,
                                  std::vector<unsigned char>& response) {
  OtaDataInfo payload;
  payload.ip = kDefaultIP;  // 默认目标 IP（可根据需求扩展）
  payload.TA = kDefaultTA;
  payload.uds_req = udsReq;
  payload.uds_resp = response;
  try {
    m_channel->Write(payload);
  } catch (const DoipException& e) {
    std::cerr << e.what() << std::endl;
  }
}

void OtaDoipChannel::SendDataAsync(unsigned short TA,
                                   const std::vector<unsigned char>& udsReq) {
  OtaDataInfo payload;
  payload.ip = kDefaultIP;  // 默认目标 IP（可根据需求扩展）
  payload.TA = kDefaultTA;
  payload.uds_req = udsReq;

  std::cout << "Sending UDS Request (" << std::dec << udsReq.size()
            << " bytes):" << std::endl;
  std::cout << "Hex: ";
  for (unsigned char byte : udsReq) {
    std::cout << std::hex << std::setw(2) << std::setfill('0')
              << static_cast<int>(byte) << " ";
  }
  std::cout << std::endl;

  try {
    m_channel->WriteAsync(payload);
  } catch (const DoipException& e) {
    std::cerr << e.what() << std::endl;
  }
}

void OtaDoipChannel::RegisterDataHandler(
    unsigned short TA, std::function<void(const OtaDataInfo&)> handler) {
  std::lock_guard<std::mutex> lock(m_mutex);
  callback_map_[TA] = handler;
}

void OtaDoipChannel::OnDataReceived(const OtaDataInfo& data) {
  std::cout << "OnDataReceived";
  std::lock_guard<std::mutex> lock(m_mutex);
  auto it = callback_map_.find(0x0064);  // 根据目标地址路由回调
  if (it != callback_map_.end()) {
    it->second(data);  // 触发对应任务的回调
    // seres::base::MessageLoop::GetInstance().PostTask(
    //   std::make_shared<seres::base::Message>(std::bind(&(it->second), data)));
  }
}

void OtaDoipChannel::UnRegisterDataHandler(unsigned short TA) {
  std::lock_guard<std::mutex> lock(m_mutex);
  auto it = callback_map_.find(TA);  // 根据目标地址路由回调
  if (it != callback_map_.end()) {
    callback_map_.erase(it);  // 触发对应任务的回调
  }
}

}  // namespace ua
}  // namespace seres