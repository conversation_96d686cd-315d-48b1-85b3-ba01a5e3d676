#ifndef OBT_DEFINE_H_
#define OBT_DEFINE_H_

#include <list>
#include <string>

using namespace std;

static const int32_t EXCEPT_TIMEOUT_DEFAULT_VALUE = -1;
static const int32_t EXCEPT_RETRY_NUM_1 = 1;
static const int32_t EXCEPT_RETRY_NUM_2 = 2;
static const int32_t EXCEPT_RETRY_NUM_3 = 3;

/* addr type */
enum OBT_ADDR_TYPE {
  OBT_ADDR_TYPE_PHY = 0x00,
  OBT_ADDR_TYPE_FUNC = 0x01,
  OBT_ADDR_TYPE_FUNC_SPEC_WAY = 0x02
};

/* obt event id */
enum OBT_EVENT_ID {
  // update
  OBT_EVENT_ID_UPDATE = 1,
  // cancel update
  OBT_EVENT_ID_CANCEL_UPDATE,
  // session ctrl
  OBT_EVENT_ID_SESSION_CTRL,
  // session control rsp
  OBT_EVENT_ID_SESSION_CTRL_RSP,
  // file transfer
  OBT_EVENT_ID_FILE_TRANSFER,
  // security access
  OBT_EVENT_ID_SECURITY_ACCESS,
  // security access rsp
  OBT_EVENT_ID_SECURITY_ACCESS_RSP,
  // routine ctrl
  OBT_EVENT_ID_ROUTINE_CTRL,
  // routine ctrl rsp
  OBT_EVENT_ID_ROUTINE_CTRL_RSP,
  // ecu reset
  OBT_EVENT_ID_ECU_RESET,
  // ecu reset rsp
  OBT_EVENT_ID_ECU_RESET_RSP,
  // clear dtc
  OBT_EVENT_ID_CLEAR_DTC,
  // clear dtc rsp
  OBT_EVENT_ID_CLEAR_DTC_RSP,
  // read did
  OBT_EVENT_ID_READ_DID,
  // read did rsp
  OBT_EVENT_ID_READ_DID_RSP,
  // write did
  OBT_EVENT_ID_WRITE_DID,
  // write did rsp
  OBT_EVENT_ID_WRITE_DID_RSP,
  // tester present
  OBT_EVENT_ID_TESTER_PRESNET,
  // tester present rsp
  OBT_EVENT_ID_TESTER_PRESNET_RSP,
  // req download
  OBT_EVENT_ID_REQ_DOWNLOAD,
  // req download rsp
  OBT_EVENT_ID_REQ_DOWNLOAD_RSP,
  // req data transfer
  OBT_EVENT_ID_DATA_TRANSFER,
  // data transfer rsp
  OBT_EVENT_ID_DATA_TRANSFER_RSP,
  // req transfer exit
  OBT_EVENT_ID_REQ_TRANSFER_EXIT,
  // req transfer exit rsp
  OBT_EVENT_ID_REQ_TRANSFER_EXIT_RSP,
  // req download(34/36/37)
  OBT_EVENT_ID_REQ_DOWNLOAD_34_36_37,
  // req func addr spec way
  OBT_EVENT_ID_REQ_FUNC_ADDR_SPEC_WAY,
  // req func addr spec way rsp
  OBT_EVENT_ID_REQ_FUNC_ADDR_SPEC_WAY_RSP,
  // routine activate status rsp
  OBT_EVENT_ID_ROUTINE_ACTIVATE_STATUS_RSP,
  // req send uds raw data
  OBT_EVENT_ID_REQ_SEND_UDS_RAW_DATA,
  // req send uds raw data rsp
  OBT_EVENT_ID_REQ_SEND_UDS_RAW_DATA_RSP,

  // app send tester present
  OBT_EVENT_ID_TESTER_PRESNET_APP,
  OBT_EVENT_ID_TESTER_PRESNET_APP_RSP,
  // collection version msg id
  OBT_EVENT_ID_COL_VERSION_MSG,
  // stop collect version msg id
  OBT_EVENT_ID_STOP_COL_VERSION_MSG,
  // diag ecu or ecus msg id
  OBT_EVENT_ID_DIAG_MSG,
  // deinit, need clear all data
  OBT_EVENT_ID_DEINIT
};

/* obt task id */
enum OBT_TASK_ID {
  OBT_TASK_ID_NULL = 0,
  // update
  OBT_TASK_ID_UPDATE = 1,
  // session control 0x10
  OBT_TASK_ID_SESSION_CTRL,

  OBT_TASK_ID_UPDATE_PRE_CHECK,

  // routine ctrl  0x31
  OBT_TASK_ID_ROUTINE_CTRL,
  // routine ctrl  0x31, verify authenticity
  OBT_TASK_ID_ROUTINE_CTRL_VERIFY_AUTH,
  // routine ctrl  0x31, activate sbl
  OBT_TASK_ID_ROUTINE_CTRL_ACTIVATE_SBL,
  // routine ctrl  0x31, verify integrity
  OBT_TASK_ID_ROUTINE_CTRL_VERIFY_INTEGRITY,
  // routine ctrl  0x31, erase memory
  OBT_TASK_ID_ROUTINE_CTRL_ERASE_MEM,
  // security access  0x27
  OBT_TASK_ID_SECURITY_ACCESS,
  // security access get seed
  OBT_TASK_ID_SECURITY_ACCESS_GET_SEED,
  // security access send key
  OBT_TASK_ID_SECURITY_ACCESS_SEND_KEY,
  // ecu reset  0x11
  OBT_TASK_ID_ECU_RESET,
  // clear dtc  0x14
  OBT_TASK_ID_CLEAR_DTC,
  // read did 0x22
  OBT_TASK_ID_READ_DID,
  // read did 0x22, readd public key
  OBT_TASK_ID_READ_DID_PUB_KEY,
  // write did 0x2E
  OBT_TASK_ID_WRITE_DID,
  // tester present 0x3E
  OBT_TASK_ID_TESTER_PRESENT,
  // file transter  0x38
  OBT_TASK_ID_FILE_TRANSFER,
  // req downlooad  0x34
  OBT_TASK_ID_REQ_DOWNLOAD,
  // data transfer  0x36
  OBT_TASK_ID_DATA_TRANSFER,
  // req transfer exit  0x37
  OBT_TASK_ID_REQ_TRANSFER_EXIT,
  // req download  0x34/0x36/0x37
  OBT_TASK_ID_REQ_DOWNLOAD_34_36_37,
  // req func addr spec way
  OBT_TASK_ID_REQ_FUNC_ADDR_SPEC_WAY,
  // req send uds raw data
  OBT_TASK_ID_REQ_SEND_UDS_RAW_DATA
};

enum OBT_TASK_RET {
  OBT_TASK_RET_OK = 0,
  OBT_TASK_RET_FAILED,
  OBT_TASK_RET_TIMEOUT
};

//  test, update later, todo
struct obtReq {
  uint16_t dstAddr;
  uint8_t subServer;
  uint8_t addrType;
  bool needRsp;
  uint32_t key;
  uint16_t routineId;
  uint32_t dtcGroup;
  uint16_t did;
  list<string> files;

  obtReq()
      : dstAddr(0x0000),
        subServer(0x00),
        addrType(0x00),
        needRsp(false),
        key(0),
        routineId(0x0000),
        dtcGroup(0),
        did(0x0000) {}
};

#endif /* OBT_DEFINE_H_ */