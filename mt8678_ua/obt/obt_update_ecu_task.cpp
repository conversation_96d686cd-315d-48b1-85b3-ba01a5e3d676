#include "obt_update_ecu_task.h"

#include <arpa/inet.h>

#include <iomanip>
#include <iostream>
#include <thread>

#include "ota_doip_channel.h"
#include "ua_log.h"
#include "unity/file_util.h"

using seres::utils::FileUtils;
namespace seres {
namespace ua {

ObtUpdateEcuTask::ObtUpdateEcuTask(uint16_t addr)
    : fd_(-1),
      curr_addr(addr),
      curr_file_(""),
      blk_counter_(1),
      filePosition_(0),
      curr_filePosition_(0),
      max_blk_len_(4094) {
  curr_file_ = "/home/<USER>/ota_workspace/mt8678_ua/LEth_Debug(1).hex";
  hexFile_.open(curr_file_);
  if (!hexFile_.is_open()) {
    std::cerr << "Error: Unable to open file: " << curr_file_ << std::endl;
  }
  buffer.clear();
  currrnt_buffer_len_ = max_blk_len_;

}

ObtUpdateEcuTask::~ObtUpdateEcuTask() {
  if (hexFile_.is_open()) {
    hexFile_.close();
  }
}
void ObtUpdateEcuTask::Enter() {
  StdOutLogger::GetInstance().Info("ObtUpdateEcuTask enter");
  OtaDoipChannel::GetInstance().RegisterDataHandler(
      0x0064, [this](const OtaDataInfo& data) { RawDataRespCallback(data); });

  // int32_t openRet = seres::utils::FileUtils::openFile(fd_, curr_file_);
  OtaDoipChannel::GetInstance().Connect("127.0.0.1");
  // RequestToChangeSession(0x01);
  DataTransfer();
}

void ObtUpdateEcuTask::RequestToChangeSession(unsigned char expect_session) {
  std::vector<unsigned char> udsRequest = {0x10};
  udsRequest.push_back(expect_session);
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
  curr_task_ = OBT_TASK_ID_SESSION_CTRL;
}

void ObtUpdateEcuTask::RequestToChangeSessionHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_SESSION_CTRL &&
      (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x50)) {
    StdOutLogger::GetInstance().Info(
        "RequestToChangeSessionHandle 1002 5002 success");
    if (data.uds_resp[1] == 0x01) {
      RequestToChangeSession(0x03);
    } else if (data.uds_resp[1] == 0x02) {
      SecurityAccess();
    } else if (data.uds_resp[1] == 0x03) {
      CheckPreCondition();
    }

  } else {
    StdOutLogger::GetInstance().Info("RequestToChangeSessionHandle failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::CheckPreCondition() {
  std::vector<unsigned char> udsRequest = {0x31, 0x01, 0x02, 0x03};
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
  curr_task_ = OBT_TASK_ID_UPDATE_PRE_CHECK;
}
void ObtUpdateEcuTask::CheckPreConditionHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_UPDATE_PRE_CHECK &&
      (data.uds_resp.size() >= 3 && data.uds_resp[0] == 0x71)) {
    StdOutLogger::GetInstance().Info(
        "CheckPreConditionHandle x31, 0x01, 0x02, 0x03;resp 71 success");
    // 关闭DTC和通信控制需要使用功能寻址
    RequestToChangeSession(0x02);

  } else {
    StdOutLogger::GetInstance().Info("CheckPreConditionHandle failed");
    rst_callback_(curr_addr, 0);
  }
}
void ObtUpdateEcuTask::SecurityAccess() {
  std::vector<unsigned char> udsRequest = {0x27, 0x09};
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
  curr_task_ = OBT_TASK_ID_SECURITY_ACCESS;
}
void ObtUpdateEcuTask::SecurityAccessHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_SECURITY_ACCESS &&
      (data.uds_resp.size() >= 3 && data.uds_resp[0] == 0x67 &&
       data.uds_resp[0] == 0x09)) {
    StdOutLogger::GetInstance().Info(
        "SecurityAccess 0x27, 0x09;resp 67 success after send 27 0A");
    std::vector<unsigned char> udsRequest = {0x27, 0x0A, 0x01,
                                             0x02, 0x03, 0x04};
    OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);

  } else if (curr_task_ == OBT_TASK_ID_SECURITY_ACCESS &&
             (data.uds_resp.size() >= 3 && data.uds_resp[0] == 0x67 &&
              data.uds_resp[0] == 0x0A)) {
    StdOutLogger::GetInstance().Info(
        "SecurityAccess 0x27, 0x0A;resp 67 success after send 2E F1 84");
    SendSeed();
  } else {
    StdOutLogger::GetInstance().Info("SecurityAccess failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::SendSeed() {
  std::vector<unsigned char> udsRequest = {0x2E, 0xF1, 0x84};
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
  curr_task_ = OBT_TASK_ID_SECURITY_ACCESS_SEND_KEY;
}

void ObtUpdateEcuTask::SendSeedHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_SECURITY_ACCESS_SEND_KEY &&
      (data.uds_resp.size() >= 3 && data.uds_resp[0] == 0x32)) {
    StdOutLogger::GetInstance().Info(
        "SendSeedHandle0x2E, 0xF1, 0x84 ;resp success");
    // 根据是否是flashdriver判断是否需要先擦除再刷写，先测试flash dirver
    RequestDownload();
  } else {
    StdOutLogger::GetInstance().Info("CheckPreConditionHandle failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::EraseMemory() {}

void ObtUpdateEcuTask::EraseMemoryHandle(OtaDataInfo data) {}

void ObtUpdateEcuTask::RequestDownload() {
  std::vector<unsigned char> udsRequest = {0x34, 0x00, 0x44};
  uint64_t addr_data = 0;
  mem_addr_ = seres::utils::FileUtils::parseFirstLine(curr_file_);

  udsRequest.push_back(mem_addr_ >> 24 & 0xFF);
  udsRequest.push_back(mem_addr_ >> 16 & 0xFF);
  udsRequest.push_back(mem_addr_ >> 8 & 0xFF);
  udsRequest.push_back(mem_addr_ & 0xFF);

  uint32_t blk_data_len_ = 0x00010000;
  udsRequest.push_back(blk_data_len_ >> 24 & 0xFF);
  udsRequest.push_back(blk_data_len_ >> 16 & 0xFF);
  udsRequest.push_back(blk_data_len_ >> 8 & 0xFF);
  udsRequest.push_back(blk_data_len_ & 0xFF);

  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
  curr_task_ = OBT_TASK_ID_REQ_DOWNLOAD;
}
void ObtUpdateEcuTask::RequestDownloadHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_REQ_DOWNLOAD &&
      (data.uds_resp.size() >= 3 && data.uds_resp[0] == 0x74)) {
    StdOutLogger::GetInstance().Info("SendSeedHandle 0x34 resp success");
    uint8_t highByte = data.uds_resp[2];
    uint8_t lowByte = data.uds_resp[3];
    uint16_t maxData = (static_cast<uint16_t>(highByte) << 8) | lowByte;
    std::cout << "Max Data: 0x" << std::hex << maxData << std::endl;
    std::cout << "Max Bytes: " << std::dec << maxData << std::endl;
    max_blk_len_ = maxData - 2;
    DataTransfer();
  } else {
    StdOutLogger::GetInstance().Info("RequestDownloadHandle failed");
    rst_callback_(curr_addr, 0);
  }
}
void ObtUpdateEcuTask::DataTransfer() {
  std::vector<unsigned char> udsRequest = {0x36};
  udsRequest.push_back(blk_counter_);
  std::vector<unsigned char> data =
      parseHexFile(curr_file_, filePosition_, max_blk_len_);
  for (uint64_t i = 0; i < currrnt_buffer_len_; i++) {
    udsRequest.push_back(data[i]);
  }
  curr_task_ = OBT_TASK_ID_DATA_TRANSFER;
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
}

void ObtUpdateEcuTask::DataTransferHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_DATA_TRANSFER &&
      (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x76 &&
       data.uds_resp[1] == blk_counter_)) {
    StdOutLogger::GetInstance().Info("DataTransferHandle 0x36 resp success");

    if (currrnt_buffer_len_ < max_blk_len_) {
      TransferExit();
    } else {
      blk_counter_ = (blk_counter_ % 0xFF) + 1;
      filePosition_ = curr_filePosition_;
      DataTransfer();
    }
  } else {
    StdOutLogger::GetInstance().Info("DataTransferHandle failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::TransferExit() {
  std::vector<unsigned char> udsRequest = {0x37};
  curr_task_ = OBT_TASK_ID_REQ_TRANSFER_EXIT;
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
}

void ObtUpdateEcuTask::TransferExitHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_REQ_TRANSFER_EXIT &&
      (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x37)) {
    StdOutLogger::GetInstance().Info("TransferExitHandle , 0x37 ;resp success");
    VerifyAuth();
  } else {
    StdOutLogger::GetInstance().Info("TransferExitHandle failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::VerifyAuth() {
  std::vector<unsigned char> udsRequest = {0x31,0x01,0xDD,0x02};
  udsRequest.insert(udsRequest.end(), 128, 0x00);
  curr_task_ = OBT_TASK_ID_ROUTINE_CTRL_VERIFY_INTEGRITY;
  OtaDoipChannel::GetInstance().SendDataAsync(0x0E, udsRequest);
}

void ObtUpdateEcuTask::VerifyAuthHandle(OtaDataInfo data) {
  if (curr_task_ == OBT_TASK_ID_ROUTINE_CTRL_VERIFY_INTEGRITY &&
      (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x71 && data.uds_resp[1] == 0x01 && data.uds_resp[2] == 0xDD && data.uds_resp[3] == 0x02)) {
    StdOutLogger::GetInstance().Info("VerifyAuthHandle , 0x31,0x01,0xDD,0x02 ;resp success");
    //后处理
  } else {
    StdOutLogger::GetInstance().Info("VerifyAuthHandle failed");
    rst_callback_(curr_addr, 0);
  }
}

void ObtUpdateEcuTask::RawDataRespCallback(const OtaDataInfo& data) {
  std::thread::id threadId = std::this_thread::get_id();
  std::cout << "Thread ID (hex): " << std::hex << threadId << std::endl;

  if (data.uds_resp.empty()) {
    std::cerr << "Error: uds_resp is empty." << std::endl;
    return;
  }

  uint8_t sid = data.uds_resp[0];
  std::cout << "SID: 0x" << std::hex << static_cast<int>(sid) << std::endl;

  std::cout << "USD MSG: ";
  for (auto byte : data.uds_resp) {
    std::cout << "0x" << std::hex << std::setfill('0') << std::setw(2)
              << static_cast<int>(byte);
  }
  std::cout << std::endl;

  // 成功的情况
  switch (sid) {
    case 0x10 + 0x40:
      RequestToChangeSessionHandle(data);
      break;
    case 0x31 + 0x40:
      if (curr_task_ == OBT_TASK_ID_ROUTINE_CTRL_VERIFY_INTEGRITY) {
        VerifyAuthHandle(data);
      } else if (curr_task_ == OBT_TASK_ID_UPDATE_PRE_CHECK) {
        CheckPreConditionHandle(data);
      }
      break;
    case 0x27 + 0x40:
      SecurityAccessHandle(data);
      break;
    case 0x2E + 0x40:
      SendSeedHandle(data);
      break;
    case 0x34 + 0x40:
      RequestDownloadHandle(data);
      break;
    case 0x36 + 0x40:
      std::cout << "Success 76" << std::endl;
      DataTransferHandle(data);
      break;
    case 0x37 + 0x40:
      TransferExitHandle(data);
      break;

    case 0x7F:
      // 处理否定响应
      break;
    default:
      std::cout << "Unhandled SID: 0x" << std::hex << static_cast<int>(sid)
                << std::endl;
      break;
  }

  // 失败的情况
  if (data.uds_resp.size() > 0 && data.uds_resp[0] == 0x7F) {
    if (curr_task_ == OBT_TASK_ID_SESSION_CTRL &&
        (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
         data.uds_resp[1] == 0x10)) {
      RequestToChangeSessionHandle(data);
      return;
    } else if (curr_task_ == OBT_TASK_ID_UPDATE_PRE_CHECK &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x31)) {
      CheckPreConditionHandle(data);
    } else if (curr_task_ == OBT_TASK_ID_SECURITY_ACCESS &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x27)) {
      SecurityAccessHandle(data);
    } else if (curr_task_ == OBT_TASK_ID_SECURITY_ACCESS_SEND_KEY &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x2E)) {
      SendSeedHandle(data);
    } else if (curr_task_ == OBT_TASK_ID_REQ_DOWNLOAD &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x34)) {
      RequestDownloadHandle(data);
    } else if (curr_task_ == OBT_TASK_ID_DATA_TRANSFER &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x36)) {
      RequestDownloadHandle(data);
    } else if (curr_task_ == OBT_TASK_ID_REQ_TRANSFER_EXIT &&
               (data.uds_resp.size() >= 2 && data.uds_resp[0] == 0x7F &&
                data.uds_resp[1] == 0x37)) {
      TransferExitHandle(data);
    } else {
    }
  }
}

std::vector<unsigned char> ObtUpdateEcuTask::parseHexFile(
    const std::string& filePath, size_t filePosition, uint16_t maxBytes) {
  std::vector<unsigned char> data;
  // 如果缓冲区中有数据，优先处理缓冲区中的数据
  if (!buffer.empty()) {
    size_t bytesToAdd =
        std::min(maxBytes, static_cast<uint16_t>(buffer.size()));
    data.insert(data.end(), buffer.begin(), buffer.begin() + bytesToAdd);
    buffer.erase(buffer.begin(), buffer.begin() + bytesToAdd);

    // 如果缓冲区中的数据已经满足最大字节数，直接返回
    if (data.size() >= maxBytes) {
      return data;
    }
  }

  // 跳过文件开头到指定位置
  hexFile_.clear();
  hexFile_.seekg(filePosition);
  std::string line;

  while (std::getline(hexFile_, line)) {
    if (line.empty() || line[0] != ':') {
      continue;
    }

    size_t pos = 1;
    uint8_t length =
        seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
    pos += 2;

    uint16_t address =
        seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]) << 8;
    pos += 2;
    address |= seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
    pos += 2;

    uint8_t type = seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
    pos += 2;

    if (type == 0x04) {
      uint16_t extAddr =
          seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]) << 8;
      pos += 2;
      extAddr |= seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
      pos += 2;
    } else if (type == 0x00) {
      for (uint8_t i = 0; i < length; ++i) {
        if (data.size() < maxBytes) {
          uint8_t byte =
              seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
          data.push_back(byte); 
          pos += 2;
        } else {
          uint8_t buffer_byte =
              seres::utils::FileUtils::hexToByte(line[pos], line[pos + 1]);
          buffer.push_back(buffer_byte);
          pos += 2;
        }
      }
    } else if (type == 0x01) {
      currrnt_buffer_len_ = data.size();
      pos += 2;
      curr_filePosition_ = hexFile_.tellg();
      return data;
    } else if (type == 0x05) {
    }

    pos += 2;

    curr_filePosition_ = hexFile_.tellg();
    if (data.size() >= maxBytes) {
      return data;
    }
  }

  return data;
}

}  // namespace ua
}  // namespace seres